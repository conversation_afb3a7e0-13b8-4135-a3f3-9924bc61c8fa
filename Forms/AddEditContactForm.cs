using WhatsBroadcasterProDesktop.Services;
using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop.Forms
{
    public partial class AddEditContactForm : Form
    {
        private readonly IDatabaseService _databaseService;
        private readonly List<Group> _allGroups;
        private readonly Contact? _contactToEdit;

        // UI Controls
        private TextBox nameTextBox;
        private TextBox phoneTextBox;
        private TextBox emailTextBox;
        private TextBox notesTextBox;
        private CheckedListBox groupsCheckedListBox;
        private Button saveButton;
        private Button cancelButton;

        public AddEditContactForm(IDatabaseService databaseService, List<Group> allGroups, Contact? contactToEdit = null)
        {
            _databaseService = databaseService;
            _allGroups = allGroups;
            _contactToEdit = contactToEdit;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = _contactToEdit == null ? "إضافة رقم جديد" : "تعديل الرقم";
            this.Size = new Size(500, 600);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            var y = 20;
            var labelWidth = 100;
            var controlWidth = 300;
            var spacing = 40;

            // Name
            var nameLabel = new Label
            {
                Text = "الاسم:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nameTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23)
            };
            this.Controls.AddRange(new Control[] { nameLabel, nameTextBox });

            y += spacing;

            // Phone
            var phoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            phoneTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23),
                PlaceholderText = "مثال: +966501234567"
            };
            this.Controls.AddRange(new Control[] { phoneLabel, phoneTextBox });

            y += spacing;

            // Email
            var emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            emailTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23),
                PlaceholderText = "اختياري"
            };
            this.Controls.AddRange(new Control[] { emailLabel, emailTextBox });

            y += spacing;

            // Notes
            var notesLabel = new Label
            {
                Text = "ملاحظات:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            notesTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 80),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                PlaceholderText = "ملاحظات إضافية (اختياري)"
            };
            this.Controls.AddRange(new Control[] { notesLabel, notesTextBox });

            y += 100;

            // Groups
            var groupsLabel = new Label
            {
                Text = "المجموعات:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            groupsCheckedListBox = new CheckedListBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 120),
                CheckOnClick = true
            };
            this.Controls.AddRange(new Control[] { groupsLabel, groupsCheckedListBox });

            y += 140;

            // Buttons
            saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(130, y),
                Size = new Size(80, 30),
                BackColor = Color.Green,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(220, y),
                Size = new Size(80, 30),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += CancelButton_Click;

            this.Controls.AddRange(new Control[] { saveButton, cancelButton });

            // Set default button
            this.AcceptButton = saveButton;
            this.CancelButton = cancelButton;
        }

        private void LoadData()
        {
            // Load groups into checked list box
            foreach (var group in _allGroups)
            {
                groupsCheckedListBox.Items.Add(group.Name, false);
            }

            // If editing, populate fields
            if (_contactToEdit != null)
            {
                nameTextBox.Text = _contactToEdit.Name;
                phoneTextBox.Text = _contactToEdit.PhoneNumber;
                emailTextBox.Text = _contactToEdit.Email ?? "";
                notesTextBox.Text = _contactToEdit.Notes ?? "";

                // Check the groups this contact belongs to
                var contactGroupNames = _contactToEdit.ContactGroups.Select(cg => cg.Group.Name).ToList();
                for (int i = 0; i < groupsCheckedListBox.Items.Count; i++)
                {
                    var groupName = groupsCheckedListBox.Items[i].ToString();
                    if (contactGroupNames.Contains(groupName))
                    {
                        groupsCheckedListBox.SetItemChecked(i, true);
                    }
                }
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                saveButton.Enabled = false;
                saveButton.Text = "جاري الحفظ...";

                Contact contact;
                if (_contactToEdit == null)
                {
                    // Create new contact
                    contact = new Contact
                    {
                        Name = nameTextBox.Text.Trim(),
                        PhoneNumber = phoneTextBox.Text.Trim(),
                        Email = string.IsNullOrWhiteSpace(emailTextBox.Text) ? null : emailTextBox.Text.Trim(),
                        Notes = string.IsNullOrWhiteSpace(notesTextBox.Text) ? null : notesTextBox.Text.Trim()
                    };

                    contact = await _databaseService.AddContactAsync(contact);
                }
                else
                {
                    // Update existing contact
                    contact = _contactToEdit;
                    contact.Name = nameTextBox.Text.Trim();
                    contact.PhoneNumber = phoneTextBox.Text.Trim();
                    contact.Email = string.IsNullOrWhiteSpace(emailTextBox.Text) ? null : emailTextBox.Text.Trim();
                    contact.Notes = string.IsNullOrWhiteSpace(notesTextBox.Text) ? null : notesTextBox.Text.Trim();

                    contact = await _databaseService.UpdateContactAsync(contact);
                }

                // Update group memberships
                await UpdateGroupMemberships(contact);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الرقم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                saveButton.Enabled = true;
                saveButton.Text = "حفظ";
            }
        }

        private async Task UpdateGroupMemberships(Contact contact)
        {
            // Get selected groups
            var selectedGroupNames = new List<string>();
            for (int i = 0; i < groupsCheckedListBox.Items.Count; i++)
            {
                if (groupsCheckedListBox.GetItemChecked(i))
                {
                    selectedGroupNames.Add(groupsCheckedListBox.Items[i].ToString()!);
                }
            }

            // Remove from all groups first (if editing)
            if (_contactToEdit != null)
            {
                foreach (var group in _allGroups)
                {
                    await _databaseService.RemoveContactFromGroupAsync(contact.Id, group.Id);
                }
            }

            // Add to selected groups
            foreach (var groupName in selectedGroupNames)
            {
                var group = _allGroups.First(g => g.Name == groupName);
                await _databaseService.AddContactToGroupAsync(contact.Id, group.Id);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم", "خطأ في الإدخال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(phoneTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "خطأ في الإدخال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                phoneTextBox.Focus();
                return false;
            }

            // Basic phone number validation
            var phone = phoneTextBox.Text.Trim();
            if (!phone.StartsWith("+") && !phone.All(char.IsDigit))
            {
                MessageBox.Show("رقم الهاتف غير صحيح. يجب أن يبدأ بـ + أو يحتوي على أرقام فقط", "خطأ في الإدخال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                phoneTextBox.Focus();
                return false;
            }

            // Email validation (if provided)
            if (!string.IsNullOrWhiteSpace(emailTextBox.Text))
            {
                var email = emailTextBox.Text.Trim();
                if (!email.Contains("@") || !email.Contains("."))
                {
                    MessageBox.Show("البريد الإلكتروني غير صحيح", "خطأ في الإدخال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    emailTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
