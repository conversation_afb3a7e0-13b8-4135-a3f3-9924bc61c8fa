using WhatsBroadcasterProDesktop.Services;

namespace WhatsBroadcasterProDesktop.Forms
{
    public partial class MainForm : Form
    {
        private readonly IDatabaseService _databaseService;
        private readonly IWhatsAppService _whatsAppService;
        private readonly IServiceProvider _serviceProvider;

        // UI Controls
        private MenuStrip menuStrip;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private ToolStripStatusLabel connectionStatusLabel;
        private Panel mainPanel;
        private TableLayoutPanel dashboardPanel;

        // Dashboard Cards
        private Panel contactsCard;
        private Panel groupsCard;
        private Panel broadcastCard;
        private Panel connectionCard;

        // Labels for counts
        private Label contactsCountLabel;
        private Label groupsCountLabel;
        private Label connectionStatusTextLabel;

        public MainForm(IDatabaseService databaseService, IWhatsAppService whatsAppService, IServiceProvider serviceProvider)
        {
            _databaseService = databaseService;
            _whatsAppService = whatsAppService;
            _serviceProvider = serviceProvider;
            
            InitializeComponent();
            SetupEventHandlers();
            LoadDashboardData();
        }

        private void InitializeComponent()
        {
            this.Text = "WhatsBroadcaster Pro - إدارة رسائل الواتساب الجماعية";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Icon = SystemIcons.Application;

            // Create menu strip
            CreateMenuStrip();

            // Create status strip
            CreateStatusStrip();

            // Create main panel
            CreateMainPanel();

            // Create dashboard
            CreateDashboard();
        }

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip();

            // File menu
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.Add("خروج", null, (s, e) => Application.Exit());

            // Contacts menu
            var contactsMenu = new ToolStripMenuItem("الأرقام");
            contactsMenu.DropDownItems.Add("إدارة الأرقام", null, OpenContactsForm);
            contactsMenu.DropDownItems.Add("إضافة رقم جديد", null, AddNewContact);
            contactsMenu.DropDownItems.Add("استيراد من ملف", null, ImportContacts);

            // Groups menu
            var groupsMenu = new ToolStripMenuItem("المجموعات");
            groupsMenu.DropDownItems.Add("إدارة المجموعات", null, OpenGroupsForm);
            groupsMenu.DropDownItems.Add("إضافة مجموعة جديدة", null, AddNewGroup);

            // Broadcast menu
            var broadcastMenu = new ToolStripMenuItem("الإرسال");
            broadcastMenu.DropDownItems.Add("إرسال رسالة جماعية", null, OpenBroadcastForm);
            broadcastMenu.DropDownItems.Add("سجل الرسائل", null, ViewMessageHistory);

            // WhatsApp menu
            var whatsappMenu = new ToolStripMenuItem("واتساب");
            whatsappMenu.DropDownItems.Add("الاتصال", null, ConnectToWhatsApp);
            whatsappMenu.DropDownItems.Add("قطع الاتصال", null, DisconnectFromWhatsApp);

            // Help menu
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.Add("حول البرنامج", null, ShowAbout);

            menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, contactsMenu, groupsMenu, broadcastMenu, whatsappMenu, helpMenu });
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusLabel = new ToolStripStatusLabel("جاهز");
            connectionStatusLabel = new ToolStripStatusLabel("غير متصل بواتساب");
            connectionStatusLabel.ForeColor = Color.Red;

            statusStrip.Items.AddRange(new ToolStripItem[] { statusLabel, new ToolStripStatusLabel() { Spring = true }, connectionStatusLabel });
            this.Controls.Add(statusStrip);
        }

        private void CreateMainPanel()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };
            this.Controls.Add(mainPanel);
        }

        private void CreateDashboard()
        {
            // Title
            var titleLabel = new Label
            {
                Text = "لوحة التحكم الرئيسية",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                AutoSize = true,
                Location = new Point(0, 0)
            };
            mainPanel.Controls.Add(titleLabel);

            // Dashboard panel
            dashboardPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 2,
                Location = new Point(0, 50),
                Size = new Size(mainPanel.Width - 40, 400),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            dashboardPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            dashboardPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            dashboardPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));
            dashboardPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 50F));

            // Create dashboard cards
            CreateContactsCard();
            CreateGroupsCard();
            CreateBroadcastCard();
            CreateConnectionCard();

            // Add cards to dashboard
            dashboardPanel.Controls.Add(contactsCard, 0, 0);
            dashboardPanel.Controls.Add(groupsCard, 1, 0);
            dashboardPanel.Controls.Add(broadcastCard, 0, 1);
            dashboardPanel.Controls.Add(connectionCard, 1, 1);

            mainPanel.Controls.Add(dashboardPanel);
        }

        private void CreateContactsCard()
        {
            contactsCard = CreateDashboardCard("👥 إدارة الأرقام", Color.LightBlue, Color.DodgerBlue);
            contactsCountLabel = new Label
            {
                Text = "0 رقم محفوظ",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.DarkBlue,
                Location = new Point(20, 60),
                AutoSize = true
            };
            contactsCard.Controls.Add(contactsCountLabel);

            var contactsButton = new Button
            {
                Text = "إدارة الأرقام",
                Size = new Size(120, 35),
                Location = new Point(20, 100),
                BackColor = Color.DodgerBlue,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            contactsButton.Click += OpenContactsForm;
            contactsCard.Controls.Add(contactsButton);
        }

        private void CreateGroupsCard()
        {
            groupsCard = CreateDashboardCard("📁 إدارة المجموعات", Color.LightGreen, Color.Green);
            groupsCountLabel = new Label
            {
                Text = "0 مجموعة",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.DarkGreen,
                Location = new Point(20, 60),
                AutoSize = true
            };
            groupsCard.Controls.Add(groupsCountLabel);

            var groupsButton = new Button
            {
                Text = "إدارة المجموعات",
                Size = new Size(120, 35),
                Location = new Point(20, 100),
                BackColor = Color.Green,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            groupsButton.Click += OpenGroupsForm;
            groupsCard.Controls.Add(groupsButton);
        }

        private void CreateBroadcastCard()
        {
            broadcastCard = CreateDashboardCard("📢 الإرسال الجماعي", Color.LightCoral, Color.Red);
            var broadcastLabel = new Label
            {
                Text = "إرسال رسائل للمجموعات",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.DarkRed,
                Location = new Point(20, 60),
                AutoSize = true
            };
            broadcastCard.Controls.Add(broadcastLabel);

            var broadcastButton = new Button
            {
                Text = "إرسال رسائل",
                Size = new Size(120, 35),
                Location = new Point(20, 100),
                BackColor = Color.Red,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            broadcastButton.Click += OpenBroadcastForm;
            broadcastCard.Controls.Add(broadcastButton);
        }

        private void CreateConnectionCard()
        {
            connectionCard = CreateDashboardCard("🔗 اتصال واتساب", Color.LightYellow, Color.Orange);
            connectionStatusTextLabel = new Label
            {
                Text = "غير متصل",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.Red,
                Location = new Point(20, 60),
                AutoSize = true
            };
            connectionCard.Controls.Add(connectionStatusTextLabel);

            var connectionButton = new Button
            {
                Text = "الاتصال بواتساب",
                Size = new Size(120, 35),
                Location = new Point(20, 100),
                BackColor = Color.Orange,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            connectionButton.Click += ConnectToWhatsApp;
            connectionCard.Controls.Add(connectionButton);
        }

        private Panel CreateDashboardCard(string title, Color backgroundColor, Color borderColor)
        {
            var card = new Panel
            {
                Size = new Size(200, 150),
                BackColor = backgroundColor,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(10)
            };

            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = borderColor,
                Location = new Point(20, 20),
                AutoSize = true
            };
            card.Controls.Add(titleLabel);

            return card;
        }

        private void SetupEventHandlers()
        {
            _whatsAppService.ConnectionStatusChanged += OnConnectionStatusChanged;
            this.Load += MainForm_Load;
        }

        private async void MainForm_Load(object sender, EventArgs e)
        {
            await LoadDashboardData();
        }

        private async Task LoadDashboardData()
        {
            try
            {
                statusLabel.Text = "جاري تحميل البيانات...";

                var contacts = await _databaseService.GetContactsAsync();
                contactsCountLabel.Text = $"{contacts.Count} رقم محفوظ";

                var groups = await _databaseService.GetGroupsAsync();
                groupsCountLabel.Text = $"{groups.Count} مجموعة";

                UpdateConnectionStatus();

                statusLabel.Text = "جاهز";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "خطأ في التحميل";
            }
        }

        private void UpdateConnectionStatus()
        {
            if (_whatsAppService.IsConnected)
            {
                connectionStatusTextLabel.Text = "متصل";
                connectionStatusTextLabel.ForeColor = Color.Green;
                connectionStatusLabel.Text = "متصل بواتساب";
                connectionStatusLabel.ForeColor = Color.Green;
            }
            else
            {
                connectionStatusTextLabel.Text = "غير متصل";
                connectionStatusTextLabel.ForeColor = Color.Red;
                connectionStatusLabel.Text = "غير متصل بواتساب";
                connectionStatusLabel.ForeColor = Color.Red;
            }
        }

        private void OnConnectionStatusChanged(object? sender, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnConnectionStatusChanged(sender, status)));
                return;
            }

            connectionStatusLabel.Text = status;
            UpdateConnectionStatus();
        }

        // Event handlers for menu items and buttons
        private void OpenContactsForm(object? sender, EventArgs e)
        {
            var contactsForm = _serviceProvider.GetRequiredService<ContactsForm>();
            contactsForm.ShowDialog();
            _ = LoadDashboardData(); // Refresh data
        }

        private void OpenGroupsForm(object? sender, EventArgs e)
        {
            var groupsForm = _serviceProvider.GetRequiredService<GroupsForm>();
            groupsForm.ShowDialog();
            _ = LoadDashboardData(); // Refresh data
        }

        private void OpenBroadcastForm(object? sender, EventArgs e)
        {
            var broadcastForm = _serviceProvider.GetRequiredService<BroadcastForm>();
            broadcastForm.ShowDialog();
        }

        private void AddNewContact(object? sender, EventArgs e)
        {
            // TODO: Implement add contact dialog
            MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void AddNewGroup(object? sender, EventArgs e)
        {
            // TODO: Implement add group dialog
            MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ImportContacts(object? sender, EventArgs e)
        {
            // TODO: Implement import contacts
            MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ViewMessageHistory(object? sender, EventArgs e)
        {
            // TODO: Implement message history
            MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void ConnectToWhatsApp(object? sender, EventArgs e)
        {
            try
            {
                statusLabel.Text = "جاري الاتصال بواتساب...";
                await _whatsAppService.ConnectAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الاتصال: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                statusLabel.Text = "جاهز";
            }
        }

        private async void DisconnectFromWhatsApp(object? sender, EventArgs e)
        {
            try
            {
                await _whatsAppService.DisconnectAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في قطع الاتصال: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowAbout(object? sender, EventArgs e)
        {
            MessageBox.Show("WhatsBroadcaster Pro v1.0\nتطبيق إدارة رسائل الواتساب الجماعية\n\n© 2024", "حول البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            _whatsAppService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            base.OnFormClosed(e);
        }
    }
}
