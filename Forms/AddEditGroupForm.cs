using WhatsBroadcasterProDesktop.Services;
using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop.Forms
{
    public partial class AddEditGroupForm : Form
    {
        private readonly IDatabaseService _databaseService;
        private readonly Group? _groupToEdit;

        // UI Controls
        private TextBox nameTextBox;
        private TextBox descriptionTextBox;
        private Panel colorPanel;
        private Button colorButton;
        private Button saveButton;
        private Button cancelButton;

        private string selectedColor = "#007ACC";

        public AddEditGroupForm(IDatabaseService databaseService, Group? groupToEdit = null)
        {
            _databaseService = databaseService;
            _groupToEdit = groupToEdit;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = _groupToEdit == null ? "إضافة مجموعة جديدة" : "تعديل المجموعة";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            CreateControls();
        }

        private void CreateControls()
        {
            var y = 20;
            var labelWidth = 100;
            var controlWidth = 280;
            var spacing = 50;

            // Name
            var nameLabel = new Label
            {
                Text = "اسم المجموعة:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            nameTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 23)
            };
            this.Controls.AddRange(new Control[] { nameLabel, nameTextBox });

            y += spacing;

            // Description
            var descriptionLabel = new Label
            {
                Text = "الوصف:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            descriptionTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(controlWidth, 80),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                PlaceholderText = "وصف المجموعة (اختياري)"
            };
            this.Controls.AddRange(new Control[] { descriptionLabel, descriptionTextBox });

            y += 100;

            // Color
            var colorLabel = new Label
            {
                Text = "اللون:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };

            colorPanel = new Panel
            {
                Location = new Point(130, y),
                Size = new Size(30, 23),
                BackColor = ColorTranslator.FromHtml(selectedColor),
                BorderStyle = BorderStyle.FixedSingle
            };

            colorButton = new Button
            {
                Text = "اختيار اللون",
                Location = new Point(170, y),
                Size = new Size(100, 25)
            };
            colorButton.Click += ColorButton_Click;

            this.Controls.AddRange(new Control[] { colorLabel, colorPanel, colorButton });

            y += spacing;

            // Buttons
            saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(130, y),
                Size = new Size(80, 30),
                BackColor = Color.Green,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            saveButton.Click += SaveButton_Click;

            cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(220, y),
                Size = new Size(80, 30),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            cancelButton.Click += CancelButton_Click;

            this.Controls.AddRange(new Control[] { saveButton, cancelButton });

            // Set default button
            this.AcceptButton = saveButton;
            this.CancelButton = cancelButton;
        }

        private void LoadData()
        {
            if (_groupToEdit != null)
            {
                nameTextBox.Text = _groupToEdit.Name;
                descriptionTextBox.Text = _groupToEdit.Description ?? "";
                selectedColor = _groupToEdit.Color;
                colorPanel.BackColor = ColorTranslator.FromHtml(selectedColor);
            }
        }

        private void ColorButton_Click(object? sender, EventArgs e)
        {
            using (var colorDialog = new ColorDialog())
            {
                colorDialog.Color = ColorTranslator.FromHtml(selectedColor);
                colorDialog.FullOpen = true;

                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    selectedColor = ColorTranslator.ToHtml(colorDialog.Color);
                    colorPanel.BackColor = colorDialog.Color;
                }
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                saveButton.Enabled = false;
                saveButton.Text = "جاري الحفظ...";

                if (_groupToEdit == null)
                {
                    // Create new group
                    var group = new Group
                    {
                        Name = nameTextBox.Text.Trim(),
                        Description = string.IsNullOrWhiteSpace(descriptionTextBox.Text) ? null : descriptionTextBox.Text.Trim(),
                        Color = selectedColor
                    };

                    await _databaseService.AddGroupAsync(group);
                }
                else
                {
                    // Update existing group
                    _groupToEdit.Name = nameTextBox.Text.Trim();
                    _groupToEdit.Description = string.IsNullOrWhiteSpace(descriptionTextBox.Text) ? null : descriptionTextBox.Text.Trim();
                    _groupToEdit.Color = selectedColor;

                    await _databaseService.UpdateGroupAsync(_groupToEdit);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المجموعة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                saveButton.Enabled = true;
                saveButton.Text = "حفظ";
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(nameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المجموعة", "خطأ في الإدخال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }

            return true;
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
