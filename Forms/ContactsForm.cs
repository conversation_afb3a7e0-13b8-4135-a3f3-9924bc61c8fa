using WhatsBroadcasterProDesktop.Services;
using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop.Forms
{
    public partial class ContactsForm : Form
    {
        private readonly IDatabaseService _databaseService;

        // UI Controls
        private ToolStrip toolStrip;
        private DataGridView contactsDataGridView;
        private TextBox searchTextBox;
        private ComboBox groupFilterComboBox;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;

        private List<Contact> allContacts = new();
        private List<Group> allGroups = new();

        public ContactsForm(IDatabaseService databaseService)
        {
            _databaseService = databaseService;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "إدارة أرقام الواتساب";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;

            CreateToolStrip();
            CreateSearchPanel();
            CreateDataGridView();
            CreateStatusStrip();
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip();

            var addButton = new ToolStripButton("إضافة رقم جديد");
            addButton.Image = SystemIcons.Information.ToBitmap();
            addButton.Click += AddContact_Click;

            var editButton = new ToolStripButton("تعديل");
            editButton.Click += EditContact_Click;

            var deleteButton = new ToolStripButton("حذف");
            deleteButton.Click += DeleteContact_Click;

            var separator1 = new ToolStripSeparator();

            var importButton = new ToolStripButton("استيراد من ملف");
            importButton.Click += ImportContacts_Click;

            var exportButton = new ToolStripButton("تصدير إلى ملف");
            exportButton.Click += ExportContacts_Click;

            toolStrip.Items.AddRange(new ToolStripItem[] { addButton, editButton, deleteButton, separator1, importButton, exportButton });
            this.Controls.Add(toolStrip);
        }

        private void CreateSearchPanel()
        {
            var searchPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Top,
                Padding = new Padding(10)
            };

            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(10, 15),
                AutoSize = true
            };

            searchTextBox = new TextBox
            {
                Location = new Point(60, 12),
                Width = 200,
                PlaceholderText = "ابحث في الأسماء أو الأرقام..."
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            var filterLabel = new Label
            {
                Text = "المجموعة:",
                Location = new Point(280, 15),
                AutoSize = true
            };

            groupFilterComboBox = new ComboBox
            {
                Location = new Point(340, 12),
                Width = 150,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            groupFilterComboBox.SelectedIndexChanged += GroupFilter_SelectedIndexChanged;

            searchPanel.Controls.AddRange(new Control[] { searchLabel, searchTextBox, filterLabel, groupFilterComboBox });
            this.Controls.Add(searchPanel);
        }

        private void CreateDataGridView()
        {
            contactsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true
            };

            // Add columns
            contactsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            contactsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "الاسم",
                DataPropertyName = "Name",
                Width = 200
            });

            contactsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "PhoneNumber",
                HeaderText = "رقم الهاتف",
                DataPropertyName = "PhoneNumber",
                Width = 150
            });

            contactsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 200
            });

            contactsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Groups",
                HeaderText = "المجموعات",
                Width = 200
            });

            contactsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                HeaderText = "تاريخ الإضافة",
                DataPropertyName = "CreatedAt",
                Width = 150
            });

            // Handle double click for editing
            contactsDataGridView.CellDoubleClick += ContactsDataGridView_CellDoubleClick;

            this.Controls.Add(contactsDataGridView);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusLabel = new ToolStripStatusLabel("جاهز");
            statusStrip.Items.Add(statusLabel);
            this.Controls.Add(statusStrip);
        }

        private async void LoadData()
        {
            try
            {
                statusLabel.Text = "جاري تحميل البيانات...";

                // Load contacts
                allContacts = await _databaseService.GetContactsAsync();

                // Load groups for filter
                allGroups = await _databaseService.GetGroupsAsync();
                groupFilterComboBox.Items.Clear();
                groupFilterComboBox.Items.Add("جميع المجموعات");
                foreach (var group in allGroups)
                {
                    groupFilterComboBox.Items.Add(group.Name);
                }
                groupFilterComboBox.SelectedIndex = 0;

                RefreshContactsDisplay();
                statusLabel.Text = $"تم تحميل {allContacts.Count} رقم";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "خطأ في التحميل";
            }
        }

        private void RefreshContactsDisplay()
        {
            var filteredContacts = FilterContacts();
            
            // Clear existing rows
            contactsDataGridView.Rows.Clear();

            // Add filtered contacts
            foreach (var contact in filteredContacts)
            {
                var groupNames = string.Join(", ", contact.ContactGroups.Select(cg => cg.Group.Name));
                var row = new DataGridViewRow();
                row.CreateCells(contactsDataGridView);
                row.Cells[0].Value = contact.Id;
                row.Cells[1].Value = contact.Name;
                row.Cells[2].Value = contact.PhoneNumber;
                row.Cells[3].Value = contact.Email ?? "";
                row.Cells[4].Value = groupNames;
                row.Cells[5].Value = contact.CreatedAt.ToString("yyyy/MM/dd");
                row.Tag = contact;
                contactsDataGridView.Rows.Add(row);
            }

            statusLabel.Text = $"عرض {filteredContacts.Count} من {allContacts.Count} رقم";
        }

        private List<Contact> FilterContacts()
        {
            var filtered = allContacts.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(searchTextBox.Text))
            {
                var searchTerm = searchTextBox.Text.ToLower();
                filtered = filtered.Where(c => 
                    c.Name.ToLower().Contains(searchTerm) ||
                    c.PhoneNumber.Contains(searchTerm) ||
                    (c.Email?.ToLower().Contains(searchTerm) ?? false));
            }

            // Apply group filter
            if (groupFilterComboBox.SelectedIndex > 0)
            {
                var selectedGroupName = groupFilterComboBox.SelectedItem.ToString();
                filtered = filtered.Where(c => 
                    c.ContactGroups.Any(cg => cg.Group.Name == selectedGroupName));
            }

            return filtered.ToList();
        }

        private void SearchTextBox_TextChanged(object? sender, EventArgs e)
        {
            RefreshContactsDisplay();
        }

        private void GroupFilter_SelectedIndexChanged(object? sender, EventArgs e)
        {
            RefreshContactsDisplay();
        }

        private void AddContact_Click(object? sender, EventArgs e)
        {
            var addContactForm = new AddEditContactForm(_databaseService, allGroups);
            if (addContactForm.ShowDialog() == DialogResult.OK)
            {
                LoadData(); // Refresh the list
            }
        }

        private void EditContact_Click(object? sender, EventArgs e)
        {
            if (contactsDataGridView.SelectedRows.Count == 1)
            {
                var selectedContact = (Contact)contactsDataGridView.SelectedRows[0].Tag;
                var editContactForm = new AddEditContactForm(_databaseService, allGroups, selectedContact);
                if (editContactForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData(); // Refresh the list
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار رقم واحد للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void DeleteContact_Click(object? sender, EventArgs e)
        {
            if (contactsDataGridView.SelectedRows.Count > 0)
            {
                var selectedContacts = contactsDataGridView.SelectedRows.Cast<DataGridViewRow>()
                    .Select(row => (Contact)row.Tag).ToList();

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف {selectedContacts.Count} رقم؟",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        foreach (var contact in selectedContacts)
                        {
                            await _databaseService.DeleteContactAsync(contact.Id);
                        }
                        LoadData(); // Refresh the list
                        MessageBox.Show("تم حذف الأرقام بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الأرقام: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار رقم أو أكثر للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void ContactsDataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditContact_Click(sender, e);
            }
        }

        private void ImportContacts_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ExportContacts_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم تطوير هذه الميزة قريباً", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
