using WhatsBroadcasterProDesktop.Services;
using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop.Forms
{
    public partial class GroupsForm : Form
    {
        private readonly IDatabaseService _databaseService;

        // UI Controls
        private ToolStrip toolStrip;
        private DataGridView groupsDataGridView;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;

        private List<Group> allGroups = new();

        public GroupsForm(IDatabaseService databaseService)
        {
            _databaseService = databaseService;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "إدارة مجموعات الأرقام";
            this.Size = new Size(800, 500);
            this.StartPosition = FormStartPosition.CenterParent;

            CreateToolStrip();
            CreateDataGridView();
            CreateStatusStrip();
        }

        private void CreateToolStrip()
        {
            toolStrip = new ToolStrip();

            var addButton = new ToolStripButton("إضافة مجموعة جديدة");
            addButton.Click += AddGroup_Click;

            var editButton = new ToolStripButton("تعديل");
            editButton.Click += EditGroup_Click;

            var deleteButton = new ToolStripButton("حذف");
            deleteButton.Click += DeleteGroup_Click;

            var separator = new ToolStripSeparator();

            var viewContactsButton = new ToolStripButton("عرض الأرقام");
            viewContactsButton.Click += ViewContacts_Click;

            toolStrip.Items.AddRange(new ToolStripItem[] { addButton, editButton, deleteButton, separator, viewContactsButton });
            this.Controls.Add(toolStrip);
        }

        private void CreateDataGridView()
        {
            groupsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoGenerateColumns = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true
            };

            // Add columns
            groupsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Id",
                HeaderText = "المعرف",
                DataPropertyName = "Id",
                Visible = false
            });

            groupsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Name",
                HeaderText = "اسم المجموعة",
                DataPropertyName = "Name",
                Width = 200
            });

            groupsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "الوصف",
                DataPropertyName = "Description",
                Width = 300
            });

            groupsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "ContactCount",
                HeaderText = "عدد الأرقام",
                Width = 100
            });

            groupsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedAt",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedAt",
                Width = 150
            });

            groupsDataGridView.CellDoubleClick += GroupsDataGridView_CellDoubleClick;

            this.Controls.Add(groupsDataGridView);
        }

        private void CreateStatusStrip()
        {
            statusStrip = new StatusStrip();
            statusLabel = new ToolStripStatusLabel("جاهز");
            statusStrip.Items.Add(statusLabel);
            this.Controls.Add(statusStrip);
        }

        private async void LoadData()
        {
            try
            {
                statusLabel.Text = "جاري تحميل البيانات...";

                allGroups = await _databaseService.GetGroupsAsync();
                RefreshGroupsDisplay();

                statusLabel.Text = $"تم تحميل {allGroups.Count} مجموعة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "خطأ في التحميل";
            }
        }

        private void RefreshGroupsDisplay()
        {
            groupsDataGridView.Rows.Clear();

            foreach (var group in allGroups)
            {
                var row = new DataGridViewRow();
                row.CreateCells(groupsDataGridView);
                row.Cells[0].Value = group.Id;
                row.Cells[1].Value = group.Name;
                row.Cells[2].Value = group.Description ?? "";
                row.Cells[3].Value = group.ContactGroups.Count;
                row.Cells[4].Value = group.CreatedAt.ToString("yyyy/MM/dd");
                row.Tag = group;
                groupsDataGridView.Rows.Add(row);
            }
        }

        private void AddGroup_Click(object? sender, EventArgs e)
        {
            var addGroupForm = new AddEditGroupForm(_databaseService);
            if (addGroupForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }

        private void EditGroup_Click(object? sender, EventArgs e)
        {
            if (groupsDataGridView.SelectedRows.Count == 1)
            {
                var selectedGroup = (Group)groupsDataGridView.SelectedRows[0].Tag;
                var editGroupForm = new AddEditGroupForm(_databaseService, selectedGroup);
                if (editGroupForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مجموعة واحدة للتعديل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void DeleteGroup_Click(object? sender, EventArgs e)
        {
            if (groupsDataGridView.SelectedRows.Count == 1)
            {
                var selectedGroup = (Group)groupsDataGridView.SelectedRows[0].Tag;
                
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف مجموعة '{selectedGroup.Name}'؟\nسيتم إزالة جميع الأرقام من هذه المجموعة.",
                    "تأكيد الحذف",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _databaseService.DeleteGroupAsync(selectedGroup.Id);
                        LoadData();
                        MessageBox.Show("تم حذف المجموعة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المجموعة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مجموعة للحذف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void ViewContacts_Click(object? sender, EventArgs e)
        {
            if (groupsDataGridView.SelectedRows.Count == 1)
            {
                var selectedGroup = (Group)groupsDataGridView.SelectedRows[0].Tag;
                MessageBox.Show($"عرض أرقام مجموعة '{selectedGroup.Name}'\nسيتم تطوير هذه الميزة قريباً", "قريباً", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("يرجى اختيار مجموعة لعرض أرقامها", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void GroupsDataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditGroup_Click(sender, e);
            }
        }
    }
}
