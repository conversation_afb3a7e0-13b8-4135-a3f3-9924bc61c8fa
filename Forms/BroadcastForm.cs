using WhatsBroadcasterProDesktop.Services;
using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop.Forms
{
    public partial class BroadcastForm : Form
    {
        private readonly IDatabaseService _databaseService;
        private readonly IWhatsAppService _whatsAppService;

        // UI Controls
        private ComboBox groupComboBox;
        private TextBox messageTextBox;
        private Label characterCountLabel;
        private Label selectedGroupInfoLabel;
        private Button sendButton;
        private Button previewButton;
        private ProgressBar sendProgressBar;
        private ListBox statusListBox;
        private Panel progressPanel;

        private List<Group> allGroups = new();
        private Group? selectedGroup;

        public BroadcastForm(IDatabaseService databaseService, IWhatsAppService whatsAppService)
        {
            _databaseService = databaseService;
            _whatsAppService = whatsAppService;

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = "إرسال رسائل جماعية";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterParent;

            CreateControls();
        }

        private void CreateControls()
        {
            var y = 20;

            // Group selection
            var groupLabel = new Label
            {
                Text = "اختيار المجموعة:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };

            groupComboBox = new ComboBox
            {
                Location = new Point(130, y),
                Size = new Size(300, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            groupComboBox.SelectedIndexChanged += GroupComboBox_SelectedIndexChanged;

            selectedGroupInfoLabel = new Label
            {
                Text = "لم يتم اختيار مجموعة",
                Location = new Point(450, y),
                Size = new Size(200, 23),
                ForeColor = Color.Gray
            };

            this.Controls.AddRange(new Control[] { groupLabel, groupComboBox, selectedGroupInfoLabel });

            y += 50;

            // Message composition
            var messageLabel = new Label
            {
                Text = "نص الرسالة:",
                Location = new Point(20, y),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };

            messageTextBox = new TextBox
            {
                Location = new Point(130, y),
                Size = new Size(500, 150),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                PlaceholderText = "اكتب رسالتك هنا..."
            };
            messageTextBox.TextChanged += MessageTextBox_TextChanged;

            characterCountLabel = new Label
            {
                Text = "0 حرف",
                Location = new Point(550, y + 155),
                Size = new Size(80, 20),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.Gray
            };

            this.Controls.AddRange(new Control[] { messageLabel, messageTextBox, characterCountLabel });

            y += 200;

            // Action buttons
            previewButton = new Button
            {
                Text = "معاينة",
                Location = new Point(130, y),
                Size = new Size(100, 35),
                BackColor = Color.Blue,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            previewButton.Click += PreviewButton_Click;

            sendButton = new Button
            {
                Text = "إرسال",
                Location = new Point(240, y),
                Size = new Size(100, 35),
                BackColor = Color.Red,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            sendButton.Click += SendButton_Click;

            this.Controls.AddRange(new Control[] { previewButton, sendButton });

            y += 60;

            // Progress panel
            progressPanel = new Panel
            {
                Location = new Point(20, y),
                Size = new Size(640, 200),
                BorderStyle = BorderStyle.FixedSingle,
                Visible = false
            };

            var progressLabel = new Label
            {
                Text = "حالة الإرسال:",
                Location = new Point(10, 10),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            sendProgressBar = new ProgressBar
            {
                Location = new Point(10, 35),
                Size = new Size(620, 25)
            };

            statusListBox = new ListBox
            {
                Location = new Point(10, 70),
                Size = new Size(620, 120)
            };

            progressPanel.Controls.AddRange(new Control[] { progressLabel, sendProgressBar, statusListBox });
            this.Controls.Add(progressPanel);
        }

        private async void LoadData()
        {
            try
            {
                allGroups = await _databaseService.GetGroupsAsync();
                
                groupComboBox.Items.Clear();
                groupComboBox.Items.Add("اختر المجموعة...");
                
                foreach (var group in allGroups.Where(g => g.ContactGroups.Any()))
                {
                    groupComboBox.Items.Add($"{group.Name} ({group.ContactGroups.Count} رقم)");
                }

                groupComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GroupComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (groupComboBox.SelectedIndex > 0)
            {
                selectedGroup = allGroups[groupComboBox.SelectedIndex - 1];
                selectedGroupInfoLabel.Text = $"المجموعة: {selectedGroup.Name} - {selectedGroup.ContactGroups.Count} رقم";
                selectedGroupInfoLabel.ForeColor = Color.Green;
            }
            else
            {
                selectedGroup = null;
                selectedGroupInfoLabel.Text = "لم يتم اختيار مجموعة";
                selectedGroupInfoLabel.ForeColor = Color.Gray;
            }

            UpdateButtonStates();
        }

        private void MessageTextBox_TextChanged(object? sender, EventArgs e)
        {
            characterCountLabel.Text = $"{messageTextBox.Text.Length} حرف";
            UpdateButtonStates();
        }

        private void UpdateButtonStates()
        {
            var canSend = selectedGroup != null && !string.IsNullOrWhiteSpace(messageTextBox.Text) && _whatsAppService.IsConnected;
            var canPreview = selectedGroup != null && !string.IsNullOrWhiteSpace(messageTextBox.Text);

            previewButton.Enabled = canPreview;
            sendButton.Enabled = canSend;

            if (!_whatsAppService.IsConnected)
            {
                sendButton.Text = "غير متصل بواتساب";
            }
            else
            {
                sendButton.Text = "إرسال";
            }
        }

        private void PreviewButton_Click(object? sender, EventArgs e)
        {
            if (selectedGroup == null) return;

            var preview = $"المجموعة: {selectedGroup.Name}\n" +
                         $"عدد الأرقام: {selectedGroup.ContactGroups.Count}\n" +
                         $"طول الرسالة: {messageTextBox.Text.Length} حرف\n\n" +
                         $"نص الرسالة:\n{messageTextBox.Text}";

            MessageBox.Show(preview, "معاينة الرسالة", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void SendButton_Click(object? sender, EventArgs e)
        {
            if (selectedGroup == null || string.IsNullOrWhiteSpace(messageTextBox.Text))
                return;

            if (!_whatsAppService.IsConnected)
            {
                MessageBox.Show("يرجى الاتصال بواتساب أولاً", "غير متصل", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من إرسال الرسالة إلى {selectedGroup.ContactGroups.Count} رقم؟",
                "تأكيد الإرسال",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                await StartSending();
            }
        }

        private async Task StartSending()
        {
            try
            {
                // Show progress panel
                progressPanel.Visible = true;
                sendButton.Enabled = false;
                sendButton.Text = "جاري الإرسال...";

                // Get contacts from selected group
                var contacts = await _databaseService.GetContactsByGroupAsync(selectedGroup!.Id);
                
                sendProgressBar.Maximum = contacts.Count;
                sendProgressBar.Value = 0;
                statusListBox.Items.Clear();

                // Subscribe to message sent events
                _whatsAppService.MessageSent += OnMessageSent;

                // Send broadcast message
                var results = await _whatsAppService.SendBroadcastMessageAsync(contacts, messageTextBox.Text);

                // Show completion message
                var successCount = results.Count(r => r);
                var failCount = results.Count(r => !r);

                MessageBox.Show(
                    $"تم الانتهاء من الإرسال\n\nنجح: {successCount}\nفشل: {failCount}",
                    "انتهى الإرسال",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الإرسال: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _whatsAppService.MessageSent -= OnMessageSent;
                sendButton.Enabled = true;
                sendButton.Text = "إرسال";
                UpdateButtonStates();
            }
        }

        private void OnMessageSent(object? sender, MessageSentEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnMessageSent(sender, e)));
                return;
            }

            sendProgressBar.Value++;
            var status = e.Success ? "✓ نجح" : "✗ فشل";
            var message = $"{e.PhoneNumber} - {status}";
            
            if (!e.Success && !string.IsNullOrEmpty(e.ErrorMessage))
            {
                message += $" ({e.ErrorMessage})";
            }

            statusListBox.Items.Add(message);
            statusListBox.TopIndex = statusListBox.Items.Count - 1; // Auto scroll to bottom
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            UpdateButtonStates();
        }
    }
}
