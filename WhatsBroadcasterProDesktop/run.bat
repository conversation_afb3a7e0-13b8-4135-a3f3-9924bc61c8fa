@echo off
chcp 65001 >nul
title WhatsBroadcaster Pro

echo 🚀 بدء تشغيل WhatsBroadcaster Pro...
echo =====================================

REM التحقق من وجود .NET
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت. يرجى تثبيت .NET 9.0 أو أحدث
    echo 🔗 https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

REM عرض إصدار .NET
for /f "tokens=*" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
echo ✅ تم العثور على .NET إصدار: %DOTNET_VERSION%

REM التحقق من وجود Google Chrome
where chrome >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ تم العثور على Google Chrome
) else (
    if exist "%ProgramFiles%\Google\Chrome\Application\chrome.exe" (
        echo ✅ تم العثور على Google Chrome
    ) else if exist "%ProgramFiles(x86)%\Google\Chrome\Application\chrome.exe" (
        echo ✅ تم العثور على Google Chrome
    ) else (
        echo ⚠️  تحذير: لم يتم العثور على Google Chrome
        echo    يرجى تثبيت Google Chrome لاستخدام ميزة واتساب
    )
)

REM استعادة الحزم
echo 📦 استعادة الحزم...
dotnet restore
if %errorlevel% neq 0 (
    echo ❌ فشل في استعادة الحزم
    pause
    exit /b 1
)

REM بناء المشروع
echo 🔨 بناء المشروع...
dotnet build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.
echo 🎯 نصائح سريعة:
echo    • استخدم الخيار 4 للاتصال بواتساب أولاً
echo    • أضف أرقام تجريبية للاختبار  
echo    • لا تغلق نافذة Chrome يدوياً
echo.
echo 🚀 تشغيل التطبيق...
echo ====================

REM تشغيل التطبيق
dotnet run

echo.
echo 👋 شكراً لاستخدام WhatsBroadcaster Pro!
pause
