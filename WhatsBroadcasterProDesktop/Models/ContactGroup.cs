using System.ComponentModel.DataAnnotations;

namespace WhatsBroadcasterProDesktop.Models
{
    public class ContactGroup
    {
        [Key]
        public int Id { get; set; }

        public int ContactId { get; set; }
        public int GroupId { get; set; }

        public DateTime AddedAt { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual Contact Contact { get; set; } = null!;
        public virtual Group Group { get; set; } = null!;
    }
}
