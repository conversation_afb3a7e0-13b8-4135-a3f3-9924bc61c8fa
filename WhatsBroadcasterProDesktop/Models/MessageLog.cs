using System.ComponentModel.DataAnnotations;

namespace WhatsBroadcasterProDesktop.Models
{
    public enum DeliveryStatus
    {
        Pending,
        Sent,
        Delivered,
        Failed,
        Read
    }

    public class MessageLog
    {
        [Key]
        public int Id { get; set; }

        public int BroadcastMessageId { get; set; }

        public int ContactId { get; set; }

        public DeliveryStatus Status { get; set; } = DeliveryStatus.Pending;

        public DateTime SentAt { get; set; } = DateTime.Now;

        [StringLength(500)]
        public string? ErrorMessage { get; set; }

        public int RetryCount { get; set; } = 0;

        // Navigation properties
        public virtual BroadcastMessage BroadcastMessage { get; set; } = null!;
        public virtual Contact Contact { get; set; } = null!;
    }
}
