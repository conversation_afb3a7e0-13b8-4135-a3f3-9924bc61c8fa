using System.ComponentModel.DataAnnotations;

namespace WhatsBroadcasterProDesktop.Models
{
    public class Contact
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Email { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation property
        public virtual ICollection<ContactGroup> ContactGroups { get; set; } = new List<ContactGroup>();
        public virtual ICollection<MessageLog> MessageLogs { get; set; } = new List<MessageLog>();
    }
}
