{"version": "0.2.0", "configurations": [{"name": "Launch WhatsBroadcaster Pro", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net9.0/WhatsBroadcasterProDesktop.dll", "args": [], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "stopAtEntry": false}, {"name": "Launch WhatsBroadcaster Pro (External Terminal)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/bin/Debug/net9.0/WhatsBroadcasterProDesktop.dll", "args": [], "cwd": "${workspaceFolder}", "console": "externalTerminal", "stopAtEntry": false}]}