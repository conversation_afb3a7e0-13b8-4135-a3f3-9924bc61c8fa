using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop.Services
{
    public interface IWhatsAppService
    {
        bool IsConnected { get; }
        Task ConnectAsync();
        Task DisconnectAsync();
        Task<string> GetCurrentStatusAsync();
        Task<byte[]?> CaptureQRCodeAsync();
        Task<bool> SendMessageAsync(string phoneNumber, string message);
        Task<bool> SendMessageWithMediaAsync(string phoneNumber, string message, string mediaPath);
        Task<List<bool>> SendBroadcastMessageAsync(List<Contact> contacts, string message, string? mediaPath = null);
        event EventHandler<string> ConnectionStatusChanged;
        event EventHandler<MessageSentEventArgs> MessageSent;
    }

    public class MessageSentEventArgs : EventArgs
    {
        public string PhoneNumber { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
