using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop.Services
{
    public interface IDatabaseService
    {
        Task InitializeAsync();
        Task<List<Contact>> GetContactsAsync();
        Task<List<Group>> GetGroupsAsync();
        Task<Contact> AddContactAsync(Contact contact);
        Task<Group> AddGroupAsync(Group group);
        Task<bool> DeleteContactAsync(int contactId);
        Task<bool> DeleteGroupAsync(int groupId);
        Task<Contact> UpdateContactAsync(Contact contact);
        Task<Group> UpdateGroupAsync(Group group);
        Task<List<Contact>> GetContactsByGroupAsync(int groupId);
        Task<bool> AddContactToGroupAsync(int contactId, int groupId);
        Task<bool> RemoveContactFromGroupAsync(int contactId, int groupId);
        Task<BroadcastMessage> SaveBroadcastMessageAsync(BroadcastMessage message);
        Task<List<BroadcastMessage>> GetBroadcastMessagesAsync();
        Task<List<MessageLog>> GetMessageLogsAsync(int broadcastMessageId);
        Task UpdateMessageLogAsync(MessageLog messageLog);
    }
}
