using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop.Services
{
    public class WhatsAppService : IWhatsAppService
    {
        private IWebDriver? _driver;
        private WebDriverWait? _wait;
        private bool _isConnected = false;

        public bool IsConnected => _isConnected && VerifyConnection();

        private bool VerifyConnection()
        {
            if (_driver == null) return false;

            try
            {
                // Quick check if we're still on WhatsApp Web and logged in
                var chatElements = _driver.FindElements(By.CssSelector("[data-testid='chat-list']"));
                return chatElements.Count > 0;
            }
            catch
            {
                return false;
            }
        }

        private void CleanupOldProfiles(string baseProfilePath)
        {
            try
            {
                if (!Directory.Exists(baseProfilePath)) return;

                var profileDirs = Directory.GetDirectories(baseProfilePath, "chrome_profile_*")
                    .OrderByDescending(d => Directory.GetCreationTime(d))
                    .Skip(3) // Keep only the 3 most recent
                    .ToList();

                foreach (var dir in profileDirs)
                {
                    try
                    {
                        Directory.Delete(dir, true);
                    }
                    catch
                    {
                        // Ignore errors when deleting old profiles
                    }
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        private async Task KillExistingChromeProcesses()
        {
            try
            {
                var processes = System.Diagnostics.Process.GetProcessesByName("chrome");
                var chromedriverProcesses = System.Diagnostics.Process.GetProcessesByName("chromedriver");

                foreach (var process in processes.Concat(chromedriverProcesses))
                {
                    try
                    {
                        if (!process.HasExited)
                        {
                            process.Kill();
                            await process.WaitForExitAsync();
                        }
                    }
                    catch
                    {
                        // Ignore errors when killing processes
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                // Wait a bit for processes to fully terminate
                await Task.Delay(2000);
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        public event EventHandler<string>? ConnectionStatusChanged;
        public event EventHandler<MessageSentEventArgs>? MessageSent;

        public async Task ConnectAsync()
        {
            try
            {
                // Kill any existing Chrome processes that might be hanging
                await KillExistingChromeProcesses();

                ConnectionStatusChanged?.Invoke(this, "جاري تحضير المتصفح...");
                // Setup Chrome options for better stability
                var options = new ChromeOptions();

                // Create unique profile path with timestamp to avoid conflicts
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var profilePath = Path.Combine(appDataPath, "WhatsBroadcasterPro", $"chrome_profile_{timestamp}");

                // Clean up old profile directories (keep only last 3)
                CleanupOldProfiles(Path.Combine(appDataPath, "WhatsBroadcasterPro"));

                Directory.CreateDirectory(profilePath);

                options.AddArgument($"--user-data-dir={profilePath}");
                options.AddArgument("--no-sandbox");
                options.AddArgument("--disable-dev-shm-usage");
                options.AddArgument("--disable-blink-features=AutomationControlled");
                options.AddArgument("--disable-extensions");
                options.AddArgument("--disable-plugins");
                options.AddArgument("--disable-images"); // Faster loading
                options.AddArgument("--disable-javascript-harmony-shipping");
                options.AddArgument("--disable-background-timer-throttling");
                options.AddArgument("--disable-backgrounding-occluded-windows");
                options.AddArgument("--disable-renderer-backgrounding");
                options.AddArgument("--disable-features=TranslateUI");
                options.AddArgument("--disable-ipc-flooding-protection");
                options.AddArgument("--force-device-scale-factor=1");
                options.AddArgument("--disable-gpu");
                options.AddArgument("--no-first-run");
                options.AddArgument("--disable-default-apps");
                options.AddExcludedArgument("enable-automation");
                options.AddAdditionalOption("useAutomationExtension", false);

                // Set user agent to look more like a regular browser
                options.AddArgument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
                
                // Initialize Chrome driver
                _driver = new ChromeDriver(options);
                _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));

                // Navigate to WhatsApp Web
                _driver.Navigate().GoToUrl("https://web.whatsapp.com");

                // Wait for connection
                await WaitForConnectionAsync();

                _isConnected = true;
                ConnectionStatusChanged?.Invoke(this, "متصل");
            }
            catch (Exception ex)
            {
                _isConnected = false;
                ConnectionStatusChanged?.Invoke(this, $"خطأ في الاتصال: {ex.Message}");
                throw;
            }
        }

        private async Task WaitForConnectionAsync()
        {
            var timeout = TimeSpan.FromMinutes(5); // 5 minutes timeout for QR scan
            var startTime = DateTime.Now;

            while (DateTime.Now - startTime < timeout)
            {
                try
                {
                    // Check if we're logged in by looking for the main chat interface
                    var chatElements = _driver?.FindElements(By.CssSelector("[data-testid='chat-list']"));
                    if (chatElements?.Count > 0)
                    {
                        // Additional verification - check if we can see the search box
                        var searchElements = _driver?.FindElements(By.CssSelector("[data-testid='chat-list-search']"));
                        if (searchElements?.Count > 0)
                        {
                            ConnectionStatusChanged?.Invoke(this, "متصل بنجاح - جاهز للإرسال");
                            return; // Successfully connected
                        }
                    }

                    // Check if QR code is still visible
                    var qrElements = _driver?.FindElements(By.CssSelector("[data-testid='qr-code']"));
                    if (qrElements?.Count > 0)
                    {
                        ConnectionStatusChanged?.Invoke(this, "في انتظار مسح رمز QR - امسح الكود بهاتفك");
                    }
                    else
                    {
                        ConnectionStatusChanged?.Invoke(this, "جاري التحقق من الاتصال...");
                    }

                    await Task.Delay(2000); // Wait 2 seconds before checking again
                }
                catch
                {
                    await Task.Delay(2000);
                }
            }

            throw new TimeoutException("انتهت مهلة انتظار الاتصال. يرجى المحاولة مرة أخرى.");
        }

        public async Task DisconnectAsync()
        {
            try
            {
                ConnectionStatusChanged?.Invoke(this, "جاري قطع الاتصال...");

                _isConnected = false;

                if (_driver != null)
                {
                    try
                    {
                        _driver.Quit();
                    }
                    catch
                    {
                        // Ignore quit errors
                    }

                    try
                    {
                        _driver.Dispose();
                    }
                    catch
                    {
                        // Ignore dispose errors
                    }

                    _driver = null;
                }

                _wait = null;

                // Clean up any remaining Chrome processes
                await KillExistingChromeProcesses();

                ConnectionStatusChanged?.Invoke(this, "تم قطع الاتصال بنجاح");
            }
            catch (Exception ex)
            {
                ConnectionStatusChanged?.Invoke(this, $"خطأ في قطع الاتصال: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> SendMessageAsync(string phoneNumber, string message)
        {
            if (!_isConnected || _driver == null || _wait == null)
            {
                MessageSent?.Invoke(this, new MessageSentEventArgs
                {
                    PhoneNumber = phoneNumber,
                    Success = false,
                    ErrorMessage = "غير متصل بواتساب"
                });
                return false;
            }

            try
            {
                // Format phone number (remove any non-digit characters except +)
                var formattedNumber = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
                if (!formattedNumber.StartsWith("+"))
                {
                    formattedNumber = "+" + formattedNumber;
                }

                // Navigate to chat with the contact
                var chatUrl = $"https://web.whatsapp.com/send?phone={formattedNumber.Replace("+", "")}&text={Uri.EscapeDataString(message)}";
                _driver.Navigate().GoToUrl(chatUrl);

                // Wait for the page to load and check if chat opened successfully
                await Task.Delay(5000);

                // Check if we're on a valid WhatsApp chat page
                try
                {
                    // Look for the chat header or message input to confirm we're in a chat
                    var chatExists = _driver.FindElements(By.CssSelector("[data-testid='conversation-compose-box-input']")).Count > 0;
                    if (!chatExists)
                    {
                        // Try alternative selector
                        chatExists = _driver.FindElements(By.CssSelector("div[contenteditable='true'][data-tab='10']")).Count > 0;
                    }

                    if (!chatExists)
                    {
                        MessageSent?.Invoke(this, new MessageSentEventArgs
                        {
                            PhoneNumber = phoneNumber,
                            Success = false,
                            ErrorMessage = "لم يتم العثور على الرقم أو فتح المحادثة"
                        });
                        return false;
                    }
                }
                catch
                {
                    MessageSent?.Invoke(this, new MessageSentEventArgs
                    {
                        PhoneNumber = phoneNumber,
                        Success = false,
                        ErrorMessage = "خطأ في فتح المحادثة"
                    });
                    return false;
                }

                // Wait for and click the send button
                try
                {
                    var sendButton = _wait.Until(driver =>
                        driver.FindElement(By.CssSelector("[data-testid='send']")));

                    sendButton.Click();

                    // Wait and verify message was sent by checking for sent message indicators
                    await Task.Delay(3000);

                    // Check for message sent indicators (like checkmarks)
                    var messageSent = false;
                    try
                    {
                        // Look for sent message indicators
                        var sentIndicators = _driver.FindElements(By.CssSelector("[data-testid='msg-check']"));
                        messageSent = sentIndicators.Count > 0;

                        if (!messageSent)
                        {
                            // Alternative check - look for any message in the chat
                            var messages = _driver.FindElements(By.CssSelector("[data-testid='msg-container']"));
                            messageSent = messages.Count > 0;
                        }
                    }
                    catch
                    {
                        // If we can't verify, assume it was sent (fallback)
                        messageSent = true;
                    }

                    MessageSent?.Invoke(this, new MessageSentEventArgs
                    {
                        PhoneNumber = phoneNumber,
                        Success = messageSent,
                        ErrorMessage = messageSent ? null : "لم يتم التأكد من إرسال الرسالة"
                    });

                    return messageSent;
                }
                catch (Exception ex)
                {
                    MessageSent?.Invoke(this, new MessageSentEventArgs
                    {
                        PhoneNumber = phoneNumber,
                        Success = false,
                        ErrorMessage = $"خطأ في إرسال الرسالة: {ex.Message}"
                    });
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageSent?.Invoke(this, new MessageSentEventArgs 
                { 
                    PhoneNumber = phoneNumber, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                });
                return false;
            }
        }

        public async Task<bool> SendMessageWithMediaAsync(string phoneNumber, string message, string mediaPath)
        {
            if (!_isConnected || _driver == null || _wait == null)
            {
                return false;
            }

            try
            {
                // First send the media, then the text message
                // This is a simplified implementation - in a real app, you'd need to handle file uploads
                return await SendMessageAsync(phoneNumber, message);
            }
            catch (Exception ex)
            {
                MessageSent?.Invoke(this, new MessageSentEventArgs 
                { 
                    PhoneNumber = phoneNumber, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                });
                return false;
            }
        }

        public async Task<List<bool>> SendBroadcastMessageAsync(List<Contact> contacts, string message, string? mediaPath = null)
        {
            var results = new List<bool>();
            var successCount = 0;
            var failCount = 0;

            for (int i = 0; i < contacts.Count; i++)
            {
                var contact = contacts[i];

                // Check connection before each message
                if (!VerifyConnection())
                {
                    results.Add(false);
                    MessageSent?.Invoke(this, new MessageSentEventArgs
                    {
                        PhoneNumber = contact.PhoneNumber,
                        Success = false,
                        ErrorMessage = "فقدان الاتصال بواتساب"
                    });
                    failCount++;
                    continue;
                }

                try
                {
                    bool success;
                    if (!string.IsNullOrEmpty(mediaPath))
                    {
                        success = await SendMessageWithMediaAsync(contact.PhoneNumber, message, mediaPath);
                    }
                    else
                    {
                        success = await SendMessageAsync(contact.PhoneNumber, message);
                    }

                    results.Add(success);

                    if (success)
                    {
                        successCount++;
                    }
                    else
                    {
                        failCount++;
                    }

                    // Progressive delay - longer delays if we're having failures
                    var baseDelay = 5000; // 5 seconds base
                    var extraDelay = failCount * 1000; // Add 1 second for each failure
                    var randomDelay = Random.Shared.Next(1000, 3000); // Random 1-3 seconds
                    var totalDelay = Math.Min(baseDelay + extraDelay + randomDelay, 15000); // Max 15 seconds

                    await Task.Delay(totalDelay);

                    // If we have too many consecutive failures, add extra delay
                    if (failCount > 2 && i < contacts.Count - 1)
                    {
                        await Task.Delay(10000); // Extra 10 second delay
                    }
                }
                catch (Exception ex)
                {
                    results.Add(false);
                    failCount++;
                    MessageSent?.Invoke(this, new MessageSentEventArgs
                    {
                        PhoneNumber = contact.PhoneNumber,
                        Success = false,
                        ErrorMessage = $"خطأ غير متوقع: {ex.Message}"
                    });

                    // Add extra delay after exceptions
                    await Task.Delay(8000);
                }
            }

            return results;
        }

        public void Dispose()
        {
            try
            {
                _driver?.Quit();
                _driver?.Dispose();
            }
            catch
            {
                // Ignore disposal errors
            }
        }
    }
}
