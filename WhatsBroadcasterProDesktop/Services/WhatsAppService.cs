using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop.Services
{
    public class WhatsAppService : IWhatsAppService
    {
        private IWebDriver? _driver;
        private WebDriverWait? _wait;
        private bool _isConnected = false;

        public bool IsConnected => _isConnected && VerifyConnection();

        private bool VerifyConnection()
        {
            if (_driver == null) return false;

            try
            {
                // Quick check if we're still on WhatsApp Web and logged in
                var chatElements = _driver.FindElements(By.CssSelector("[data-testid='chat-list']"));
                if (chatElements.Count > 0) return true;

                // Alternative check
                var sidePanel = _driver.FindElements(By.CssSelector("[data-testid='side']"));
                if (sidePanel.Count > 0) return true;

                // Check URL
                var currentUrl = _driver.Url;
                if (!string.IsNullOrEmpty(currentUrl) &&
                    currentUrl.Contains("web.whatsapp.com") &&
                    !currentUrl.Equals("https://web.whatsapp.com/"))
                {
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> GetCurrentStatusAsync()
        {
            if (_driver == null) return "غير متصل";

            try
            {
                // Check if logged in
                if (VerifyConnection())
                {
                    return "متصل ومستعد للإرسال";
                }

                // Check if QR code is visible
                var qrElements = _driver.FindElements(By.CssSelector("[data-testid='qr-code']"));
                var canvasElements = _driver.FindElements(By.CssSelector("canvas"));

                if (qrElements.Count > 0 || canvasElements.Count > 0)
                {
                    return "رمز QR متاح - امسح بهاتفك";
                }

                // Check page content
                var pageText = _driver.FindElement(By.TagName("body"))?.Text ?? "";
                if (pageText.Contains("QR") || pageText.Contains("scan"))
                {
                    return "صفحة QR محملة";
                }

                if (pageText.Contains("Loading") || pageText.Contains("تحميل"))
                {
                    return "جاري التحميل";
                }

                return "حالة غير معروفة";
            }
            catch (Exception ex)
            {
                return $"خطأ في فحص الحالة: {ex.Message}";
            }
        }

        private void CleanupOldProfiles(string baseProfilePath)
        {
            try
            {
                if (!Directory.Exists(baseProfilePath)) return;

                var profileDirs = Directory.GetDirectories(baseProfilePath, "chrome_profile_*")
                    .OrderByDescending(d => Directory.GetCreationTime(d))
                    .Skip(3) // Keep only the 3 most recent
                    .ToList();

                foreach (var dir in profileDirs)
                {
                    try
                    {
                        Directory.Delete(dir, true);
                    }
                    catch
                    {
                        // Ignore errors when deleting old profiles
                    }
                }
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        private bool IsChromeInstalled()
        {
            try
            {
                // Check common Chrome installation paths on macOS
                var chromePaths = new[]
                {
                    "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                    "/usr/bin/google-chrome",
                    "/usr/local/bin/google-chrome"
                };

                foreach (var path in chromePaths)
                {
                    if (File.Exists(path))
                    {
                        return true;
                    }
                }

                // Try to find Chrome using which command
                try
                {
                    var process = new System.Diagnostics.Process
                    {
                        StartInfo = new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = "which",
                            Arguments = "google-chrome",
                            UseShellExecute = false,
                            RedirectStandardOutput = true,
                            CreateNoWindow = true
                        }
                    };
                    process.Start();
                    var output = process.StandardOutput.ReadToEnd();
                    process.WaitForExit();

                    return !string.IsNullOrWhiteSpace(output);
                }
                catch
                {
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }

        private async Task KillExistingChromeProcesses()
        {
            try
            {
                var processes = System.Diagnostics.Process.GetProcessesByName("chrome");
                var chromedriverProcesses = System.Diagnostics.Process.GetProcessesByName("chromedriver");

                foreach (var process in processes.Concat(chromedriverProcesses))
                {
                    try
                    {
                        if (!process.HasExited)
                        {
                            process.Kill();
                            await process.WaitForExitAsync();
                        }
                    }
                    catch
                    {
                        // Ignore errors when killing processes
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                // Wait a bit for processes to fully terminate
                await Task.Delay(2000);
            }
            catch
            {
                // Ignore cleanup errors
            }
        }

        public event EventHandler<string>? ConnectionStatusChanged;
        public event EventHandler<MessageSentEventArgs>? MessageSent;

        public async Task ConnectAsync()
        {
            try
            {
                // Check if Chrome is installed
                if (!IsChromeInstalled())
                {
                    ConnectionStatusChanged?.Invoke(this, "❌ Google Chrome غير مثبت - يرجى تثبيت Chrome أولاً");
                    throw new Exception("Google Chrome غير مثبت على النظام");
                }

                // Kill any existing Chrome processes that might be hanging
                await KillExistingChromeProcesses();

                ConnectionStatusChanged?.Invoke(this, "جاري تحضير المتصفح...");

                // Setup Chrome options for better stability
                var options = new ChromeOptions();

                // Create unique profile path with timestamp to avoid conflicts
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var profilePath = Path.Combine(appDataPath, "WhatsBroadcasterPro", $"chrome_profile_{timestamp}");

                // Clean up old profile directories (keep only last 3)
                CleanupOldProfiles(Path.Combine(appDataPath, "WhatsBroadcasterPro"));

                Directory.CreateDirectory(profilePath);

                // Essential Chrome arguments for macOS compatibility
                options.AddArgument($"--user-data-dir={profilePath}");
                options.AddArgument("--no-sandbox");
                options.AddArgument("--disable-dev-shm-usage");
                options.AddArgument("--disable-blink-features=AutomationControlled");
                options.AddArgument("--disable-extensions");
                options.AddArgument("--disable-plugins");
                options.AddArgument("--disable-background-timer-throttling");
                options.AddArgument("--disable-backgrounding-occluded-windows");
                options.AddArgument("--disable-renderer-backgrounding");
                options.AddArgument("--disable-features=TranslateUI");
                options.AddArgument("--disable-ipc-flooding-protection");
                options.AddArgument("--no-first-run");
                options.AddArgument("--disable-default-apps");
                options.AddArgument("--disable-web-security");
                options.AddArgument("--allow-running-insecure-content");
                options.AddArgument("--ignore-certificate-errors");
                options.AddArgument("--ignore-ssl-errors");
                options.AddArgument("--ignore-certificate-errors-spki-list");
                options.AddArgument("--disable-features=VizDisplayCompositor");

                // Remove problematic arguments for macOS
                // options.AddArgument("--disable-gpu"); // Can cause issues on macOS
                // options.AddArgument("--disable-images"); // May prevent WhatsApp from loading properly

                options.AddExcludedArgument("enable-automation");
                options.AddAdditionalOption("useAutomationExtension", false);

                // Set user agent to look more like a regular browser
                options.AddArgument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");

                ConnectionStatusChanged?.Invoke(this, "جاري تشغيل المتصفح...");

                // Initialize Chrome driver with error handling
                try
                {
                    _driver = new ChromeDriver(options);
                    _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));
                }
                catch (Exception driverEx)
                {
                    ConnectionStatusChanged?.Invoke(this, $"خطأ في تشغيل المتصفح: {driverEx.Message}");
                    throw new Exception($"فشل في تشغيل Chrome: {driverEx.Message}");
                }

                ConnectionStatusChanged?.Invoke(this, "جاري الاتصال بواتساب ويب...");

                // Navigate to WhatsApp Web with retry logic
                var maxRetries = 3;
                for (int i = 0; i < maxRetries; i++)
                {
                    try
                    {
                        _driver.Navigate().GoToUrl("https://web.whatsapp.com");
                        break;
                    }
                    catch (Exception navEx)
                    {
                        if (i == maxRetries - 1)
                        {
                            throw new Exception($"فشل في الوصول لواتساب ويب: {navEx.Message}");
                        }
                        await Task.Delay(2000);
                    }
                }

                // Wait for connection
                await WaitForConnectionAsync();

                _isConnected = true;
                ConnectionStatusChanged?.Invoke(this, "متصل بنجاح - جاهز للإرسال");
            }
            catch (Exception ex)
            {
                _isConnected = false;

                // Clean up on error
                try
                {
                    _driver?.Quit();
                    _driver?.Dispose();
                    _driver = null;
                    _wait = null;
                }
                catch { }

                var errorMessage = ex.Message;
                if (errorMessage.Contains("chrome"))
                {
                    errorMessage = "تأكد من تثبيت Google Chrome على النظام";
                }
                else if (errorMessage.Contains("network") || errorMessage.Contains("timeout"))
                {
                    errorMessage = "تحقق من اتصال الإنترنت";
                }

                ConnectionStatusChanged?.Invoke(this, $"خطأ في الاتصال: {errorMessage}");
                throw;
            }
        }

        private async Task WaitForConnectionAsync()
        {
            var timeout = TimeSpan.FromMinutes(5); // 5 minutes timeout for QR scan
            var startTime = DateTime.Now;
            var lastStatus = "";

            ConnectionStatusChanged?.Invoke(this, "جاري تحميل واتساب ويب...");

            // Wait for initial page load
            await Task.Delay(5000);

            while (DateTime.Now - startTime < timeout)
            {
                try
                {
                    // Check if page is loaded
                    var pageLoaded = _driver?.FindElements(By.TagName("body")).Count > 0;
                    if (!pageLoaded)
                    {
                        ConnectionStatusChanged?.Invoke(this, "جاري تحميل الصفحة...");
                        await Task.Delay(3000);
                        continue;
                    }

                    // Check if we're logged in by looking for multiple indicators
                    var isLoggedIn = false;

                    // Method 1: Check for chat list
                    var chatElements = _driver?.FindElements(By.CssSelector("[data-testid='chat-list']"));
                    if (chatElements?.Count > 0)
                    {
                        isLoggedIn = true;
                    }

                    // Method 2: Check for side panel
                    if (!isLoggedIn)
                    {
                        var sidePanel = _driver?.FindElements(By.CssSelector("[data-testid='side']"));
                        if (sidePanel?.Count > 0)
                        {
                            isLoggedIn = true;
                        }
                    }

                    // Method 3: Check for search box
                    if (!isLoggedIn)
                    {
                        var searchElements = _driver?.FindElements(By.CssSelector("[data-testid='chat-list-search']"));
                        if (searchElements?.Count > 0)
                        {
                            isLoggedIn = true;
                        }
                    }

                    // Method 4: Check for main app container (more reliable)
                    if (!isLoggedIn)
                    {
                        var appElements = _driver?.FindElements(By.CssSelector("#app"));
                        if (appElements?.Count > 0)
                        {
                            var appText = appElements[0].Text;
                            // If we see typical WhatsApp interface text, we're likely logged in
                            if (appText.Contains("Chats") || appText.Contains("محادثات") ||
                                appText.Contains("Status") || appText.Contains("الحالة") ||
                                appText.Contains("Calls") || appText.Contains("المكالمات") ||
                                !appText.Contains("WhatsApp Web"))
                            {
                                isLoggedIn = true;
                            }
                        }
                    }

                    // Method 5: Check URL - if we're not on the main page, we might be logged in
                    if (!isLoggedIn)
                    {
                        var currentUrl = _driver?.Url;
                        if (!string.IsNullOrEmpty(currentUrl) &&
                            currentUrl.Contains("web.whatsapp.com") &&
                            !currentUrl.Equals("https://web.whatsapp.com/"))
                        {
                            isLoggedIn = true;
                        }
                    }

                    if (isLoggedIn)
                    {
                        ConnectionStatusChanged?.Invoke(this, "✅ تم الاتصال بنجاح! جاهز للإرسال");
                        return; // Successfully connected
                    }

                    // Check if QR code is visible using multiple methods
                    var qrVisible = false;

                    // Method 1: Check for QR code element
                    var qrElements = _driver?.FindElements(By.CssSelector("[data-testid='qr-code']"));
                    if (qrElements?.Count > 0)
                    {
                        qrVisible = true;
                    }

                    // Method 2: Check for canvas (QR code is often in canvas)
                    if (!qrVisible)
                    {
                        var canvasElements = _driver?.FindElements(By.CssSelector("canvas"));
                        if (canvasElements?.Count > 0)
                        {
                            qrVisible = true;
                        }
                    }

                    // Method 3: Check for QR container div
                    if (!qrVisible)
                    {
                        var qrContainers = _driver?.FindElements(By.CssSelector("div[data-ref]"));
                        if (qrContainers?.Count > 0)
                        {
                            qrVisible = true;
                        }
                    }

                    // Method 4: Check page content for QR-related text
                    if (!qrVisible)
                    {
                        var pageText = _driver?.FindElement(By.TagName("body"))?.Text ?? "";
                        if (pageText.Contains("QR") || pageText.Contains("scan") ||
                            pageText.Contains("phone") || pageText.Contains("هاتف") ||
                            pageText.Contains("مسح") || pageText.Contains("رمز"))
                        {
                            qrVisible = true;
                        }
                    }

                    if (qrVisible)
                    {
                        var newStatus = "📱 امسح رمز QR بهاتفك للاتصال";
                        if (newStatus != lastStatus)
                        {
                            ConnectionStatusChanged?.Invoke(this, newStatus);
                            lastStatus = newStatus;
                        }
                    }
                    else
                    {
                        // Check for loading indicators
                        var loadingElements = _driver?.FindElements(By.CssSelector("[data-testid='intro-md-beta-logo-dark'], [data-testid='intro-md-beta-logo-light']"));
                        var isLoading = loadingElements?.Count > 0;

                        // Also check for WhatsApp logo or loading text
                        if (!isLoading)
                        {
                            var pageText = _driver?.FindElement(By.TagName("body"))?.Text ?? "";
                            if (pageText.Contains("WhatsApp") && pageText.Contains("Loading"))
                            {
                                isLoading = true;
                            }
                        }

                        if (isLoading)
                        {
                            var newStatus = "⏳ جاري تحميل واتساب ويب...";
                            if (newStatus != lastStatus)
                            {
                                ConnectionStatusChanged?.Invoke(this, newStatus);
                                lastStatus = newStatus;
                            }
                        }
                        else
                        {
                            var newStatus = "🔍 جاري البحث عن رمز QR...";
                            if (newStatus != lastStatus)
                            {
                                ConnectionStatusChanged?.Invoke(this, newStatus);
                                lastStatus = newStatus;
                            }
                        }
                    }

                    await Task.Delay(2000); // Wait 2 seconds before checking again
                }
                catch
                {
                    await Task.Delay(2000);
                }
            }

            throw new TimeoutException("انتهت مهلة انتظار الاتصال. يرجى المحاولة مرة أخرى.");
        }

        public async Task DisconnectAsync()
        {
            try
            {
                ConnectionStatusChanged?.Invoke(this, "جاري قطع الاتصال...");

                _isConnected = false;

                if (_driver != null)
                {
                    try
                    {
                        _driver.Quit();
                    }
                    catch
                    {
                        // Ignore quit errors
                    }

                    try
                    {
                        _driver.Dispose();
                    }
                    catch
                    {
                        // Ignore dispose errors
                    }

                    _driver = null;
                }

                _wait = null;

                // Clean up any remaining Chrome processes
                await KillExistingChromeProcesses();

                ConnectionStatusChanged?.Invoke(this, "تم قطع الاتصال بنجاح");
            }
            catch (Exception ex)
            {
                ConnectionStatusChanged?.Invoke(this, $"خطأ في قطع الاتصال: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> SendMessageAsync(string phoneNumber, string message)
        {
            if (!_isConnected || _driver == null || _wait == null)
            {
                MessageSent?.Invoke(this, new MessageSentEventArgs
                {
                    PhoneNumber = phoneNumber,
                    Success = false,
                    ErrorMessage = "غير متصل بواتساب"
                });
                return false;
            }

            try
            {
                // Format phone number (remove any non-digit characters except +)
                var formattedNumber = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
                if (!formattedNumber.StartsWith("+"))
                {
                    formattedNumber = "+" + formattedNumber;
                }

                // Navigate to chat with the contact (without pre-filled message to avoid issues)
                var chatUrl = $"https://web.whatsapp.com/send?phone={formattedNumber.Replace("+", "")}";
                _driver.Navigate().GoToUrl(chatUrl);

                // Wait for the page to load
                await Task.Delay(7000); // Increased wait time

                // Check if we're on a valid WhatsApp chat page
                try
                {
                    // Look for the message input box
                    var messageInput = _wait.Until(driver =>
                    {
                        var input1 = driver.FindElements(By.CssSelector("[data-testid='conversation-compose-box-input']"));
                        if (input1.Count > 0) return input1[0];

                        var input2 = driver.FindElements(By.CssSelector("div[contenteditable='true'][data-tab='10']"));
                        if (input2.Count > 0) return input2[0];

                        var input3 = driver.FindElements(By.CssSelector("div[contenteditable='true']"));
                        if (input3.Count > 0) return input3[0];

                        throw new NoSuchElementException("Message input not found");
                    });

                    // Clear any existing text and type the message
                    messageInput.Clear();
                    messageInput.Click();
                    await Task.Delay(1000);

                    // Type the message character by character for better reliability
                    messageInput.SendKeys(message);
                    await Task.Delay(2000);

                    // Wait for send button to become available and click it
                    var sendButton = _wait.Until(driver =>
                    {
                        // Try multiple selectors for send button
                        var btn1 = driver.FindElements(By.CssSelector("[data-testid='send']"));
                        if (btn1.Count > 0 && btn1[0].Enabled) return btn1[0];

                        var btn2 = driver.FindElements(By.CssSelector("button[aria-label*='Send']"));
                        if (btn2.Count > 0 && btn2[0].Enabled) return btn2[0];

                        var btn3 = driver.FindElements(By.CssSelector("span[data-testid='send']"));
                        if (btn3.Count > 0) return btn3[0];

                        // Look for send icon
                        var btn4 = driver.FindElements(By.XPath("//span[@data-testid='send']//parent::button"));
                        if (btn4.Count > 0 && btn4[0].Enabled) return btn4[0];

                        throw new NoSuchElementException("Send button not found or not enabled");
                    });

                    // Click the send button
                    sendButton.Click();
                    await Task.Delay(1000);

                    // Alternative: Press Enter key if click doesn't work
                    try
                    {
                        messageInput.SendKeys(OpenQA.Selenium.Keys.Enter);
                    }
                    catch
                    {
                        // Ignore if Enter doesn't work
                    }
                }
                catch (Exception ex)
                {
                    MessageSent?.Invoke(this, new MessageSentEventArgs
                    {
                        PhoneNumber = phoneNumber,
                        Success = false,
                        ErrorMessage = $"خطأ في كتابة أو إرسال الرسالة: {ex.Message}"
                    });
                    return false;
                }

                // Wait for message to be sent and verify
                await Task.Delay(4000); // Wait for message to be processed

                // Check if message was sent successfully using multiple methods
                var messageSent = false;
                var errorMessage = "";

                try
                {
                    // Method 1: Check for sent message indicators (checkmarks)
                    var sentIndicators = _driver.FindElements(By.CssSelector("[data-testid='msg-check']"));
                    if (sentIndicators.Count > 0)
                    {
                        messageSent = true;
                    }

                    // Method 2: Look for message containers in the chat
                    if (!messageSent)
                    {
                        var messageContainers = _driver.FindElements(By.CssSelector("[data-testid='msg-container']"));
                        var outgoingMessages = _driver.FindElements(By.CssSelector("div[data-testid='msg-container'].message-out"));

                        if (messageContainers.Count > 0 || outgoingMessages.Count > 0)
                        {
                            messageSent = true;
                        }
                    }

                    // Method 3: Check if message input is empty (indicates message was sent)
                    if (!messageSent)
                    {
                        var messageInput = _driver.FindElements(By.CssSelector("[data-testid='conversation-compose-box-input']"));
                        if (messageInput.Count > 0)
                        {
                            var inputText = messageInput[0].Text.Trim();
                            if (string.IsNullOrEmpty(inputText))
                            {
                                messageSent = true;
                            }
                        }
                    }

                    // Method 4: Check for any recent message in chat that contains our text
                    if (!messageSent)
                    {
                        var allMessages = _driver.FindElements(By.CssSelector("span.selectable-text"));
                        foreach (var msg in allMessages.TakeLast(3)) // Check last 3 messages
                        {
                            if (msg.Text.Contains(message.Substring(0, Math.Min(20, message.Length))))
                            {
                                messageSent = true;
                                break;
                            }
                        }
                    }

                    // Method 5: Check if send button is no longer visible/active
                    if (!messageSent)
                    {
                        var sendButtons = _driver.FindElements(By.CssSelector("[data-testid='send']"));
                        if (sendButtons.Count == 0)
                        {
                            // Send button disappeared, likely message was sent
                            messageSent = true;
                        }
                    }

                    if (!messageSent)
                    {
                        errorMessage = "لم يتم التأكد من إرسال الرسالة - قد تحتاج للتحقق يدوياً";
                    }
                }
                catch (Exception ex)
                {
                    // If verification fails, check if we can still see the message input
                    try
                    {
                        var messageInput = _driver.FindElements(By.CssSelector("[data-testid='conversation-compose-box-input']"));
                        if (messageInput.Count > 0 && string.IsNullOrEmpty(messageInput[0].Text.Trim()))
                        {
                            messageSent = true; // Input is empty, likely sent
                        }
                        else
                        {
                            errorMessage = $"خطأ في التحقق من الإرسال: {ex.Message}";
                        }
                    }
                    catch
                    {
                        errorMessage = "لم يتم التأكد من إرسال الرسالة";
                    }
                }

                MessageSent?.Invoke(this, new MessageSentEventArgs
                {
                    PhoneNumber = phoneNumber,
                    Success = messageSent,
                    ErrorMessage = messageSent ? null : errorMessage
                });

                return messageSent;
            }
            catch (Exception ex)
            {
                MessageSent?.Invoke(this, new MessageSentEventArgs 
                { 
                    PhoneNumber = phoneNumber, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                });
                return false;
            }
        }

        public async Task<bool> SendMessageWithMediaAsync(string phoneNumber, string message, string mediaPath)
        {
            if (!_isConnected || _driver == null || _wait == null)
            {
                return false;
            }

            try
            {
                // First send the media, then the text message
                // This is a simplified implementation - in a real app, you'd need to handle file uploads
                return await SendMessageAsync(phoneNumber, message);
            }
            catch (Exception ex)
            {
                MessageSent?.Invoke(this, new MessageSentEventArgs 
                { 
                    PhoneNumber = phoneNumber, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                });
                return false;
            }
        }

        public async Task<List<bool>> SendBroadcastMessageAsync(List<Contact> contacts, string message, string? mediaPath = null)
        {
            var results = new List<bool>();
            var successCount = 0;
            var failCount = 0;

            for (int i = 0; i < contacts.Count; i++)
            {
                var contact = contacts[i];

                // Check connection before each message
                if (!VerifyConnection())
                {
                    results.Add(false);
                    MessageSent?.Invoke(this, new MessageSentEventArgs
                    {
                        PhoneNumber = contact.PhoneNumber,
                        Success = false,
                        ErrorMessage = "فقدان الاتصال بواتساب"
                    });
                    failCount++;
                    continue;
                }

                try
                {
                    bool success;
                    if (!string.IsNullOrEmpty(mediaPath))
                    {
                        success = await SendMessageWithMediaAsync(contact.PhoneNumber, message, mediaPath);
                    }
                    else
                    {
                        success = await SendMessageAsync(contact.PhoneNumber, message);
                    }

                    results.Add(success);

                    if (success)
                    {
                        successCount++;
                    }
                    else
                    {
                        failCount++;
                    }

                    // Progressive delay - longer delays if we're having failures
                    var baseDelay = 5000; // 5 seconds base
                    var extraDelay = failCount * 1000; // Add 1 second for each failure
                    var randomDelay = Random.Shared.Next(1000, 3000); // Random 1-3 seconds
                    var totalDelay = Math.Min(baseDelay + extraDelay + randomDelay, 15000); // Max 15 seconds

                    await Task.Delay(totalDelay);

                    // If we have too many consecutive failures, add extra delay
                    if (failCount > 2 && i < contacts.Count - 1)
                    {
                        await Task.Delay(10000); // Extra 10 second delay
                    }
                }
                catch (Exception ex)
                {
                    results.Add(false);
                    failCount++;
                    MessageSent?.Invoke(this, new MessageSentEventArgs
                    {
                        PhoneNumber = contact.PhoneNumber,
                        Success = false,
                        ErrorMessage = $"خطأ غير متوقع: {ex.Message}"
                    });

                    // Add extra delay after exceptions
                    await Task.Delay(8000);
                }
            }

            return results;
        }

        public void Dispose()
        {
            try
            {
                _driver?.Quit();
                _driver?.Dispose();
            }
            catch
            {
                // Ignore disposal errors
            }
        }
    }
}
