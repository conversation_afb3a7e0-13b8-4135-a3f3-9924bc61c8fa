<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="WhatsBroadcasterProDesktop.Views.SelectiveMessageWindow"
        Title="🎯 إرسال رسائل لأرقام محددة"
        Width="1200" Height="800"
        MinWidth="900" MinHeight="600"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}">

    <Grid Margin="25">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                Background="{StaticResource PrimaryBrush}"
                Padding="25"
                CornerRadius="15"
                BoxShadow="0 4 12 0 #20000000"
                Margin="0,0,0,30">
            <StackPanel HorizontalAlignment="Center">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Spacing="15">
                    <TextBlock Text="🎯" FontSize="36" VerticalAlignment="Center" Foreground="{StaticResource TextOnPrimaryBrush}"/>
                    <TextBlock Text="إرسال رسائل لأرقام محددة"
                               FontSize="28"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               Foreground="{StaticResource TextOnPrimaryBrush}"/>
                </StackPanel>
                <TextBlock Text="اختر الأرقام التي تريد إرسال الرسالة إليها بدقة"
                           FontSize="16"
                           HorizontalAlignment="Center"
                           Foreground="{StaticResource TextOnPrimaryBrush}"
                           Opacity="0.9"
                           Margin="0,8,0,0"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3*"/>
                <ColumnDefinition Width="25"/>
                <ColumnDefinition Width="2*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Contacts List -->
            <Border Grid.Column="0"
                    Background="{StaticResource SurfaceBrush}"
                    CornerRadius="15"
                    Padding="25"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    BoxShadow="0 4 12 0 #10000000">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Contacts Header -->
                    <Border Grid.Row="0"
                            Background="{StaticResource PrimaryLightBrush}"
                            CornerRadius="8"
                            Padding="15"
                            Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal" Spacing="12">
                            <TextBlock Text="👥" FontSize="24" VerticalAlignment="Center"/>
                            <TextBlock Text="قائمة الأرقام"
                                       FontSize="18"
                                       FontWeight="Bold"
                                       Foreground="{StaticResource PrimaryDarkBrush}"
                                       VerticalAlignment="Center"/>
                            <Border Background="{StaticResource PrimaryBrush}"
                                    CornerRadius="12"
                                    Padding="8,4">
                                <TextBlock Text="{Binding TotalContactsCount, StringFormat=\{0\} رقم}"
                                           FontSize="12"
                                           FontWeight="SemiBold"
                                           Foreground="{StaticResource TextOnPrimaryBrush}"/>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Search and Filter -->
                    <Border Grid.Row="1"
                            Background="{StaticResource BackgroundBrush}"
                            CornerRadius="8"
                            Padding="15"
                            Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="15"/>
                                <ColumnDefinition Width="220"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0"
                                     Text="{Binding SearchText}"
                                     Watermark="🔍 البحث في الأسماء أو الأرقام..."
                                     Height="45"
                                     FontSize="14"
                                     CornerRadius="8"
                                     Padding="12"/>

                            <ComboBox Grid.Column="2"
                                      ItemsSource="{Binding Groups}"
                                      SelectedItem="{Binding SelectedGroupFilter}"
                                      DisplayMemberBinding="{Binding Name}"
                                      PlaceholderText="تصفية حسب المجموعة"
                                      Height="45"
                                      FontSize="14"
                                      CornerRadius="8"/>
                        </Grid>
                    </Border>

                    <!-- Contacts DataGrid -->
                    <DataGrid Grid.Row="2" 
                              ItemsSource="{Binding FilteredContacts}"
                              AutoGenerateColumns="False"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              SelectionMode="Extended"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="True"
                              Background="White"
                              CornerRadius="8">
                        
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Header="✓" Width="50">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox IsChecked="{Binding IsSelected}" 
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <DataGridTextColumn Header="الاسم" 
                                                Binding="{Binding Name}" 
                                                Width="200"/>
                            
                            <DataGridTextColumn Header="رقم الهاتف" 
                                                Binding="{Binding PhoneNumber}" 
                                                Width="150"/>
                            
                            <DataGridTextColumn Header="البريد الإلكتروني" 
                                                Binding="{Binding Email}" 
                                                Width="200"/>
                            
                            <DataGridTextColumn Header="المجموعات" 
                                                Binding="{Binding GroupNames}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Selection Controls -->
                    <Border Grid.Row="3"
                            Background="{StaticResource SecondaryLightBrush}"
                            CornerRadius="8"
                            Padding="15"
                            Margin="0,20,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="15"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <Button Grid.Column="0"
                                    Content="✅ تحديد الكل"
                                    Command="{Binding SelectAllCommand}"
                                    Classes="success"
                                    Height="40"
                                    MinWidth="120"/>

                            <Button Grid.Column="2"
                                    Content="❌ إلغاء التحديد"
                                    Command="{Binding UnselectAllCommand}"
                                    Background="{StaticResource TextSecondaryBrush}"
                                    Foreground="{StaticResource TextOnDarkBrush}"
                                    CornerRadius="8"
                                    Height="40"
                                    MinWidth="120"
                                    FontSize="14"
                                    FontWeight="SemiBold"/>

                            <Border Grid.Column="4"
                                    Background="{StaticResource SuccessBrush}"
                                    CornerRadius="20"
                                    Padding="15,8">
                                <StackPanel Orientation="Horizontal" Spacing="8">
                                    <TextBlock Text="📊" FontSize="16" Foreground="{StaticResource TextOnDarkBrush}"/>
                                    <TextBlock Text="{Binding SelectedContactsCount, StringFormat='المحدد: \{0\} رقم'}"
                                               FontWeight="Bold"
                                               FontSize="14"
                                               Foreground="{StaticResource TextOnDarkBrush}"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>
                </Grid>
            </Border>

            <!-- Right Panel - Message Composition -->
            <ScrollViewer Grid.Column="2" VerticalScrollBarVisibility="Auto">
                <StackPanel Spacing="20" Margin="5">
                
                <!-- Message Composition -->
                <Border Background="{StaticResource SurfaceBrush}"
                        CornerRadius="15"
                        Padding="25"
                        BorderBrush="{StaticResource BorderBrush}"
                        BorderThickness="1"
                        BoxShadow="0 4 12 0 #10000000">
                    <StackPanel Spacing="20">
                        <Border Background="{StaticResource WarningBrush}"
                                CornerRadius="10"
                                Padding="15">
                            <StackPanel Orientation="Horizontal" Spacing="12">
                                <TextBlock Text="✍️" FontSize="24" Foreground="{StaticResource TextOnDarkBrush}"/>
                                <TextBlock Text="كتابة الرسالة"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="{StaticResource TextOnDarkBrush}"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <TextBox Text="{Binding MessageContent}"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 Height="120"
                                 Watermark="اكتب نص الرسالة التي تريد إرسالها..."
                                 FontSize="14"
                                 CornerRadius="8"
                                 Padding="12"
                                 BorderBrush="{StaticResource BorderBrush}"
                                 BorderThickness="2"/>

                        <Border Background="{StaticResource BackgroundBrush}"
                                CornerRadius="8"
                                Padding="12">
                            <Grid>
                                <StackPanel Orientation="Horizontal" Spacing="8">
                                    <TextBlock Text="📝" FontSize="14"/>
                                    <TextBlock Text="{Binding MessageContent.Length, StringFormat='عدد الأحرف: \{0\}'}"
                                               FontSize="13"
                                               FontWeight="SemiBold"
                                               Foreground="{StaticResource TextPrimaryBrush}"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal"
                                            HorizontalAlignment="Right"
                                            Spacing="6">
                                    <TextBlock Text="💡" FontSize="14"/>
                                    <TextBlock Text="اجعل الرسالة واضحة ومختصرة"
                                               FontSize="12"
                                               Foreground="{StaticResource WarningBrush}"
                                               FontWeight="SemiBold"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Send Settings -->
                <Border Background="{StaticResource SurfaceBrush}"
                        CornerRadius="12"
                        Padding="18"
                        BorderBrush="{StaticResource BorderBrush}"
                        BorderThickness="1"
                        BoxShadow="0 2 8 0 #10000000">
                    <StackPanel Spacing="15">
                        <Border Background="{StaticResource InfoBrush}"
                                CornerRadius="10"
                                Padding="15">
                            <StackPanel Orientation="Horizontal" Spacing="12">
                                <TextBlock Text="⚙️" FontSize="24" Foreground="{StaticResource TextOnDarkBrush}"/>
                                <TextBlock Text="إعدادات الإرسال"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="{StaticResource TextOnDarkBrush}"
                                           VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <Border Background="{StaticResource SecondaryLightBrush}"
                                CornerRadius="10"
                                Padding="15">
                            <StackPanel Spacing="12">
                                <StackPanel Orientation="Horizontal" Spacing="8">
                                    <TextBlock Text="⏱️" FontSize="16"/>
                                    <TextBlock Text="التأخير بين الرسائل (ثانية):"
                                               FontSize="14"
                                               FontWeight="SemiBold"
                                               Foreground="{StaticResource TextPrimaryBrush}"/>
                                </StackPanel>
                                <NumericUpDown Value="{Binding DelayBetweenMessages}"
                                               Minimum="3"
                                               Maximum="30"
                                               Increment="1"
                                               Height="35"
                                               FontSize="14"
                                               CornerRadius="6"
                                               Padding="8"/>
                                <Border Background="{StaticResource DangerBrush}"
                                        CornerRadius="6"
                                        Padding="10">
                                    <StackPanel Orientation="Horizontal" Spacing="8">
                                        <TextBlock Text="⚠️" FontSize="14" Foreground="{StaticResource TextOnDarkBrush}"/>
                                        <TextBlock Text="يُنصح بـ 5-10 ثوانٍ لتجنب الحظر"
                                                   FontSize="12"
                                                   FontWeight="SemiBold"
                                                   Foreground="{StaticResource TextOnDarkBrush}"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Border>

                        <Border Background="{StaticResource PrimaryLightBrush}"
                                CornerRadius="8"
                                Padding="15">
                            <CheckBox Content="💾 حفظ الرسالة في سجل الإرسال"
                                      IsChecked="{Binding SaveToHistory}"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      Foreground="{StaticResource PrimaryDarkBrush}"/>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Send Button -->
                <Border CornerRadius="10"
                        BoxShadow="0 4 12 0 #30000000">
                    <Button Content="🚀 إرسال الرسائل"
                            Background="{StaticResource PrimaryBrush}"
                            Foreground="{StaticResource TextOnPrimaryBrush}"
                            FontSize="16"
                            FontWeight="Bold"
                            Height="50"
                            CornerRadius="10"
                            BorderThickness="0"
                            Command="{Binding SendMessagesCommand}"
                            IsEnabled="{Binding CanSendMessages}"/>
                </Border>

                <!-- Progress -->
                <Border Background="{StaticResource WarningBrush}"
                        CornerRadius="15"
                        Padding="25"
                        BoxShadow="0 4 12 0 #30000000"
                        IsVisible="{Binding IsSending}">
                    <StackPanel Spacing="20">
                        <StackPanel Orientation="Horizontal"
                                    HorizontalAlignment="Center"
                                    Spacing="15">
                            <TextBlock Text="⏳" FontSize="28" Foreground="{StaticResource TextOnDarkBrush}"/>
                            <TextBlock Text="جاري الإرسال..."
                                       FontSize="20"
                                       FontWeight="Bold"
                                       Foreground="{StaticResource TextOnDarkBrush}"
                                       VerticalAlignment="Center"/>
                        </StackPanel>

                        <Border Background="White"
                                CornerRadius="15"
                                Padding="8">
                            <ProgressBar Value="{Binding SendProgress}"
                                         Maximum="100"
                                         Height="30"
                                         CornerRadius="12"
                                         Background="#F0F0F0"
                                         Foreground="{StaticResource SuccessBrush}"/>
                        </Border>

                        <Border Background="White"
                                CornerRadius="8"
                                Padding="12">
                            <TextBlock Text="{Binding SendProgressText}"
                                       HorizontalAlignment="Center"
                                       FontSize="15"
                                       FontWeight="SemiBold"
                                       Foreground="{StaticResource TextPrimaryBrush}"/>
                        </Border>
                    </StackPanel>
                </Border>

                </StackPanel>
            </ScrollViewer>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2"
                Background="{StaticResource InfoBrush}"
                Padding="20"
                CornerRadius="15"
                Margin="0,25,0,0"
                BoxShadow="0 4 12 0 #20000000">
            <Grid>
                <StackPanel Orientation="Horizontal" Spacing="10">
                    <TextBlock Text="ℹ️" FontSize="16" Foreground="{StaticResource TextOnDarkBrush}"/>
                    <TextBlock Text="{Binding StatusMessage}"
                               VerticalAlignment="Center"
                               FontSize="15"
                               FontWeight="SemiBold"
                               Foreground="{StaticResource TextOnDarkBrush}"/>
                </StackPanel>
                <Border Background="White"
                        CornerRadius="20"
                        Padding="12,6"
                        HorizontalAlignment="Right">
                    <StackPanel Orientation="Horizontal" Spacing="6">
                        <TextBlock Text="💡" FontSize="14"/>
                        <TextBlock Text="نصيحة: اختبر الرسالة على رقم واحد أولاً"
                                   FontSize="12"
                                   FontWeight="SemiBold"
                                   Foreground="{StaticResource TextPrimaryBrush}"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

    </Grid>
</Window>
