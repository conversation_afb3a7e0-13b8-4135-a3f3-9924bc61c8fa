<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="WhatsBroadcasterProDesktop.Views.SelectiveMessageWindow"
        Title="🎯 إرسال رسائل لأرقام محددة"
        Width="1200" Height="800"
        MinWidth="900" MinHeight="600"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}">

    <Grid Margin="25">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0"
                Background="{StaticResource PrimaryBrush}"
                Padding="25"
                CornerRadius="15"
                BoxShadow="0 4 12 0 #20000000"
                Margin="0,0,0,30">
            <StackPanel HorizontalAlignment="Center">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Spacing="15">
                    <TextBlock Text="🎯" FontSize="36" VerticalAlignment="Center" Foreground="White"/>
                    <TextBlock Text="إرسال رسائل لأرقام محددة"
                               FontSize="28"
                               FontWeight="Bold"
                               VerticalAlignment="Center"
                               Foreground="White"/>
                </StackPanel>
                <TextBlock Text="اختر الأرقام التي تريد إرسال الرسالة إليها بدقة"
                           FontSize="16"
                           HorizontalAlignment="Center"
                           Foreground="White"
                           Opacity="0.9"
                           Margin="0,8,0,0"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Contacts List -->
            <Border Grid.Column="0"
                    Background="{StaticResource SurfaceBrush}"
                    CornerRadius="15"
                    Padding="25"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    BoxShadow="0 4 12 0 #10000000">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Contacts Header -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Spacing="10" Margin="0,0,0,15">
                        <TextBlock Text="👥 قائمة الأرقام" 
                                   FontSize="20" 
                                   FontWeight="Bold"
                                   VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding TotalContactsCount, StringFormat='({0} رقم)'}" 
                                   FontSize="14" 
                                   Foreground="Gray"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Search and Filter -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="10"/>
                            <ColumnDefinition Width="200"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0" 
                                 Text="{Binding SearchText}" 
                                 Watermark="🔍 البحث في الأسماء أو الأرقام..."
                                 Height="40"/>

                        <ComboBox Grid.Column="2" 
                                  ItemsSource="{Binding Groups}"
                                  SelectedItem="{Binding SelectedGroupFilter}"
                                  DisplayMemberBinding="{Binding Name}"
                                  PlaceholderText="تصفية حسب المجموعة"
                                  Height="40"/>
                    </Grid>

                    <!-- Contacts DataGrid -->
                    <DataGrid Grid.Row="2" 
                              ItemsSource="{Binding FilteredContacts}"
                              AutoGenerateColumns="False"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              SelectionMode="Extended"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="True"
                              Background="White"
                              CornerRadius="8">
                        
                        <DataGrid.Columns>
                            <DataGridTemplateColumn Header="✓" Width="50">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <CheckBox IsChecked="{Binding IsSelected}" 
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            
                            <DataGridTextColumn Header="الاسم" 
                                                Binding="{Binding Name}" 
                                                Width="200"/>
                            
                            <DataGridTextColumn Header="رقم الهاتف" 
                                                Binding="{Binding PhoneNumber}" 
                                                Width="150"/>
                            
                            <DataGridTextColumn Header="البريد الإلكتروني" 
                                                Binding="{Binding Email}" 
                                                Width="200"/>
                            
                            <DataGridTextColumn Header="المجموعات" 
                                                Binding="{Binding GroupNames}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Selection Controls -->
                    <StackPanel Grid.Row="3" Orientation="Horizontal" Spacing="10" Margin="0,15,0,0">
                        <Button Content="✅ تحديد الكل" 
                                Command="{Binding SelectAllCommand}"
                                Background="#007ACC"
                                Foreground="White"
                                Padding="10,5"/>
                        
                        <Button Content="❌ إلغاء التحديد" 
                                Command="{Binding UnselectAllCommand}"
                                Background="#6C757D"
                                Foreground="White"
                                Padding="10,5"/>
                        
                        <TextBlock Text="{Binding SelectedContactsCount, StringFormat='المحدد: {0} رقم'}" 
                                   FontWeight="SemiBold"
                                   VerticalAlignment="Center"
                                   Foreground="#28A745"
                                   Margin="20,0,0,0"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Right Panel - Message Composition -->
            <StackPanel Grid.Column="2" Spacing="25">
                
                <!-- Message Composition -->
                <Border Background="#F8F9FA" 
                        CornerRadius="12" 
                        Padding="20"
                        BorderBrush="#E9ECEF"
                        BorderThickness="1">
                    <StackPanel Spacing="15">
                        <StackPanel Orientation="Horizontal" Spacing="10">
                            <TextBlock Text="✍️" FontSize="20"/>
                            <TextBlock Text="كتابة الرسالة" 
                                       FontSize="18" 
                                       FontWeight="Bold"/>
                        </StackPanel>
                        
                        <TextBox Text="{Binding MessageContent}"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 Height="200"
                                 Watermark="اكتب نص الرسالة هنا..."
                                 FontSize="14"/>
                        
                        <Grid>
                            <TextBlock Text="{Binding MessageContent.Length, StringFormat='عدد الأحرف: {0}'}"
                                       FontSize="12"
                                       Foreground="Gray"
                                       HorizontalAlignment="Left"/>
                            <TextBlock Text="💡 اجعل الرسالة واضحة ومختصرة"
                                       FontSize="12"
                                       Foreground="#FFC107"
                                       HorizontalAlignment="Right"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Send Settings -->
                <Border Background="#F8F9FA" 
                        CornerRadius="12" 
                        Padding="20"
                        BorderBrush="#E9ECEF"
                        BorderThickness="1">
                    <StackPanel Spacing="15">
                        <StackPanel Orientation="Horizontal" Spacing="10">
                            <TextBlock Text="⚙️" FontSize="20"/>
                            <TextBlock Text="إعدادات الإرسال" 
                                       FontSize="18" 
                                       FontWeight="Bold"/>
                        </StackPanel>
                        
                        <StackPanel Spacing="10">
                            <TextBlock Text="⏱️ التأخير بين الرسائل (ثانية):" 
                                       FontSize="14" 
                                       FontWeight="SemiBold"/>
                            <NumericUpDown Value="{Binding DelayBetweenMessages}"
                                           Minimum="3"
                                           Maximum="30"
                                           Increment="1"
                                           Height="40"
                                           FontSize="14"/>
                            <TextBlock Text="⚠️ يُنصح بـ 5-10 ثوانٍ لتجنب الحظر" 
                                       FontSize="12" 
                                       Foreground="#DC3545"/>
                        </StackPanel>
                        
                        <CheckBox Content="💾 حفظ الرسالة في سجل الإرسال"
                                  IsChecked="{Binding SaveToHistory}"
                                  FontSize="14"/>
                    </StackPanel>
                </Border>

                <!-- Send Button -->
                <Button Content="🚀 إرسال الرسائل" 
                        Background="#25D366"
                        Foreground="White"
                        FontSize="18"
                        FontWeight="Bold"
                        Height="60"
                        CornerRadius="10"
                        Command="{Binding SendMessagesCommand}"
                        IsEnabled="{Binding CanSendMessages}"/>

                <!-- Progress -->
                <Border Background="#FFF3CD" 
                        CornerRadius="12" 
                        Padding="20"
                        BorderBrush="#FFC107"
                        BorderThickness="1"
                        IsVisible="{Binding IsSending}">
                    <StackPanel Spacing="15">
                        <StackPanel Orientation="Horizontal" Spacing="10">
                            <TextBlock Text="⏳" FontSize="20"/>
                            <TextBlock Text="جاري الإرسال..." 
                                       FontSize="18" 
                                       FontWeight="Bold"
                                       Foreground="#856404"/>
                        </StackPanel>
                        
                        <ProgressBar Value="{Binding SendProgress}"
                                     Maximum="100"
                                     Height="25"
                                     Background="#FFF"
                                     Foreground="#FFC107"/>
                        
                        <TextBlock Text="{Binding SendProgressText}"
                                   HorizontalAlignment="Center"
                                   FontSize="14"
                                   Foreground="#856404"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" 
                Background="#E9ECEF" 
                Padding="15"
                CornerRadius="8"
                Margin="0,20,0,0">
            <Grid>
                <TextBlock Text="{Binding StatusMessage}" 
                           VerticalAlignment="Center"
                           FontSize="14"
                           Foreground="#495057"/>
                <TextBlock Text="💡 نصيحة: اختبر الرسالة على رقم واحد أولاً" 
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="#6C757D"/>
            </Grid>
        </Border>

    </Grid>
</Window>
