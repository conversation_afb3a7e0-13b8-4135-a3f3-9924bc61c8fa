<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:WhatsBroadcasterProDesktop.ViewModels"
        x:Class="WhatsBroadcasterProDesktop.Views.AddContactWindow"
        x:DataType="vm:AddContactViewModel"
        Title="إضافة رقم جديد"
        Width="500" Height="600"
        MinWidth="400" MinHeight="500"
        WindowStartupLocation="CenterScreen"
        CanResize="False">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="➕ إضافة رقم جديد" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Foreground="#007ACC"
                       HorizontalAlignment="Center"/>
            <TextBlock Text="املأ البيانات التالية لإضافة رقم جديد" 
                       FontSize="14" 
                       Foreground="Gray"
                       HorizontalAlignment="Center"
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Form -->
        <ScrollViewer Grid.Row="1">
            <StackPanel Spacing="15">
                
                <!-- Name -->
                <StackPanel>
                    <TextBlock Text="الاسم *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding Name}" 
                             Watermark="أدخل اسم الشخص"
                             Height="40"/>
                </StackPanel>

                <!-- Phone Number -->
                <StackPanel>
                    <TextBlock Text="رقم الهاتف *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding PhoneNumber}" 
                             Watermark="مثال: +966501234567"
                             Height="40"/>
                    <TextBlock Text="يجب أن يبدأ الرقم برمز الدولة (مثل +966)" 
                               FontSize="12" 
                               Foreground="Gray"
                               Margin="0,2,0,0"/>
                </StackPanel>

                <!-- Email -->
                <StackPanel>
                    <TextBlock Text="البريد الإلكتروني" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding Email}" 
                             Watermark="<EMAIL>"
                             Height="40"/>
                </StackPanel>

                <!-- Notes -->
                <StackPanel>
                    <TextBlock Text="ملاحظات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding Notes}" 
                             Watermark="أي ملاحظات إضافية..."
                             AcceptsReturn="True"
                             TextWrapping="Wrap"
                             Height="80"/>
                </StackPanel>

                <!-- Groups -->
                <StackPanel>
                    <TextBlock Text="المجموعات" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <ComboBox ItemsSource="{Binding AvailableGroups}"
                              SelectedItem="{Binding SelectedGroup}"
                              DisplayMemberBinding="{Binding Name}"
                              PlaceholderText="اختر مجموعة (اختياري)"
                              Height="40"/>
                </StackPanel>

                <!-- Validation Message -->
                <TextBlock Text="{Binding ValidationMessage}" 
                           Foreground="Red"
                           FontSize="12"
                           IsVisible="{Binding HasValidationError}"
                           TextWrapping="Wrap"/>

            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" 
                    Content="إلغاء" 
                    Height="40"
                    Background="Gray"
                    Foreground="White"
                    Command="{Binding CancelCommand}"/>

            <Button Grid.Column="2" 
                    Content="حفظ" 
                    Height="40"
                    Background="#28A745"
                    Foreground="White"
                    Command="{Binding SaveCommand}"
                    IsEnabled="{Binding CanSave}"/>
        </Grid>

    </Grid>
</Window>
