<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="WhatsBroadcasterProDesktop.Views.MessageDeliverySimulatorWindow"
        Title="🔄 محاكي حالة التسليم"
        Width="600" Height="400"
        WindowStartupLocation="CenterScreen">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="🔄 محاكي حالة التسليم" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center"
                       Foreground="#25D366"/>
            <TextBlock Text="محاكاة تحديث حالة الرسائل (للاختبار)" 
                       FontSize="14" 
                       HorizontalAlignment="Center"
                       Foreground="Gray"
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Controls -->
        <StackPanel Grid.Row="1" Spacing="20">
            
            <!-- Simulate Delivery -->
            <Border Background="#E8F5E8" 
                    CornerRadius="10" 
                    Padding="20"
                    BorderBrush="#28A745"
                    BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock Text="📬 محاكاة التسليم" 
                               FontSize="18" 
                               FontWeight="Bold"
                               Foreground="#28A745"/>
                    <TextBlock Text="تحديث حالة الرسائل المرسلة إلى 'تم التسليم'" 
                               FontSize="14"/>
                    <Button Content="✅ تحديث إلى تم التسليم" 
                            Background="#28A745"
                            Foreground="White"
                            FontSize="16"
                            Height="45"
                            Command="{Binding SimulateDeliveryCommand}"/>
                </StackPanel>
            </Border>

            <!-- Simulate Read -->
            <Border Background="#F3E5F5" 
                    CornerRadius="10" 
                    Padding="20"
                    BorderBrush="#7B1FA2"
                    BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock Text="👁️ محاكاة القراءة" 
                               FontSize="18" 
                               FontWeight="Bold"
                               Foreground="#7B1FA2"/>
                    <TextBlock Text="تحديث حالة الرسائل المسلمة إلى 'تم القراءة'" 
                               FontSize="14"/>
                    <Button Content="👁️ تحديث إلى تم القراءة" 
                            Background="#7B1FA2"
                            Foreground="White"
                            FontSize="16"
                            Height="45"
                            Command="{Binding SimulateReadCommand}"/>
                </StackPanel>
            </Border>

            <!-- Simulate Failure -->
            <Border Background="#FFEBEE" 
                    CornerRadius="10" 
                    Padding="20"
                    BorderBrush="#DC3545"
                    BorderThickness="1">
                <StackPanel Spacing="15">
                    <TextBlock Text="❌ محاكاة الفشل" 
                               FontSize="18" 
                               FontWeight="Bold"
                               Foreground="#DC3545"/>
                    <TextBlock Text="تحديث بعض الرسائل إلى 'فشل الإرسال'" 
                               FontSize="14"/>
                    <Button Content="❌ محاكاة فشل بعض الرسائل" 
                            Background="#DC3545"
                            Foreground="White"
                            FontSize="16"
                            Height="45"
                            Command="{Binding SimulateFailureCommand}"/>
                </StackPanel>
            </Border>

        </StackPanel>

        <!-- Status -->
        <Border Grid.Row="2" 
                Background="#F8F9FA" 
                Padding="15"
                CornerRadius="8"
                Margin="0,20,0,0">
            <TextBlock Text="{Binding StatusMessage}" 
                       HorizontalAlignment="Center"
                       FontSize="14"
                       FontWeight="SemiBold"/>
        </Border>

    </Grid>
</Window>
