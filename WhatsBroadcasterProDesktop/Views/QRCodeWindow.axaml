<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:WhatsBroadcasterProDesktop.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="700"
        x:Class="WhatsBroadcasterProDesktop.Views.QRCodeWindow"
        x:DataType="vm:QRCodeWindowViewModel"
        Title="🔗 الاتصال بواتساب - QR Code"
        Width="600" Height="700"
        MinWidth="500" MinHeight="600"
        WindowStartupLocation="CenterScreen"
        Background="White"
        CanResize="False">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="#25D366" 
                Padding="20" 
                CornerRadius="10" 
                Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="📱" FontSize="32" VerticalAlignment="Center" Foreground="White"/>
                <TextBlock Text="الاتصال بواتساب ويب" 
                           FontSize="20" 
                           FontWeight="Bold" 
                           Foreground="White" 
                           VerticalAlignment="Center" 
                           Margin="10,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- Instructions -->
        <Border Grid.Row="1" 
                Background="#F8F9FA" 
                Padding="15" 
                CornerRadius="8" 
                Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="📋 خطوات الاتصال:" 
                           FontSize="16" 
                           FontWeight="Bold" 
                           Margin="0,0,0,10"/>
                <TextBlock Text="1️⃣ افتح تطبيق واتساب على هاتفك" FontSize="14" Margin="0,0,0,5"/>
                <TextBlock Text="2️⃣ اذهب إلى الإعدادات ← الأجهزة المرتبطة" FontSize="14" Margin="0,0,0,5"/>
                <TextBlock Text="3️⃣ اضغط على 'ربط جهاز'" FontSize="14" Margin="0,0,0,5"/>
                <TextBlock Text="4️⃣ امسح رمز QR أدناه بكاميرا هاتفك" FontSize="14" Margin="0,0,0,5"/>
                <TextBlock Text="5️⃣ انتظر حتى يتم الاتصال بنجاح" FontSize="14"/>
            </StackPanel>
        </Border>

        <!-- QR Code Display Area -->
        <Border Grid.Row="2" 
                Background="White" 
                BorderBrush="#E0E0E0" 
                BorderThickness="2" 
                CornerRadius="10" 
                Padding="20">
            <Grid>
                <!-- QR Code Image -->
                <Image Name="QRCodeImage" 
                       Source="{Binding QRCodeImage}" 
                       Stretch="Uniform" 
                       MaxWidth="300" 
                       MaxHeight="300"
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"
                       IsVisible="{Binding IsQRCodeVisible}"/>

                <!-- Loading Indicator -->
                <StackPanel HorizontalAlignment="Center" 
                            VerticalAlignment="Center"
                            IsVisible="{Binding IsLoading}">
                    <TextBlock Text="⏳" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="جاري تحميل رمز QR..." 
                               FontSize="16" 
                               HorizontalAlignment="Center"/>
                    <ProgressBar IsIndeterminate="True" 
                                 Width="200" 
                                 Height="4" 
                                 Margin="0,10,0,0"/>
                </StackPanel>

                <!-- Error Message -->
                <StackPanel HorizontalAlignment="Center" 
                            VerticalAlignment="Center"
                            IsVisible="{Binding HasError}">
                    <TextBlock Text="❌" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding ErrorMessage}" 
                               FontSize="14" 
                               HorizontalAlignment="Center"
                               TextWrapping="Wrap"
                               MaxWidth="300"/>
                </StackPanel>

                <!-- Success Message -->
                <StackPanel HorizontalAlignment="Center" 
                            VerticalAlignment="Center"
                            IsVisible="{Binding IsConnected}">
                    <TextBlock Text="✅" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="تم الاتصال بنجاح!" 
                               FontSize="18" 
                               FontWeight="Bold"
                               HorizontalAlignment="Center" 
                               Foreground="#25D366"/>
                    <TextBlock Text="يمكنك الآن إغلاق هذه النافذة" 
                               FontSize="14" 
                               HorizontalAlignment="Center" 
                               Margin="0,5,0,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Status -->
        <Border Grid.Row="3" 
                Background="#E3F2FD" 
                Padding="15" 
                CornerRadius="8" 
                Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="📊" FontSize="16" VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBlock Text="الحالة: " FontSize="14" VerticalAlignment="Center"/>
                <TextBlock Text="{Binding StatusMessage}" 
                           FontSize="14" 
                           FontWeight="Bold" 
                           VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="4" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Spacing="15" 
                    Margin="0,20,0,0">
            
            <Button Content="🔄 تحديث QR" 
                    Classes="primary"
                    Command="{Binding RefreshQRCommand}"
                    IsEnabled="{Binding !IsLoading}"/>
            
            <Button Content="🌐 فتح في المتصفح" 
                    Classes="secondary"
                    Command="{Binding OpenInBrowserCommand}"
                    IsEnabled="{Binding !IsLoading}"/>
            
            <Button Content="❌ إلغاء" 
                    Classes="danger"
                    Command="{Binding CancelCommand}"/>
        </StackPanel>
    </Grid>
</Window>
