<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:WhatsBroadcasterProDesktop.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="1000" d:DesignHeight="700"
        x:Class="WhatsBroadcasterProDesktop.Views.ContactsWindow"
        x:DataType="vm:ContactsWindowViewModel"
        Title="إدارة أرقام الواتساب"
        Width="1000" Height="700"
        MinWidth="800" MinHeight="500"
        WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="👥" FontSize="24" VerticalAlignment="Center" Foreground="White"/>
                    <TextBlock Text="إدارة أرقام الواتساب" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               VerticalAlignment="Center" 
                               Margin="10,0,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
                    <Button Content="➕ إضافة رقم" 
                            Classes="success"
                            Command="{Binding AddContactCommand}"/>
                    <Button Content="📁 استيراد" 
                            Classes="primary"
                            Command="{Binding ImportContactsCommand}"/>
                    <Button Content="💾 تصدير" 
                            Classes="primary"
                            Command="{Binding ExportContactsCommand}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Search and Filter -->
        <Border Grid.Row="1" Background="#F8F9FA" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Grid.Column="0" 
                         Text="{Binding SearchText}"
                         Watermark="البحث في الأسماء أو الأرقام..."
                         Margin="0,0,10,0"/>
                
                <ComboBox Grid.Column="1" 
                          ItemsSource="{Binding Groups}"
                          SelectedItem="{Binding SelectedGroup}"
                          DisplayMemberBinding="{Binding Name}"
                          PlaceholderText="تصفية حسب المجموعة"
                          Margin="0,0,10,0"/>
                
                <Button Grid.Column="2" 
                        Content="🔍 بحث" 
                        Classes="primary"
                        Command="{Binding SearchCommand}"/>
            </Grid>
        </Border>

        <!-- Contacts List -->
        <DataGrid Grid.Row="2" 
                  ItemsSource="{Binding FilteredContacts}"
                  SelectedItem="{Binding SelectedContact}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  Margin="15">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="200"/>
                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="150"/>
                <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="200"/>
                <DataGridTextColumn Header="المجموعات" Binding="{Binding GroupNames}" Width="*"/>
                <DataGridTextColumn Header="تاريخ الإضافة" Binding="{Binding CreatedAt, StringFormat='yyyy/MM/dd'}" Width="120"/>
                <DataGridTemplateColumn Header="الإجراءات" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" Spacing="5">
                                <Button Content="✏️" 
                                        Classes="primary"
                                        FontSize="12"
                                        Padding="8,4"
                                        Command="{Binding $parent[DataGrid].((vm:ContactsWindowViewModel)DataContext).EditContactCommand}"
                                        CommandParameter="{Binding}"/>
                                <Button Content="🗑️" 
                                        Classes="danger"
                                        FontSize="12"
                                        Padding="8,4"
                                        Command="{Binding $parent[DataGrid].((vm:ContactsWindowViewModel)DataContext).DeleteContactCommand}"
                                        CommandParameter="{Binding}"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Status Bar -->
        <Border Grid.Row="3" Background="#F5F5F5" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="{Binding StatusMessage}" 
                           VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" 
                           Text="{Binding ContactsCount, StringFormat='إجمالي الأرقام: {0}'}" 
                           VerticalAlignment="Center"/>
            </Grid>
        </Border>

    </Grid>
</Window>
