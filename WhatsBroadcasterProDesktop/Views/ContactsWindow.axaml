<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="WhatsBroadcasterProDesktop.Views.ContactsWindow"
        Title="إدارة أرقام الواتساب"
        Width="800" Height="600"
        MinWidth="600" MinHeight="400"
        WindowStartupLocation="CenterScreen">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="📱 إدارة أرقام الواتساب" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center"
                       Foreground="#007ACC"/>
        </StackPanel>

        <!-- Add Contact Button -->
        <Button Grid.Row="1" 
                Content="➕ إضافة رقم جديد" 
                Height="50"
                FontSize="16"
                Background="#28A745"
                Foreground="White"
                HorizontalAlignment="Center"
                Margin="0,0,0,20"
                Command="{Binding AddContactCommand}"/>

        <!-- Contacts List Placeholder -->
        <Border Grid.Row="2" 
                Background="#F8F9FA" 
                CornerRadius="8"
                Padding="20">
            <StackPanel Spacing="15">
                <TextBlock Text="📋 قائمة الأرقام" 
                           FontSize="18" 
                           FontWeight="SemiBold"
                           HorizontalAlignment="Center"/>
                
                <TextBlock Text="لا توجد أرقام محفوظة حالياً" 
                           HorizontalAlignment="Center"
                           Foreground="Gray"/>
                
                <TextBlock Text="انقر على 'إضافة رقم جديد' لبدء إضافة الأرقام" 
                           HorizontalAlignment="Center"
                           Foreground="Gray"
                           FontSize="12"/>
            </StackPanel>
        </Border>

        <!-- Status Bar -->
        <TextBlock Grid.Row="3" 
                   Text="{Binding StatusMessage}" 
                   HorizontalAlignment="Center"
                   Margin="0,20,0,0"
                   Foreground="Blue"/>

    </Grid>
</Window>
