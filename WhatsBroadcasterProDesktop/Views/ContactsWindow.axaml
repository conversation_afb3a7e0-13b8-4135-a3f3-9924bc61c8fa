<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:WhatsBroadcasterProDesktop.ViewModels"
        x:Class="WhatsBroadcasterProDesktop.Views.ContactsWindow"
        Title="إدارة أرقام الواتساب"
        Width="800" Height="600"
        MinWidth="600" MinHeight="400"
        WindowStartupLocation="CenterScreen"
        Background="White">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="📱 إدارة أرقام الواتساب" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center"
                       Foreground="#007ACC"/>
        </StackPanel>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Spacing="15" Margin="0,0,0,20">
            <Button Content="➕ إضافة رقم جديد"
                    Height="50"
                    FontSize="16"
                    Classes="success"
                    Command="{Binding AddContactCommand}"/>

            <Button Content="📤 تصدير إلى Excel"
                    Height="50"
                    FontSize="16"
                    Classes="primary"
                    Command="{Binding ExportToExcelCommand}"/>

            <Button Content="📥 استيراد من Excel"
                    Height="50"
                    FontSize="16"
                    Classes="secondary"
                    Command="{Binding ImportFromExcelCommand}"/>

            <Button Content="📄 تصدير إلى CSV"
                    Height="50"
                    FontSize="16"
                    Classes="primary"
                    Command="{Binding ExportToCsvCommand}"/>

            <Button Content="📁 استيراد من CSV"
                    Height="50"
                    FontSize="16"
                    Classes="secondary"
                    Command="{Binding ImportFromCsvCommand}"/>
        </StackPanel>

        <!-- Contacts List -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Search and Filter -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Spacing="10" Margin="0,0,0,15">
                <TextBlock Text="🔍 البحث:"
                           VerticalAlignment="Center"
                           FontWeight="SemiBold"/>
                <TextBox Name="SearchTextBox"
                         Watermark="ابحث بالاسم أو رقم الهاتف..."
                         Width="300"
                         Text="{Binding SearchText}"/>
                <Button Content="🔄 تحديث"
                        Command="{Binding RefreshCommand}"
                        Classes="secondary"
                        Padding="10,5"/>
            </StackPanel>

            <!-- Contacts DataGrid -->
            <Border Grid.Row="1"
                    Background="White"
                    CornerRadius="8"
                    BorderBrush="#DDD"
                    BorderThickness="1">
                <Grid>
                    <!-- DataGrid for contacts -->
                    <DataGrid ItemsSource="{Binding FilteredContacts}"
                              SelectedItem="{Binding SelectedContact}"
                              AutoGenerateColumns="False"
                              IsReadOnly="False"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              SelectionMode="Single"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              IsVisible="{Binding HasContacts}">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الاسم"
                                                Binding="{Binding Name}"
                                                Width="200"
                                                IsReadOnly="False"/>

                            <DataGridTextColumn Header="رقم الهاتف"
                                                Binding="{Binding PhoneNumber}"
                                                Width="150"
                                                IsReadOnly="False"/>

                            <DataGridTextColumn Header="البريد الإلكتروني"
                                                Binding="{Binding Email}"
                                                Width="200"
                                                IsReadOnly="False"/>

                            <DataGridTextColumn Header="المجموعات"
                                                Binding="{Binding GroupNames}"
                                                Width="*"
                                                IsReadOnly="True"/>

                            <DataGridTemplateColumn Header="الإجراءات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" Spacing="5">
                                            <Button Content="✏️"
                                                    Classes="primary"
                                                    FontSize="12"
                                                    Padding="8,4"
                                                    ToolTip.Tip="تعديل"
                                                    Command="{Binding $parent[Window].DataContext.EditContactCommand}"
                                                    CommandParameter="{Binding}"/>
                                            <Button Content="🗑️"
                                                    Classes="danger"
                                                    FontSize="12"
                                                    Padding="8,4"
                                                    ToolTip.Tip="حذف"
                                                    Command="{Binding $parent[Window].DataContext.DeleteContactCommand}"
                                                    CommandParameter="{Binding}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- Empty State -->
                    <StackPanel IsVisible="{Binding !HasContacts}"
                                VerticalAlignment="Center"
                                HorizontalAlignment="Center"
                                Spacing="15">
                        <TextBlock Text="📋 قائمة الأرقام فارغة"
                                   FontSize="18"
                                   FontWeight="SemiBold"
                                   HorizontalAlignment="Center"
                                   Foreground="Gray"/>

                        <TextBlock Text="لا توجد أرقام محفوظة حالياً"
                                   HorizontalAlignment="Center"
                                   Foreground="Gray"/>

                        <TextBlock Text="انقر على 'إضافة رقم جديد' لبدء إضافة الأرقام"
                                   HorizontalAlignment="Center"
                                   Foreground="Gray"
                                   FontSize="12"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <TextBlock Grid.Row="3" 
                   Text="{Binding StatusMessage}" 
                   HorizontalAlignment="Center"
                   Margin="0,20,0,0"
                   Foreground="Blue"/>

    </Grid>
</Window>
