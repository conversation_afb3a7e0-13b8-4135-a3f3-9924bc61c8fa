<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:WhatsBroadcasterProDesktop.ViewModels"
        x:Class="WhatsBroadcasterProDesktop.Views.GroupsWindow"
        x:DataType="vm:GroupsWindowViewModel"
        Title="إدارة المجموعات"
        Width="800" Height="600"
        WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource SecondaryBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📁" FontSize="24" VerticalAlignment="Center" Foreground="White"/>
                    <TextBlock Text="إدارة مجموعات الأرقام" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               VerticalAlignment="Center" 
                               Margin="10,0,0,0"/>
                </StackPanel>
                
                <Button Grid.Column="1" 
                        Content="➕ إضافة مجموعة" 
                        Classes="success"
                        Command="{Binding AddGroupCommand}"/>
            </Grid>
        </Border>

        <!-- Groups List -->
        <DataGrid Grid.Row="1" 
                  ItemsSource="{Binding Groups}"
                  SelectedItem="{Binding SelectedGroup}"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  Margin="15">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="اسم المجموعة" Binding="{Binding Name}" Width="200"/>
                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                <DataGridTextColumn Header="عدد الأرقام" Binding="{Binding ContactGroups.Count}" Width="100"/>
                <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedAt, StringFormat='yyyy/MM/dd'}" Width="120"/>
                <DataGridTemplateColumn Header="الإجراءات" Width="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" Spacing="5">
                                <Button Content="✏️" 
                                        Classes="primary"
                                        FontSize="12"
                                        Padding="8,4"
                                        Command="{Binding $parent[DataGrid].((vm:GroupsWindowViewModel)DataContext).EditGroupCommand}"
                                        CommandParameter="{Binding}"/>
                                <Button Content="🗑️" 
                                        Classes="danger"
                                        FontSize="12"
                                        Padding="8,4"
                                        Command="{Binding $parent[DataGrid].((vm:GroupsWindowViewModel)DataContext).DeleteGroupCommand}"
                                        CommandParameter="{Binding}"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="10">
            <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
        </Border>

    </Grid>
</Window>
