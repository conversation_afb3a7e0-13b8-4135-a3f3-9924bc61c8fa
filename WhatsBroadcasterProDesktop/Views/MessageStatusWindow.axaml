<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="WhatsBroadcasterProDesktop.Views.MessageStatusWindow"
        Title="📊 تقرير حالة الرسائل"
        Width="1200" Height="800"
        MinWidth="900" MinHeight="600"
        WindowStartupLocation="CenterScreen">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="📊 تقرير حالة الرسائل المرسلة" 
                       FontSize="28" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center"
                       Foreground="#25D366"/>
            <TextBlock Text="تتبع حالة وصول الرسائل لكل رقم" 
                       FontSize="14" 
                       HorizontalAlignment="Center"
                       Foreground="Gray"
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Statistics Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Messages -->
            <Border Grid.Column="0" 
                    Background="#E3F2FD" 
                    CornerRadius="10" 
                    Padding="15"
                    Margin="0,0,5,0">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📤" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalMessages}" 
                               FontSize="20" 
                               FontWeight="Bold"
                               HorizontalAlignment="Center"
                               Foreground="#1976D2"/>
                    <TextBlock Text="إجمالي الرسائل" 
                               FontSize="12"
                               HorizontalAlignment="Center"
                               Foreground="#1976D2"/>
                </StackPanel>
            </Border>

            <!-- Sent Messages -->
            <Border Grid.Column="1" 
                    Background="#E8F5E8" 
                    CornerRadius="10" 
                    Padding="15"
                    Margin="5,0,5,0">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding SentMessages}" 
                               FontSize="20" 
                               FontWeight="Bold"
                               HorizontalAlignment="Center"
                               Foreground="#388E3C"/>
                    <TextBlock Text="تم الإرسال" 
                               FontSize="12"
                               HorizontalAlignment="Center"
                               Foreground="#388E3C"/>
                </StackPanel>
            </Border>

            <!-- Delivered Messages -->
            <Border Grid.Column="2" 
                    Background="#E1F5FE" 
                    CornerRadius="10" 
                    Padding="15"
                    Margin="5,0,5,0">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="📬" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding DeliveredMessages}" 
                               FontSize="20" 
                               FontWeight="Bold"
                               HorizontalAlignment="Center"
                               Foreground="#0288D1"/>
                    <TextBlock Text="تم التسليم" 
                               FontSize="12"
                               HorizontalAlignment="Center"
                               Foreground="#0288D1"/>
                </StackPanel>
            </Border>

            <!-- Read Messages -->
            <Border Grid.Column="3" 
                    Background="#F3E5F5" 
                    CornerRadius="10" 
                    Padding="15"
                    Margin="5,0,5,0">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="👁️" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding ReadMessages}" 
                               FontSize="20" 
                               FontWeight="Bold"
                               HorizontalAlignment="Center"
                               Foreground="#7B1FA2"/>
                    <TextBlock Text="تم القراءة" 
                               FontSize="12"
                               HorizontalAlignment="Center"
                               Foreground="#7B1FA2"/>
                </StackPanel>
            </Border>

            <!-- Failed Messages -->
            <Border Grid.Column="4" 
                    Background="#FFEBEE" 
                    CornerRadius="10" 
                    Padding="15"
                    Margin="5,0,0,0">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock Text="❌" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding FailedMessages}" 
                               FontSize="20" 
                               FontWeight="Bold"
                               HorizontalAlignment="Center"
                               Foreground="#D32F2F"/>
                    <TextBlock Text="فشل الإرسال" 
                               FontSize="12"
                               HorizontalAlignment="Center"
                               Foreground="#D32F2F"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Messages List -->
        <Border Grid.Row="2" 
                Background="#F8F9FA" 
                CornerRadius="12" 
                Padding="20"
                BorderBrush="#E9ECEF"
                BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Filter Controls -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Spacing="15" Margin="0,0,0,15">
                    <TextBlock Text="🔍 تصفية النتائج:" 
                               FontWeight="SemiBold" 
                               VerticalAlignment="Center"/>
                    
                    <ComboBox ItemsSource="{Binding BroadcastMessages}"
                              SelectedItem="{Binding SelectedBroadcast}"
                              DisplayMemberBinding="{Binding Content}"
                              PlaceholderText="اختر رسالة محددة"
                              Width="200"/>
                    
                    <ComboBox ItemsSource="{Binding StatusFilters}"
                              SelectedItem="{Binding SelectedStatusFilter}"
                              PlaceholderText="حالة الرسالة"
                              Width="150"/>
                    
                    <Button Content="🔄 تحديث" 
                            Command="{Binding RefreshCommand}"
                            Background="#007ACC"
                            Foreground="White"
                            Padding="10,5"/>
                </StackPanel>

                <!-- Column Headers -->
                <Border Grid.Row="1" Background="#E9ECEF" CornerRadius="5" Margin="0,0,0,10">
                    <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="100"/>
                    </Grid.ColumnDefinitions>
                    
                        <TextBlock Grid.Column="0" Text="اسم المستقبل" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" Text="رقم الهاتف" FontWeight="Bold"/>
                        <TextBlock Grid.Column="2" Text="محتوى الرسالة" FontWeight="Bold"/>
                        <TextBlock Grid.Column="3" Text="حالة التسليم" FontWeight="Bold"/>
                        <TextBlock Grid.Column="4" Text="وقت الإرسال" FontWeight="Bold"/>
                        <TextBlock Grid.Column="5" Text="المحاولات" FontWeight="Bold"/>
                    </Grid>
                </Border>

                <!-- Messages DataGrid -->
                <ScrollViewer Grid.Row="2">
                    <ItemsControl ItemsSource="{Binding FilteredMessageLogs}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="White" 
                                        CornerRadius="5" 
                                        Padding="10" 
                                        Margin="0,0,0,5"
                                        BorderBrush="#DDD"
                                        BorderThickness="1">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="200"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="100"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" 
                                                   Text="{Binding Contact.Name}" 
                                                   VerticalAlignment="Center"/>
                                        
                                        <TextBlock Grid.Column="1" 
                                                   Text="{Binding Contact.PhoneNumber}" 
                                                   VerticalAlignment="Center"/>
                                        
                                        <TextBlock Grid.Column="2" 
                                                   Text="{Binding BroadcastMessage.Content}" 
                                                   TextWrapping="Wrap"
                                                   VerticalAlignment="Center"
                                                   MaxHeight="50"/>
                                        
                                        <StackPanel Grid.Column="3" VerticalAlignment="Center">
                                            <Border Background="{Binding StatusColor}" 
                                                    CornerRadius="10" 
                                                    Padding="8,4">
                                                <TextBlock Text="{Binding StatusText}" 
                                                           Foreground="White"
                                                           FontSize="11"
                                                           FontWeight="SemiBold"
                                                           HorizontalAlignment="Center"/>
                                            </Border>
                                        </StackPanel>
                                        
                                        <TextBlock Grid.Column="4" 
                                                   Text="{Binding SentAt, StringFormat='yyyy/MM/dd HH:mm'}" 
                                                   VerticalAlignment="Center"
                                                   FontSize="12"/>
                                        
                                        <TextBlock Grid.Column="5" 
                                                   Text="{Binding RetryCount}" 
                                                   VerticalAlignment="Center"
                                                   HorizontalAlignment="Center"
                                                   FontWeight="SemiBold"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="3" 
                Background="#E9ECEF" 
                Padding="15"
                CornerRadius="8"
                Margin="0,20,0,0">
            <Grid>
                <TextBlock Text="{Binding StatusMessage}" 
                           VerticalAlignment="Center"
                           FontSize="14"/>
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Right"
                            Spacing="20">
                    <TextBlock Text="{Binding DeliveryRate, StringFormat='معدل التسليم: {0:P0}'}" 
                               FontWeight="SemiBold"
                               Foreground="#28A745"/>
                    <TextBlock Text="{Binding LastUpdated, StringFormat='آخر تحديث: {0:HH:mm}'}" 
                               FontSize="12"
                               Foreground="#6C757D"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</Window>
