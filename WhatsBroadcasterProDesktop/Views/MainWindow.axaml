<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:WhatsBroadcasterProDesktop.ViewModels"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="1200" d:DesignHeight="800"
        x:Class="WhatsBroadcasterProDesktop.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"

        Title="WhatsBroadcaster Pro - إدارة رسائل الواتساب الجماعية"
        Width="1200" Height="800"
        MinWidth="800" MinHeight="600"
        WindowStartupLocation="CenterScreen">

    <Design.DataContext>
        <!-- This only sets the DataContext for the previewer in an IDE,
             to set the actual DataContext for runtime, set the DataContext property in code (look at App.axaml.cs) -->
        <vm:MainWindowViewModel/>
    </Design.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📱" FontSize="32" VerticalAlignment="Center" Foreground="White"/>
                    <TextBlock Text="WhatsBroadcaster Pro" 
                               FontSize="24" 
                               FontWeight="Bold" 
                               Foreground="White" 
                               VerticalAlignment="Center" 
                               Margin="12,0,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="10">
                    <TextBlock Text="{Binding ConnectionStatus}" 
                               FontSize="14" 
                               Foreground="White" 
                               VerticalAlignment="Center"/>
                    <Button Content="🔗 اتصال واتساب" 
                            Classes="success"
                            Command="{Binding ConnectWhatsAppCommand}"
                            IsVisible="{Binding !IsConnected}"/>
                    <Button Content="🔌 قطع الاتصال" 
                            Classes="danger"
                            Command="{Binding DisconnectWhatsAppCommand}"
                            IsVisible="{Binding IsConnected}"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Dashboard Cards -->
            <TextBlock Grid.Row="0" Text="لوحة التحكم الرئيسية" Classes="header"/>
            
            <ScrollViewer Grid.Row="1">
                <StackPanel Spacing="20">
                    
                    <!-- Statistics Cards -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Contacts Card -->
                        <Border Grid.Column="0" Classes="card">
                            <StackPanel>
                                <TextBlock Text="👥" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="إدارة الأرقام" Classes="subheader" HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding ContactsCount, StringFormat='\{0\} رقم محفوظ'}"
                                           FontSize="14" 
                                           HorizontalAlignment="Center" 
                                           Margin="0,0,0,15"/>
                                <Button Content="إدارة الأرقام" 
                                        Classes="primary" 
                                        HorizontalAlignment="Stretch"
                                        Command="{Binding OpenContactsCommand}"/>
                            </StackPanel>
                        </Border>

                        <!-- Groups Card -->
                        <Border Grid.Column="1" Classes="card">
                            <StackPanel>
                                <TextBlock Text="📁" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="إدارة المجموعات" Classes="subheader" HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding GroupsCount, StringFormat='\{0\} مجموعة'}"
                                           FontSize="14" 
                                           HorizontalAlignment="Center" 
                                           Margin="0,0,0,15"/>
                                <Button Content="إدارة المجموعات" 
                                        Classes="success" 
                                        HorizontalAlignment="Stretch"
                                        Command="{Binding OpenGroupsCommand}"/>
                            </StackPanel>
                        </Border>

                        <!-- Broadcast Card -->
                        <Border Grid.Column="2" Classes="card">
                            <StackPanel>
                                <TextBlock Text="📢" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="الإرسال الجماعي" Classes="subheader" HorizontalAlignment="Center"/>
                                <TextBlock Text="إرسال رسائل للمجموعات" 
                                           FontSize="14" 
                                           HorizontalAlignment="Center" 
                                           Margin="0,0,0,15"/>
                                <Button Content="إرسال رسائل" 
                                        Classes="danger" 
                                        HorizontalAlignment="Stretch"
                                        Command="{Binding OpenBroadcastCommand}"/>
                            </StackPanel>
                        </Border>

                        <!-- Statistics Card -->
                        <Border Grid.Column="3" Classes="card">
                            <StackPanel>
                                <TextBlock Text="📊" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="الإحصائيات" Classes="subheader" HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding MessagesCount, StringFormat='\{0\} رسالة مرسلة'}"
                                           FontSize="14" 
                                           HorizontalAlignment="Center" 
                                           Margin="0,0,0,15"/>
                                <Button Content="عرض التفاصيل" 
                                        Classes="primary" 
                                        HorizontalAlignment="Stretch"
                                        Command="{Binding RefreshDataCommand}"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- Recent Messages -->
                    <Border Classes="card">
                        <StackPanel>
                            <TextBlock Text="الرسائل الأخيرة" Classes="subheader"/>
                            <DataGrid ItemsSource="{Binding RecentMessages}" 
                                      AutoGenerateColumns="False"
                                      IsReadOnly="True"
                                      GridLinesVisibility="Horizontal"
                                      HeadersVisibility="Column"
                                      Height="200">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="المحتوى" Binding="{Binding Content}" Width="*"/>
                                    <DataGridTextColumn Header="المجموعة" Binding="{Binding Group.Name}" Width="150"/>
                                    <DataGridTextColumn Header="التاريخ" Binding="{Binding CreatedAt, StringFormat='yyyy/MM/dd HH:mm'}" Width="150"/>
                                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                                    <DataGridTextColumn Header="نجح" Binding="{Binding SuccessfulSends}" Width="80"/>
                                    <DataGridTextColumn Header="فشل" Binding="{Binding FailedSends}" Width="80"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
                <TextBlock Grid.Column="1" Text="{Binding CurrentTime}" VerticalAlignment="Center"/>
            </Grid>
        </Border>

    </Grid>
</Window>
