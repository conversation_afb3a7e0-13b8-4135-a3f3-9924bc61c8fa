<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="WhatsBroadcasterProDesktop.Views.DesignShowcaseWindow"
        Title="🎨 التصميم الجديد المحسن"
        Width="800" Height="600"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}">

    <ScrollViewer Padding="30">
        <StackPanel Spacing="25">
            
            <!-- Header -->
            <Border Background="{StaticResource PrimaryBrush}" 
                    CornerRadius="15" 
                    Padding="25"
                    BoxShadow="0 4 12 0 #20000000">
                <StackPanel HorizontalAlignment="Center">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Spacing="15">
                        <TextBlock Text="🎨" FontSize="36" Foreground="White"/>
                        <TextBlock Text="التصميم الجديد المحسن" 
                                   FontSize="28" 
                                   FontWeight="Bold" 
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                    <TextBlock Text="تحسينات شاملة للواجهة والتناسق البصري" 
                               FontSize="16" 
                               HorizontalAlignment="Center"
                               Foreground="White"
                               Opacity="0.9"
                               Margin="0,8,0,0"/>
                </StackPanel>
            </Border>

            <!-- Color Palette -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    CornerRadius="15" 
                    Padding="25"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    BoxShadow="0 4 12 0 #10000000">
                <StackPanel Spacing="20">
                    <TextBlock Text="🌈 لوحة الألوان الجديدة" 
                               FontSize="20" 
                               FontWeight="Bold"
                               Foreground="{StaticResource TextPrimaryBrush}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Primary -->
                        <Border Grid.Column="0" 
                                Background="{StaticResource PrimaryBrush}" 
                                CornerRadius="10" 
                                Height="80"
                                Margin="5">
                            <TextBlock Text="أساسي" 
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                        
                        <!-- Secondary -->
                        <Border Grid.Column="1" 
                                Background="{StaticResource SecondaryBrush}" 
                                CornerRadius="10" 
                                Height="80"
                                Margin="5">
                            <TextBlock Text="ثانوي" 
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                        
                        <!-- Success -->
                        <Border Grid.Column="2" 
                                Background="{StaticResource SuccessBrush}" 
                                CornerRadius="10" 
                                Height="80"
                                Margin="5">
                            <TextBlock Text="نجاح" 
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                        
                        <!-- Warning -->
                        <Border Grid.Column="3" 
                                Background="{StaticResource WarningBrush}" 
                                CornerRadius="10" 
                                Height="80"
                                Margin="5">
                            <TextBlock Text="تحذير" 
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                        
                        <!-- Danger -->
                        <Border Grid.Column="4" 
                                Background="{StaticResource DangerBrush}" 
                                CornerRadius="10" 
                                Height="80"
                                Margin="5">
                            <TextBlock Text="خطر" 
                                       HorizontalAlignment="Center"
                                       VerticalAlignment="Center"
                                       Foreground="White"
                                       FontWeight="Bold"/>
                        </Border>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Button Styles -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    CornerRadius="15" 
                    Padding="25"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    BoxShadow="0 4 12 0 #10000000">
                <StackPanel Spacing="20">
                    <TextBlock Text="🔘 أنماط الأزرار المحسنة" 
                               FontSize="20" 
                               FontWeight="Bold"
                               Foreground="{StaticResource TextPrimaryBrush}"/>
                    
                    <StackPanel Orientation="Horizontal" Spacing="15">
                        <Button Content="أساسي" Classes="primary"/>
                        <Button Content="ثانوي" Classes="secondary"/>
                        <Button Content="نجاح" Classes="success"/>
                        <Button Content="تحذير" Classes="warning"/>
                        <Button Content="خطر" Classes="danger"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Typography -->
            <Border Background="{StaticResource SurfaceBrush}" 
                    CornerRadius="15" 
                    Padding="25"
                    BorderBrush="{StaticResource BorderBrush}"
                    BorderThickness="1"
                    BoxShadow="0 4 12 0 #10000000">
                <StackPanel Spacing="15">
                    <TextBlock Text="📝 التحسينات النصية" 
                               FontSize="20" 
                               FontWeight="Bold"
                               Foreground="{StaticResource TextPrimaryBrush}"/>
                    
                    <TextBlock Text="عنوان رئيسي" Classes="header"/>
                    <TextBlock Text="عنوان فرعي" Classes="subheader"/>
                    <TextBlock Text="نص عادي مع تحسينات في القراءة والوضوح" Classes="body"/>
                    <TextBlock Text="نص توضيحي" Classes="caption"/>
                    <TextBlock Text="نص مساعد" Classes="hint"/>
                </StackPanel>
            </Border>

            <!-- Features -->
            <Border Background="{StaticResource InfoBrush}" 
                    CornerRadius="15" 
                    Padding="25"
                    BoxShadow="0 4 12 0 #20000000">
                <StackPanel Spacing="15">
                    <StackPanel Orientation="Horizontal" Spacing="12">
                        <TextBlock Text="✨" FontSize="24" Foreground="White"/>
                        <TextBlock Text="المميزات الجديدة" 
                                   FontSize="20" 
                                   FontWeight="Bold"
                                   Foreground="White"/>
                    </StackPanel>
                    
                    <StackPanel Spacing="8">
                        <TextBlock Text="• ألوان متناسقة ومريحة للعين" 
                                   FontSize="14" 
                                   Foreground="White"/>
                        <TextBlock Text="• تباين واضح بين النصوص والخلفيات" 
                                   FontSize="14" 
                                   Foreground="White"/>
                        <TextBlock Text="• ظلال وتأثيرات بصرية جميلة" 
                                   FontSize="14" 
                                   Foreground="White"/>
                        <TextBlock Text="• تصميم عصري ومتجاوب" 
                                   FontSize="14" 
                                   Foreground="White"/>
                        <TextBlock Text="• أزرار واضحة وسهلة الاستخدام" 
                                   FontSize="14" 
                                   Foreground="White"/>
                    </StackPanel>
                </StackPanel>
            </Border>

        </StackPanel>
    </ScrollViewer>
</Window>
