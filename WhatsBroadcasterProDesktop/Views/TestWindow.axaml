<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="WhatsBroadcasterProDesktop.Views.TestWindow"
        Title="نافذة اختبار"
        Width="400" Height="300"
        WindowStartupLocation="CenterScreen">

    <StackPanel Margin="20" Spacing="20">
        <TextBlock Text="نافذة اختبار" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   HorizontalAlignment="Center"/>
        
        <TextBlock Text="إذا ظهرت هذه النافذة، فإن النظام يعمل بشكل صحيح" 
                   TextWrapping="Wrap"
                   HorizontalAlignment="Center"/>
        
        <Button Content="إغلاق" 
                HorizontalAlignment="Center"
                Click="CloseButton_Click"/>
    </StackPanel>
</Window>
