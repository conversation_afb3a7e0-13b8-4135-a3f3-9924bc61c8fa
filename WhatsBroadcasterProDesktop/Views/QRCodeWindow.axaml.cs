using Avalonia.Controls;
using WhatsBroadcasterProDesktop.ViewModels;
using System;

namespace WhatsBroadcasterProDesktop.Views
{
    public partial class QRCodeWindow : Window
    {
        public QRCodeWindow()
        {
            InitializeComponent();
        }

        public QRCodeWindow(QRCodeWindowViewModel viewModel) : this()
        {
            DataContext = viewModel;
            
            // Subscribe to events
            viewModel.ConnectionSuccessful += OnConnectionSuccessful;
            viewModel.WindowCloseRequested += OnWindowCloseRequested;
        }

        private void OnConnectionSuccessful(object? sender, EventArgs e)
        {
            // Auto-close window after successful connection
            Close();
        }

        private void OnWindowCloseRequested(object? sender, EventArgs e)
        {
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            // Clean up event subscriptions
            if (DataContext is QRCodeWindowViewModel viewModel)
            {
                viewModel.ConnectionSuccessful -= OnConnectionSuccessful;
                viewModel.WindowCloseRequested -= OnWindowCloseRequested;
                viewModel.Cleanup();
            }

            base.OnClosed(e);
        }
    }
}
