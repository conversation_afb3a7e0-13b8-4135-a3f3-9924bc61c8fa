<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="WhatsBroadcasterProDesktop.Views.BroadcastWindow"
        Title="📢 الإرسال الجماعي - واتساب"
        Width="1000" Height="800"
        MinWidth="800" MinHeight="600"
        WindowStartupLocation="CenterScreen">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,30">
            <TextBlock Text="📢 إرسال رسائل جماعية عبر الواتساب"
                       FontSize="28"
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       Foreground="#25D366"/>
            <TextBlock Text="أرسل رسائل إلى مجموعة من الأرقام بسهولة وأمان"
                       FontSize="14"
                       HorizontalAlignment="Center"
                       Foreground="Gray"
                       Margin="0,5,0,0"/>
        </StackPanel>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Panel - Message Setup -->
                <StackPanel Grid.Column="0" Spacing="25">

                    <!-- Step 1: Select Group -->
                    <Border Background="#F8F9FA"
                            CornerRadius="12"
                            Padding="20"
                            BorderBrush="#E9ECEF"
                            BorderThickness="1">
                        <StackPanel Spacing="15">
                            <StackPanel Orientation="Horizontal" Spacing="10">
                                <Border Background="#007ACC"
                                        CornerRadius="15"
                                        Width="30" Height="30">
                                    <TextBlock Text="1"
                                               Foreground="White"
                                               FontWeight="Bold"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="اختر المجموعة المستهدفة"
                                           FontSize="18"
                                           FontWeight="SemiBold"
                                           VerticalAlignment="Center"/>
                            </StackPanel>

                            <ComboBox ItemsSource="{Binding Groups}"
                                      SelectedItem="{Binding SelectedGroup}"
                                      DisplayMemberBinding="{Binding Name}"
                                      PlaceholderText="اختر مجموعة من القائمة..."
                                      Height="45"
                                      FontSize="14"/>

                            <TextBlock Text="{Binding SelectedGroupContactsCount, StringFormat='📱 عدد الأرقام: {0} رقم'}"
                                       FontSize="14"
                                       Foreground="#28A745"
                                       FontWeight="SemiBold"
                                       IsVisible="{Binding HasSelectedGroup}"/>
                        </StackPanel>
                    </Border>

                    <!-- Step 2: Write Message -->
                    <Border Background="#F8F9FA"
                            CornerRadius="12"
                            Padding="20"
                            BorderBrush="#E9ECEF"
                            BorderThickness="1">
                        <StackPanel Spacing="15">
                            <StackPanel Orientation="Horizontal" Spacing="10">
                                <Border Background="#28A745"
                                        CornerRadius="15"
                                        Width="30" Height="30">
                                    <TextBlock Text="2"
                                               Foreground="White"
                                               FontWeight="Bold"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="اكتب نص الرسالة"
                                           FontSize="18"
                                           FontWeight="SemiBold"
                                           VerticalAlignment="Center"/>
                            </StackPanel>

                            <TextBox Text="{Binding MessageContent}"
                                     AcceptsReturn="True"
                                     TextWrapping="Wrap"
                                     Height="120"
                                     Watermark="اكتب نص الرسالة التي تريد إرسالها..."
                                     FontSize="14"/>

                            <Grid>
                                <TextBlock Text="{Binding MessageContent.Length, StringFormat='عدد الأحرف: {0}'}"
                                           FontSize="12"
                                           Foreground="Gray"
                                           HorizontalAlignment="Left"/>
                                <TextBlock Text="💡 نصيحة: اجعل الرسالة واضحة ومختصرة"
                                           FontSize="12"
                                           Foreground="#FFC107"
                                           HorizontalAlignment="Right"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Step 3: Send Settings -->
                    <Border Background="#F8F9FA"
                            CornerRadius="12"
                            Padding="20"
                            BorderBrush="#E9ECEF"
                            BorderThickness="1">
                        <StackPanel Spacing="15">
                            <StackPanel Orientation="Horizontal" Spacing="10">
                                <Border Background="#FFC107"
                                        CornerRadius="15"
                                        Width="30" Height="30">
                                    <TextBlock Text="3"
                                               Foreground="White"
                                               FontWeight="Bold"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="إعدادات الإرسال"
                                           FontSize="18"
                                           FontWeight="SemiBold"
                                           VerticalAlignment="Center"/>
                            </StackPanel>

                            <StackPanel Spacing="10">
                                <TextBlock Text="⏱️ التأخير بين الرسائل (ثانية):"
                                           FontSize="14"
                                           FontWeight="SemiBold"/>
                                <NumericUpDown Value="{Binding DelayBetweenMessages}"
                                               Minimum="3"
                                               Maximum="30"
                                               Increment="1"
                                               Height="40"
                                               FontSize="14"/>
                                <TextBlock Text="⚠️ يُنصح بـ 5-10 ثوانٍ لتجنب الحظر"
                                           FontSize="12"
                                           Foreground="#DC3545"/>
                            </StackPanel>

                            <CheckBox Content="💾 حفظ الرسالة في سجل الإرسال"
                                      IsChecked="{Binding SaveToHistory}"
                                      FontSize="14"/>
                        </StackPanel>
                    </Border>

                </StackPanel>

                <!-- Send Settings -->
                <Border Classes="card">
                    <StackPanel>
                        <TextBlock Text="إعدادات الإرسال" Classes="subheader"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="التأخير بين الرسائل (ثانية):" Margin="0,0,0,5"/>
                                <NumericUpDown Value="{Binding DelayBetweenMessages}"
                                               Minimum="3"
                                               Maximum="30"
                                               Increment="1"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <CheckBox Content="حفظ الرسالة في السجل"
                                          IsChecked="{Binding SaveToHistory}"
                                          Margin="0,20,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Right Panel - Preview & Send -->
                <StackPanel Grid.Column="2" Spacing="25">

                    <!-- Preview -->
                    <Border Background="#E8F5E8"
                            CornerRadius="12"
                            Padding="20"
                            BorderBrush="#28A745"
                            BorderThickness="2"
                            IsVisible="{Binding CanSend}">
                        <StackPanel Spacing="15">
                            <StackPanel Orientation="Horizontal" Spacing="10">
                                <TextBlock Text="👁️" FontSize="20"/>
                                <TextBlock Text="معاينة الإرسال"
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="#28A745"/>
                            </StackPanel>

                            <Border Background="White"
                                    CornerRadius="8"
                                    Padding="15"
                                    BorderBrush="#DDD"
                                    BorderThickness="1">
                                <StackPanel Spacing="10">
                                    <TextBlock Text="📱 المجموعة المستهدفة:"
                                               FontSize="12"
                                               Foreground="Gray"/>
                                    <TextBlock Text="{Binding SelectedGroup.Name}"
                                               FontWeight="SemiBold"
                                               FontSize="14"/>

                                    <TextBlock Text="📝 نص الرسالة:"
                                               FontSize="12"
                                               Foreground="Gray"
                                               Margin="0,10,0,0"/>
                                    <Border Background="#F8F9FA"
                                            CornerRadius="5"
                                            Padding="10">
                                        <TextBlock Text="{Binding MessageContent}"
                                                   TextWrapping="Wrap"
                                                   FontSize="13"/>
                                    </Border>

                                    <Grid Margin="0,10,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="👥 عدد المستقبلين"
                                                       FontSize="11"
                                                       Foreground="Gray"/>
                                            <TextBlock Text="{Binding SelectedGroupContactsCount}"
                                                       FontWeight="Bold"
                                                       FontSize="16"
                                                       Foreground="#007ACC"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="⏱️ التأخير"
                                                       FontSize="11"
                                                       Foreground="Gray"/>
                                            <TextBlock Text="{Binding DelayBetweenMessages, StringFormat=\{0\} ثانية}"
                                                       FontWeight="Bold"
                                                       FontSize="16"
                                                       Foreground="#FFC107"/>
                                        </StackPanel>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>
                    <!-- Send Button -->
                    <Button Content="🚀 إرسال الرسائل الآن"
                            Background="#25D366"
                            Foreground="White"
                            FontSize="18"
                            FontWeight="Bold"
                            Height="60"
                            CornerRadius="10"
                            Command="{Binding SendBroadcastCommand}"
                            IsEnabled="{Binding CanSend}"/>

                    <!-- Progress -->
                    <Border Background="#FFF3CD"
                            CornerRadius="12"
                            Padding="20"
                            BorderBrush="#FFC107"
                            BorderThickness="1"
                            IsVisible="{Binding IsSending}">
                        <StackPanel Spacing="15">
                            <StackPanel Orientation="Horizontal" Spacing="10">
                                <TextBlock Text="⏳" FontSize="20"/>
                                <TextBlock Text="جاري الإرسال..."
                                           FontSize="18"
                                           FontWeight="Bold"
                                           Foreground="#856404"/>
                            </StackPanel>

                            <ProgressBar Value="{Binding SendProgress}"
                                         Maximum="100"
                                         Height="25"
                                         Background="#FFF"
                                         Foreground="#FFC107"/>

                            <TextBlock Text="{Binding SendProgressText}"
                                       HorizontalAlignment="Center"
                                       FontSize="14"
                                       Foreground="#856404"/>
                        </StackPanel>
                    </Border>

                    <!-- Warning -->
                    <Border Background="#F8D7DA"
                            CornerRadius="8"
                            Padding="15"
                            BorderBrush="#DC3545"
                            BorderThickness="1">
                        <StackPanel Spacing="8">
                            <TextBlock Text="⚠️ تنبيه مهم"
                                       FontWeight="Bold"
                                       FontSize="14"
                                       Foreground="#721C24"/>
                            <TextBlock Text="• تأكد من اتصال الواتساب قبل الإرسال"
                                       FontSize="12"
                                       Foreground="#721C24"/>
                            <TextBlock Text="• لا تغلق التطبيق أثناء الإرسال"
                                       FontSize="12"
                                       Foreground="#721C24"/>
                            <TextBlock Text="• استخدم تأخير مناسب لتجنب الحظر"
                                       FontSize="12"
                                       Foreground="#721C24"/>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2"
                Background="#E9ECEF"
                Padding="15"
                CornerRadius="0,0,10,10"
                Margin="0,20,0,0">
            <Grid>
                <TextBlock Text="{Binding StatusMessage}"
                           VerticalAlignment="Center"
                           FontSize="14"
                           Foreground="#495057"/>
                <TextBlock Text="💡 نصيحة: اختبر الرسالة على رقم واحد أولاً"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="#6C757D"/>
            </Grid>
        </Border>

    </Grid>
</Window>
