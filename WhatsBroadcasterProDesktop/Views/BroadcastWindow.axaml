<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:WhatsBroadcasterProDesktop.ViewModels"
        x:Class="WhatsBroadcasterProDesktop.Views.BroadcastWindow"
        x:DataType="vm:BroadcastWindowViewModel"
        Title="الإرسال الجماعي"
        Width="900" Height="700"
        WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource DangerBrush}" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📢" FontSize="24" VerticalAlignment="Center" Foreground="White"/>
                <TextBlock Text="إرسال رسائل جماعية" 
                           FontSize="20" 
                           FontWeight="Bold" 
                           Foreground="White" 
                           VerticalAlignment="Center" 
                           Margin="10,0,0,0"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel Spacing="20">
                
                <!-- Group Selection -->
                <Border Classes="card">
                    <StackPanel>
                        <TextBlock Text="اختيار المجموعة" Classes="subheader"/>
                        <ComboBox ItemsSource="{Binding Groups}"
                                  SelectedItem="{Binding SelectedGroup}"
                                  DisplayMemberBinding="{Binding Name}"
                                  PlaceholderText="اختر المجموعة للإرسال إليها"
                                  HorizontalAlignment="Stretch"
                                  Margin="0,10,0,0"/>
                        <TextBlock Text="{Binding SelectedGroupContactsCount, StringFormat='عدد الأرقام في المجموعة: {0}'}"
                                   FontSize="12"
                                   Foreground="Gray"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Message Content -->
                <Border Classes="card">
                    <StackPanel>
                        <TextBlock Text="محتوى الرسالة" Classes="subheader"/>
                        <TextBox Text="{Binding MessageContent}"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 Height="150"
                                 Watermark="اكتب نص الرسالة هنا..."
                                 Margin="0,10,0,0"/>
                        <TextBlock Text="{Binding MessageContent.Length, StringFormat='عدد الأحرف: {0}'}"
                                   FontSize="12"
                                   Foreground="Gray"
                                   HorizontalAlignment="Right"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Send Settings -->
                <Border Classes="card">
                    <StackPanel>
                        <TextBlock Text="إعدادات الإرسال" Classes="subheader"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                <TextBlock Text="التأخير بين الرسائل (ثانية):" Margin="0,0,0,5"/>
                                <NumericUpDown Value="{Binding DelayBetweenMessages}"
                                               Minimum="3"
                                               Maximum="30"
                                               Increment="1"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <CheckBox Content="حفظ الرسالة في السجل"
                                          IsChecked="{Binding SaveToHistory}"
                                          Margin="0,20,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Preview -->
                <Border Classes="card" IsVisible="{Binding CanSend}">
                    <StackPanel>
                        <TextBlock Text="معاينة الإرسال" Classes="subheader"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="المجموعة:" FontWeight="SemiBold"/>
                                <TextBlock Text="{Binding SelectedGroup.Name}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="عدد المستقبلين:" FontWeight="SemiBold"/>
                                <TextBlock Text="{Binding SelectedGroupContactsCount}"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="الوقت المتوقع:" FontWeight="SemiBold"/>
                                <TextBlock Text="{Binding EstimatedTime}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Send Button -->
                <Button Content="📤 إرسال الرسائل" 
                        Classes="danger"
                        FontSize="16"
                        Padding="20,12"
                        HorizontalAlignment="Center"
                        Command="{Binding SendBroadcastCommand}"
                        IsEnabled="{Binding CanSend}"/>

                <!-- Progress -->
                <Border Classes="card" IsVisible="{Binding IsSending}">
                    <StackPanel>
                        <TextBlock Text="جاري الإرسال..." Classes="subheader"/>
                        <ProgressBar Value="{Binding SendProgress}"
                                     Maximum="100"
                                     Height="20"
                                     Margin="0,10,0,0"/>
                        <TextBlock Text="{Binding SendProgressText}"
                                   HorizontalAlignment="Center"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="10">
            <TextBlock Text="{Binding StatusMessage}" VerticalAlignment="Center"/>
        </Border>

    </Grid>
</Window>
