﻿using WhatsBroadcasterProDesktop.Services;
using WhatsBroadcasterProDesktop.Models;

namespace WhatsBroadcasterProDesktop
{
    internal static class Program
    {
        private static IDatabaseService _databaseService = null!;
        private static IWhatsAppService _whatsAppService = null!;

        static async Task Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("🚀 مرحباً بك في WhatsBroadcaster Pro");
            Console.WriteLine("=====================================");

            // Initialize services
            _databaseService = new DatabaseService();
            _whatsAppService = new WhatsAppService();

            // Initialize database
            await _databaseService.InitializeAsync();
            Console.WriteLine("✅ تم تهيئة قاعدة البيانات بنجاح");

            // Subscribe to WhatsApp events
            _whatsAppService.ConnectionStatusChanged += (sender, status) =>
            {
                Console.WriteLine($"📱 حالة الاتصال: {status}");
            };

            // Main menu loop
            await ShowMainMenu();
        }

        private static async Task ShowMainMenu()
        {
            while (true)
            {
                Console.WriteLine("\n📋 القائمة الرئيسية:");
                Console.WriteLine("1. إدارة الأرقام");
                Console.WriteLine("2. إدارة المجموعات");
                Console.WriteLine("3. إرسال رسائل جماعية");
                Console.WriteLine("4. الاتصال بواتساب");
                Console.WriteLine("5. قطع الاتصال من واتساب");
                Console.WriteLine("6. عرض الإحصائيات");
                Console.WriteLine("0. خروج");
                Console.Write("\nاختر رقم الخيار: ");

                var choice = Console.ReadLine();

                try
                {
                    switch (choice)
                    {
                        case "1":
                            await ManageContacts();
                            break;
                        case "2":
                            await ManageGroups();
                            break;
                        case "3":
                            await SendBroadcastMessage();
                            break;
                        case "4":
                            await ConnectToWhatsApp();
                            break;
                        case "5":
                            await DisconnectFromWhatsApp();
                            break;
                        case "6":
                            await ShowStatistics();
                            break;
                        case "0":
                            Console.WriteLine("👋 شكراً لاستخدام WhatsBroadcaster Pro!");
                            return;
                        default:
                            Console.WriteLine("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ حدث خطأ: {ex.Message}");
                }

                Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                Console.ReadKey();
            }
        }

        private static async Task ManageContacts()
        {
            Console.WriteLine("\n👥 إدارة الأرقام");
            Console.WriteLine("================");

            while (true)
            {
                Console.WriteLine("\n1. عرض جميع الأرقام");
                Console.WriteLine("2. إضافة رقم جديد");
                Console.WriteLine("3. حذف رقم");
                Console.WriteLine("0. العودة للقائمة الرئيسية");
                Console.Write("\nاختر رقم الخيار: ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        await ShowAllContacts();
                        break;
                    case "2":
                        await AddNewContact();
                        break;
                    case "3":
                        await DeleteContact();
                        break;
                    case "0":
                        return;
                    default:
                        Console.WriteLine("❌ خيار غير صحيح");
                        break;
                }
            }
        }

        private static async Task ShowAllContacts()
        {
            var contacts = await _databaseService.GetContactsAsync();

            if (!contacts.Any())
            {
                Console.WriteLine("📭 لا توجد أرقام محفوظة");
                return;
            }

            Console.WriteLine($"\n📞 الأرقام المحفوظة ({contacts.Count}):");
            Console.WriteLine("".PadRight(60, '-'));

            foreach (var contact in contacts)
            {
                var groups = string.Join(", ", contact.ContactGroups.Select(cg => cg.Group.Name));
                Console.WriteLine($"👤 {contact.Name}");
                Console.WriteLine($"   📱 {contact.PhoneNumber}");
                if (!string.IsNullOrEmpty(contact.Email))
                    Console.WriteLine($"   📧 {contact.Email}");
                if (!string.IsNullOrEmpty(groups))
                    Console.WriteLine($"   📁 المجموعات: {groups}");
                Console.WriteLine();
            }
        }

        private static async Task AddNewContact()
        {
            Console.WriteLine("\n➕ إضافة رقم جديد");
            Console.WriteLine("==================");

            Console.Write("الاسم: ");
            var name = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(name))
            {
                Console.WriteLine("❌ الاسم مطلوب");
                return;
            }

            Console.Write("رقم الهاتف (مع رمز الدولة): ");
            var phone = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(phone))
            {
                Console.WriteLine("❌ رقم الهاتف مطلوب");
                return;
            }

            Console.Write("البريد الإلكتروني (اختياري): ");
            var email = Console.ReadLine();

            Console.Write("ملاحظات (اختياري): ");
            var notes = Console.ReadLine();

            var contact = new Contact
            {
                Name = name.Trim(),
                PhoneNumber = phone.Trim(),
                Email = string.IsNullOrWhiteSpace(email) ? null : email.Trim(),
                Notes = string.IsNullOrWhiteSpace(notes) ? null : notes.Trim()
            };

            await _databaseService.AddContactAsync(contact);
            Console.WriteLine("✅ تم إضافة الرقم بنجاح");
        }

        private static async Task DeleteContact()
        {
            var contacts = await _databaseService.GetContactsAsync();

            if (!contacts.Any())
            {
                Console.WriteLine("📭 لا توجد أرقام للحذف");
                return;
            }

            Console.WriteLine("\n🗑️ حذف رقم");
            Console.WriteLine("============");

            for (int i = 0; i < contacts.Count; i++)
            {
                Console.WriteLine($"{i + 1}. {contacts[i].Name} - {contacts[i].PhoneNumber}");
            }

            Console.Write("\nاختر رقم الرقم المراد حذفه (0 للإلغاء): ");
            if (int.TryParse(Console.ReadLine(), out int choice) && choice > 0 && choice <= contacts.Count)
            {
                var contactToDelete = contacts[choice - 1];
                Console.Write($"هل أنت متأكد من حذف {contactToDelete.Name}؟ (y/n): ");
                var confirm = Console.ReadLine();

                if (confirm?.ToLower() == "y" || confirm?.ToLower() == "yes")
                {
                    await _databaseService.DeleteContactAsync(contactToDelete.Id);
                    Console.WriteLine("✅ تم حذف الرقم بنجاح");
                }
                else
                {
                    Console.WriteLine("❌ تم إلغاء العملية");
                }
            }
            else
            {
                Console.WriteLine("❌ اختيار غير صحيح");
            }
        }

        private static async Task ManageGroups()
        {
            Console.WriteLine("\n📁 إدارة المجموعات");
            Console.WriteLine("==================");

            while (true)
            {
                Console.WriteLine("\n1. عرض جميع المجموعات");
                Console.WriteLine("2. إضافة مجموعة جديدة");
                Console.WriteLine("3. حذف مجموعة");
                Console.WriteLine("0. العودة للقائمة الرئيسية");
                Console.Write("\nاختر رقم الخيار: ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        await ShowAllGroups();
                        break;
                    case "2":
                        await AddNewGroup();
                        break;
                    case "3":
                        await DeleteGroup();
                        break;
                    case "0":
                        return;
                    default:
                        Console.WriteLine("❌ خيار غير صحيح");
                        break;
                }
            }
        }

        private static async Task ShowAllGroups()
        {
            var groups = await _databaseService.GetGroupsAsync();

            if (!groups.Any())
            {
                Console.WriteLine("📭 لا توجد مجموعات");
                return;
            }

            Console.WriteLine($"\n📁 المجموعات ({groups.Count}):");
            Console.WriteLine("".PadRight(60, '-'));

            foreach (var group in groups)
            {
                Console.WriteLine($"📁 {group.Name} ({group.ContactGroups.Count} رقم)");
                if (!string.IsNullOrEmpty(group.Description))
                    Console.WriteLine($"   📝 {group.Description}");
                Console.WriteLine();
            }
        }

        private static async Task AddNewGroup()
        {
            Console.WriteLine("\n➕ إضافة مجموعة جديدة");
            Console.WriteLine("=====================");

            Console.Write("اسم المجموعة: ");
            var name = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(name))
            {
                Console.WriteLine("❌ اسم المجموعة مطلوب");
                return;
            }

            Console.Write("وصف المجموعة (اختياري): ");
            var description = Console.ReadLine();

            var group = new Group
            {
                Name = name.Trim(),
                Description = string.IsNullOrWhiteSpace(description) ? null : description.Trim()
            };

            await _databaseService.AddGroupAsync(group);
            Console.WriteLine("✅ تم إضافة المجموعة بنجاح");
        }

        private static async Task DeleteGroup()
        {
            var groups = await _databaseService.GetGroupsAsync();

            if (!groups.Any())
            {
                Console.WriteLine("📭 لا توجد مجموعات للحذف");
                return;
            }

            Console.WriteLine("\n🗑️ حذف مجموعة");
            Console.WriteLine("===============");

            for (int i = 0; i < groups.Count; i++)
            {
                Console.WriteLine($"{i + 1}. {groups[i].Name} ({groups[i].ContactGroups.Count} رقم)");
            }

            Console.Write("\nاختر رقم المجموعة المراد حذفها (0 للإلغاء): ");
            if (int.TryParse(Console.ReadLine(), out int choice) && choice > 0 && choice <= groups.Count)
            {
                var groupToDelete = groups[choice - 1];
                Console.Write($"هل أنت متأكد من حذف مجموعة {groupToDelete.Name}؟ (y/n): ");
                var confirm = Console.ReadLine();

                if (confirm?.ToLower() == "y" || confirm?.ToLower() == "yes")
                {
                    await _databaseService.DeleteGroupAsync(groupToDelete.Id);
                    Console.WriteLine("✅ تم حذف المجموعة بنجاح");
                }
                else
                {
                    Console.WriteLine("❌ تم إلغاء العملية");
                }
            }
            else
            {
                Console.WriteLine("❌ اختيار غير صحيح");
            }
        }

        private static async Task SendBroadcastMessage()
        {
            Console.WriteLine("\n📢 إرسال رسائل جماعية");
            Console.WriteLine("====================");

            if (!_whatsAppService.IsConnected)
            {
                Console.WriteLine("❌ يجب الاتصال بواتساب أولاً");
                return;
            }

            var groups = await _databaseService.GetGroupsAsync();
            var groupsWithContacts = groups.Where(g => g.ContactGroups.Any()).ToList();

            if (!groupsWithContacts.Any())
            {
                Console.WriteLine("📭 لا توجد مجموعات تحتوي على أرقام");
                return;
            }

            Console.WriteLine("\nاختر المجموعة:");
            for (int i = 0; i < groupsWithContacts.Count; i++)
            {
                Console.WriteLine($"{i + 1}. {groupsWithContacts[i].Name} ({groupsWithContacts[i].ContactGroups.Count} رقم)");
            }

            Console.Write("\nاختر رقم المجموعة: ");
            if (!int.TryParse(Console.ReadLine(), out int choice) || choice < 1 || choice > groupsWithContacts.Count)
            {
                Console.WriteLine("❌ اختيار غير صحيح");
                return;
            }

            var selectedGroup = groupsWithContacts[choice - 1];
            var contacts = await _databaseService.GetContactsByGroupAsync(selectedGroup.Id);

            Console.Write("\nاكتب نص الرسالة: ");
            var message = Console.ReadLine();
            if (string.IsNullOrWhiteSpace(message))
            {
                Console.WriteLine("❌ نص الرسالة مطلوب");
                return;
            }

            Console.WriteLine($"\nسيتم إرسال الرسالة إلى {contacts.Count} رقم في مجموعة '{selectedGroup.Name}'");
            Console.Write("هل تريد المتابعة؟ (y/n): ");
            var confirm = Console.ReadLine();

            if (confirm?.ToLower() != "y" && confirm?.ToLower() != "yes")
            {
                Console.WriteLine("❌ تم إلغاء العملية");
                return;
            }

            Console.WriteLine("\n🚀 بدء الإرسال...");

            int successCount = 0;
            int failCount = 0;

            for (int i = 0; i < contacts.Count; i++)
            {
                var contact = contacts[i];
                Console.Write($"[{i + 1}/{contacts.Count}] إرسال إلى {contact.Name} ({contact.PhoneNumber})... ");

                try
                {
                    var success = await _whatsAppService.SendMessageAsync(contact.PhoneNumber, message);
                    if (success)
                    {
                        Console.WriteLine("✅ نجح");
                        successCount++;
                    }
                    else
                    {
                        Console.WriteLine("❌ فشل");
                        failCount++;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ خطأ: {ex.Message}");
                    failCount++;
                }

                // تأخير بين الرسائل
                if (i < contacts.Count - 1)
                {
                    await Task.Delay(5000); // 5 ثواني
                }
            }

            Console.WriteLine($"\n📊 انتهى الإرسال:");
            Console.WriteLine($"✅ نجح: {successCount}");
            Console.WriteLine($"❌ فشل: {failCount}");
        }

        private static async Task ConnectToWhatsApp()
        {
            Console.WriteLine("\n🔗 الاتصال بواتساب");
            Console.WriteLine("==================");

            if (_whatsAppService.IsConnected)
            {
                Console.WriteLine("✅ متصل بالفعل بواتساب");
                return;
            }

            Console.WriteLine("جاري فتح متصفح واتساب ويب...");
            Console.WriteLine("يرجى مسح رمز QR باستخدام هاتفك");

            try
            {
                await _whatsAppService.ConnectAsync();
                Console.WriteLine("✅ تم الاتصال بواتساب بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل الاتصال: {ex.Message}");
            }
        }

        private static async Task DisconnectFromWhatsApp()
        {
            Console.WriteLine("\n🔌 قطع الاتصال من واتساب");
            Console.WriteLine("========================");

            if (!_whatsAppService.IsConnected)
            {
                Console.WriteLine("❌ غير متصل بواتساب");
                return;
            }

            try
            {
                await _whatsAppService.DisconnectAsync();
                Console.WriteLine("✅ تم قطع الاتصال بنجاح");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في قطع الاتصال: {ex.Message}");
            }
        }

        private static async Task ShowStatistics()
        {
            Console.WriteLine("\n📊 الإحصائيات");
            Console.WriteLine("==============");

            var contacts = await _databaseService.GetContactsAsync();
            var groups = await _databaseService.GetGroupsAsync();
            var messages = await _databaseService.GetBroadcastMessagesAsync();

            Console.WriteLine($"👥 إجمالي الأرقام: {contacts.Count}");
            Console.WriteLine($"📁 إجمالي المجموعات: {groups.Count}");
            Console.WriteLine($"📢 إجمالي الرسائل المرسلة: {messages.Count}");
            Console.WriteLine($"🔗 حالة الاتصال: {(_whatsAppService.IsConnected ? "متصل" : "غير متصل")}");

            if (messages.Any())
            {
                var totalSent = messages.Sum(m => m.SuccessfulSends);
                var totalFailed = messages.Sum(m => m.FailedSends);
                Console.WriteLine($"✅ رسائل نجحت: {totalSent}");
                Console.WriteLine($"❌ رسائل فشلت: {totalFailed}");
            }
        }
    }
}
