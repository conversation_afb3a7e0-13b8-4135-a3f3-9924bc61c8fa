# 🔧 دليل حل مشاكل الاتصال - WhatsBroadcaster Pro

## 🚨 **المشاكل الشائعة وحلولها:**

### ❌ **المشكلة: "التطبيق لا يمكنه الاتصال"**

#### 🔍 **الأسباب المحتملة:**

### 1. **Google Chrome غير مثبت أو قديم**
```bash
# تحقق من تثبيت Chrome
ls "/Applications/Google Chrome.app"
```
**الحل:**
- قم بتحميل وتثبيت Google Chrome من الموقع الرسمي
- تأكد من أن الإصدار حديث (120+)

### 2. **مشكلة في ChromeDriver**
**الأعراض:**
- رسالة "خطأ في تشغيل المتصفح"
- التطبيق يتوقف عند "جاري تحضير المتصفح"

**الحل:**
```bash
# تحديث ChromeDriver
cd WhatsBroadcasterProDesktop
dotnet build
```

### 3. **مشكلة في الشبكة/الإنترنت**
**الأعراض:**
- "فشل في الوصول لواتساب ويب"
- التطبيق يتوقف عند "جاري الاتصال بواتساب ويب"

**الحل:**
- تحقق من اتصال الإنترنت
- جرب فتح https://web.whatsapp.com في المتصفح العادي
- تأكد من عدم وجود جدار حماية يحجب الاتصال

### 4. **عمليات Chrome معلقة**
**الأعراض:**
- التطبيق لا يستجيب
- رسالة "user data directory is already in use"

**الحل:**
```bash
# إغلاق جميع عمليات Chrome
pkill -f chrome
pkill -f chromedriver
```

## 🛠️ **خطوات استكشاف الأخطاء:**

### **الخطوة 1: فحص المتطلبات الأساسية**
```bash
# تحقق من Chrome
open -a "Google Chrome" --args --version

# تحقق من .NET
dotnet --version
```

### **الخطوة 2: تنظيف النظام**
```bash
# إغلاق العمليات المعلقة
pkill -f chrome
pkill -f chromedriver

# حذف مجلدات Chrome المؤقتة
rm -rf ~/Library/Application\ Support/WhatsBroadcasterPro/chrome_profile_*
```

### **الخطوة 3: إعادة بناء التطبيق**
```bash
cd WhatsBroadcasterProDesktop
dotnet clean
dotnet build
dotnet run
```

### **الخطوة 4: اختبار الاتصال اليدوي**
1. افتح Google Chrome عادياً
2. اذهب إلى https://web.whatsapp.com
3. تأكد من أن الموقع يعمل بشكل طبيعي
4. امسح رمز QR بهاتفك
5. إذا نجح، أغلق Chrome وجرب التطبيق

## 🔧 **حلول متقدمة:**

### **إذا استمرت المشكلة:**

#### **1. تحديث Chrome:**
```bash
# تحديث Chrome على macOS
# اذهب إلى Chrome > About Google Chrome
# أو حمل الإصدار الأحدث من google.com/chrome
```

#### **2. إعادة تعيين إعدادات Chrome:**
```bash
# حذف ملفات Chrome المؤقتة
rm -rf ~/Library/Application\ Support/Google/Chrome/Default/Web\ Data
rm -rf ~/Library/Caches/Google/Chrome
```

#### **3. فحص صلاحيات النظام:**
- تأكد من أن التطبيق لديه صلاحية الوصول للإنترنت
- تحقق من إعدادات الأمان في macOS
- تأكد من عدم حجب التطبيق بواسطة مكافح الفيروسات

#### **4. تشغيل التطبيق بصلاحيات إدارية:**
```bash
sudo dotnet run
```

## 📱 **اختبار الاتصال خطوة بخطوة:**

### **1. تشغيل التطبيق:**
```bash
dotnet run
```

### **2. مراقبة الرسائل:**
- "جاري تحضير المتصفح..." ✅
- "جاري تشغيل المتصفح..." ✅
- "جاري الاتصال بواتساب ويب..." ✅
- "جاري تحميل واتساب ويب..." ✅
- "📱 امسح رمز QR بهاتفك للاتصال" ✅
- "تم الاتصال بنجاح! جاهز للإرسال" ✅

### **3. إذا توقف في أي مرحلة:**
- اضغط Ctrl+C لإيقاف التطبيق
- اتبع الحلول المناسبة أعلاه
- أعد المحاولة

## 🚨 **رسائل الخطأ الشائعة:**

### **"Google Chrome غير مثبت"**
**الحل:** قم بتثبيت Google Chrome

### **"خطأ في تشغيل المتصفح"**
**الحل:** 
```bash
pkill -f chrome
dotnet clean
dotnet build
```

### **"فشل في الوصول لواتساب ويب"**
**الحل:** تحقق من الإنترنت وجرب:
```bash
ping google.com
curl -I https://web.whatsapp.com
```

### **"انتهت مهلة انتظار الاتصال"**
**الحل:** 
- تأكد من مسح رمز QR بسرعة
- تحقق من أن هاتفك متصل بالإنترنت
- جرب إعادة تشغيل التطبيق

## 📞 **الدعم الفني:**

### **معلومات مفيدة للدعم:**
```bash
# معلومات النظام
uname -a
sw_vers

# إصدار Chrome
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --version

# إصدار .NET
dotnet --version

# سجل الأخطاء
dotnet run 2>&1 | tee error.log
```

### **ملفات السجل:**
- سجل التطبيق: في الكونسول
- سجل Chrome: `~/Library/Logs/Google/Chrome/`
- سجل النظام: Console.app

---

**💡 نصيحة:** إذا فشلت جميع الحلول، جرب إعادة تشغيل الكمبيوتر وتشغيل التطبيق مرة أخرى.
