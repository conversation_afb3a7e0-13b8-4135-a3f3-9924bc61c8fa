#!/bin/bash

# WhatsBroadcaster Pro - تشغيل التطبيق
# =====================================

echo "🚀 بدء تشغيل WhatsBroadcaster Pro..."
echo "====================================="

# التحقق من وجود .NET
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET غير مثبت. يرجى تثبيت .NET 9.0 أو أحدث"
    echo "🔗 https://dotnet.microsoft.com/download"
    exit 1
fi

# التحقق من إصدار .NET
DOTNET_VERSION=$(dotnet --version)
echo "✅ تم العثور على .NET إصدار: $DOTNET_VERSION"

# التحقق من وجود Google Chrome
if command -v google-chrome &> /dev/null; then
    echo "✅ تم العثور على Google Chrome"
elif command -v chromium-browser &> /dev/null; then
    echo "✅ تم العثور على Chromium"
elif command -v "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome" &> /dev/null; then
    echo "✅ تم العثور على Google Chrome (macOS)"
else
    echo "⚠️  تحذير: لم يتم العثور على Google Chrome"
    echo "   يرجى تثبيت Google Chrome لاستخدام ميزة واتساب"
fi

# استعادة الحزم
echo "📦 استعادة الحزم..."
dotnet restore

if [ $? -ne 0 ]; then
    echo "❌ فشل في استعادة الحزم"
    exit 1
fi

# بناء المشروع
echo "🔨 بناء المشروع..."
dotnet build

if [ $? -ne 0 ]; then
    echo "❌ فشل في بناء المشروع"
    exit 1
fi

echo "✅ تم بناء المشروع بنجاح"
echo ""
echo "🎯 نصائح سريعة:"
echo "   • استخدم الخيار 4 للاتصال بواتساب أولاً"
echo "   • أضف أرقام تجريبية للاختبار"
echo "   • لا تغلق نافذة Chrome يدوياً"
echo ""
echo "🚀 تشغيل التطبيق..."
echo "===================="

# تشغيل التطبيق
dotnet run
