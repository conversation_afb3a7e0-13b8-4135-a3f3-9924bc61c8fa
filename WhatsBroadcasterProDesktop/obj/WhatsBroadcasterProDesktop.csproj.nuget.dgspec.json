{"format": 1, "restore": {"/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/WhatsBroadcasterProDesktop.csproj": {}}, "projects": {"/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/WhatsBroadcasterProDesktop.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/WhatsBroadcasterProDesktop.csproj", "projectName": "WhatsBroadcasterProDesktop", "projectPath": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/WhatsBroadcasterProDesktop.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Controls.DataGrid": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.ReactiveUI": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.0.10, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.34.0, )"}, "Selenium.WebDriver.ChromeDriver": {"target": "Package", "version": "[138.0.7204.15700, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.202/PortableRuntimeIdentifierGraph.json"}}}}}