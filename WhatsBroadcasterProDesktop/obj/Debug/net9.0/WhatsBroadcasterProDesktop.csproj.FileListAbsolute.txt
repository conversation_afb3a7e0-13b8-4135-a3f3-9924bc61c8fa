/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/selenium-manager/linux/selenium-manager
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/selenium-manager/macos/selenium-manager
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/selenium-manager/windows/selenium-manager.exe
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/WhatsBroadcasterProDesktop
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/WhatsBroadcasterProDesktop.deps.json
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/WhatsBroadcasterProDesktop.runtimeconfig.json
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/WhatsBroadcasterProDesktop.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/WhatsBroadcasterProDesktop.pdb
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Base.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Controls.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.DesignerSupport.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Dialogs.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Markup.Xaml.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Markup.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Metal.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.MicroCom.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.OpenGL.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Controls.DataGrid.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Desktop.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Fonts.Inter.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.FreeDesktop.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Native.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.ReactiveUI.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Remote.Protocol.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Skia.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Themes.Fluent.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.Win32.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Avalonia.X11.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/CommunityToolkit.Mvvm.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/DynamicData.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/HarfBuzzSharp.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/MicroCom.Runtime.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Data.Sqlite.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.EntityFrameworkCore.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.EntityFrameworkCore.Abstractions.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.EntityFrameworkCore.Relational.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.EntityFrameworkCore.Sqlite.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.Caching.Abstractions.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.Caching.Memory.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.DependencyInjection.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.DependencyModel.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.Logging.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.Options.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Microsoft.Extensions.Primitives.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/ReactiveUI.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/WebDriver.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/SkiaSharp.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Splat.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/SQLitePCLRaw.batteries_v2.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/SQLitePCLRaw.core.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/SQLitePCLRaw.provider.e_sqlite3.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/System.Reactive.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/System.Text.Json.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/Tmds.DBus.Protocol.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-arm64/native/av_libglesv2.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-x64/native/av_libglesv2.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-x86/native/av_libglesv2.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/osx/native/libAvaloniaNative.dylib
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-arm/native/libHarfBuzzSharp.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-arm64/native/libHarfBuzzSharp.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-musl-x64/native/libHarfBuzzSharp.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-x64/native/libHarfBuzzSharp.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/osx/native/libHarfBuzzSharp.dylib
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-arm64/native/libHarfBuzzSharp.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-x64/native/libHarfBuzzSharp.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-x86/native/libHarfBuzzSharp.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-arm/native/libSkiaSharp.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-arm64/native/libSkiaSharp.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-musl-x64/native/libSkiaSharp.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-x64/native/libSkiaSharp.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/osx/native/libSkiaSharp.dylib
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-arm64/native/libSkiaSharp.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-x64/native/libSkiaSharp.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-x86/native/libSkiaSharp.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-arm/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-arm64/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-armel/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-mips64/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-musl-arm/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-musl-arm64/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-musl-s390x/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-musl-x64/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-ppc64le/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-s390x/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-x64/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/linux-x86/native/libe_sqlite3.so
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/maccatalyst-x64/native/libe_sqlite3.dylib
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/osx-arm64/native/libe_sqlite3.dylib
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/osx-x64/native/libe_sqlite3.dylib
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-arm/native/e_sqlite3.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-arm64/native/e_sqlite3.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-x64/native/e_sqlite3.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/bin/Debug/net9.0/runtimes/win-x86/native/e_sqlite3.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/WhatsBroadcasterProDesktop.csproj.AssemblyReference.cache
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/Avalonia/Resources.Inputs.cache
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/Avalonia/resources
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/WhatsBroadcasterProDesktop.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/WhatsBroadcasterProDesktop.AssemblyInfoInputs.cache
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/WhatsBroadcasterProDesktop.AssemblyInfo.cs
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/WhatsBroadcasterProDesktop.csproj.CoreCompileInputs.cache
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/Avalonia/references
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/Avalonia/original.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/Avalonia/original.pdb
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/Avalonia/original.ref.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/refint/WhatsBroadcasterProDesktop.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/WhatsBroadcasterProDesktop.dll
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/WhatsBroadcasterProDesktop.pdb
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/WhatsBro.B711418F.Up2Date
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/WhatsBroadcasterProDesktop.genruntimeconfig.cache
/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/Debug/net9.0/ref/WhatsBroadcasterProDesktop.dll
