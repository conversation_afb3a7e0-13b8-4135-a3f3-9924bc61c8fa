is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property._MvvmToolkitIsUsingWindowsRuntimePack = false
build_property.CsWinRTComponent = 
build_property.CsWinRTAotOptimizerEnabled = 
build_property.CsWinRTAotWarningLevel = 
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = WhatsBroadcasterProDesktop
build_property.ProjectDir = /Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/Views/BroadcastWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/Views/ContactsWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/Views/GroupsWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/Views/MainWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/Views/TestWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
