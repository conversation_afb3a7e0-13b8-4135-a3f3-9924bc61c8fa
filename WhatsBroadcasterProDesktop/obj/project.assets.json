{"version": 3, "targets": {"net9.0": {"Avalonia/11.0.10": {"type": "package", "dependencies": {"Avalonia.BuildServices": "0.0.29", "Avalonia.Remote.Protocol": "11.0.10", "MicroCom.Runtime": "0.11.0", "System.ComponentModel.Annotations": "4.5.0"}, "compile": {"ref/net6.0/Avalonia.Base.dll": {"related": ".xml"}, "ref/net6.0/Avalonia.Controls.dll": {"related": ".xml"}, "ref/net6.0/Avalonia.DesignerSupport.dll": {"related": ".xml"}, "ref/net6.0/Avalonia.Dialogs.dll": {"related": ".xml"}, "ref/net6.0/Avalonia.Markup.Xaml.dll": {"related": ".xml"}, "ref/net6.0/Avalonia.Markup.dll": {"related": ".Xaml.xml;.xml"}, "ref/net6.0/Avalonia.Metal.dll": {"related": ".xml"}, "ref/net6.0/Avalonia.MicroCom.dll": {"related": ".xml"}, "ref/net6.0/Avalonia.OpenGL.dll": {"related": ".xml"}, "ref/net6.0/Avalonia.dll": {"related": ".Base.xml;.Controls.xml;.DesignerSupport.xml;.Dialogs.xml;.Markup.Xaml.xml;.Markup.xml;.Metal.xml;.MicroCom.xml;.OpenGL.xml;.xml"}}, "runtime": {"lib/net6.0/Avalonia.Base.dll": {"related": ".xml"}, "lib/net6.0/Avalonia.Controls.dll": {"related": ".xml"}, "lib/net6.0/Avalonia.DesignerSupport.dll": {"related": ".xml"}, "lib/net6.0/Avalonia.Dialogs.dll": {"related": ".xml"}, "lib/net6.0/Avalonia.Markup.Xaml.dll": {"related": ".xml"}, "lib/net6.0/Avalonia.Markup.dll": {"related": ".Xaml.xml;.xml"}, "lib/net6.0/Avalonia.Metal.dll": {"related": ".xml"}, "lib/net6.0/Avalonia.MicroCom.dll": {"related": ".xml"}, "lib/net6.0/Avalonia.OpenGL.dll": {"related": ".xml"}, "lib/net6.0/Avalonia.dll": {"related": ".Base.xml;.Controls.xml;.DesignerSupport.xml;.Dialogs.xml;.Markup.Xaml.xml;.Markup.xml;.Metal.xml;.MicroCom.xml;.OpenGL.xml;.xml"}}, "build": {"buildTransitive/Avalonia.props": {}, "buildTransitive/Avalonia.targets": {}}}, "Avalonia.Angle.Windows.Natives/2.1.0.**********": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/av_libglesv2.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/av_libglesv2.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/av_libglesv2.dll": {"assetType": "native", "rid": "win-x86"}}}, "Avalonia.BuildServices/0.0.29": {"type": "package", "build": {"buildTransitive/Avalonia.BuildServices.targets": {}}}, "Avalonia.Controls.DataGrid/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10", "Avalonia.Remote.Protocol": "11.0.10"}, "compile": {"lib/net6.0/Avalonia.Controls.DataGrid.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.Controls.DataGrid.dll": {"related": ".xml"}}}, "Avalonia.Desktop/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10", "Avalonia.Native": "11.0.10", "Avalonia.Skia": "11.0.10", "Avalonia.Win32": "11.0.10", "Avalonia.X11": "11.0.10"}, "compile": {"lib/net6.0/Avalonia.Desktop.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.Desktop.dll": {"related": ".xml"}}}, "Avalonia.Fonts.Inter/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10"}, "compile": {"lib/net6.0/Avalonia.Fonts.Inter.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.Fonts.Inter.dll": {"related": ".xml"}}}, "Avalonia.FreeDesktop/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10", "Tmds.DBus.Protocol": "0.15.0"}, "compile": {"lib/net6.0/Avalonia.FreeDesktop.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.FreeDesktop.dll": {"related": ".xml"}}}, "Avalonia.Native/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10"}, "compile": {"lib/net6.0/Avalonia.Native.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.Native.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/osx/native/libAvaloniaNative.dylib": {"assetType": "native", "rid": "osx"}}}, "Avalonia.ReactiveUI/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10", "ReactiveUI": "18.3.1", "System.Reactive": "5.0.0"}, "compile": {"lib/net6.0/Avalonia.ReactiveUI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.ReactiveUI.dll": {"related": ".xml"}}}, "Avalonia.Remote.Protocol/11.0.10": {"type": "package", "compile": {"lib/net6.0/Avalonia.Remote.Protocol.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.Remote.Protocol.dll": {"related": ".xml"}}}, "Avalonia.Skia/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10", "HarfBuzzSharp": "7.3.0", "HarfBuzzSharp.NativeAssets.Linux": "7.3.0", "HarfBuzzSharp.NativeAssets.WebAssembly": "7.3.0", "SkiaSharp": "2.88.7", "SkiaSharp.NativeAssets.Linux": "2.88.7", "SkiaSharp.NativeAssets.WebAssembly": "2.88.7"}, "compile": {"lib/net6.0/Avalonia.Skia.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.Skia.dll": {"related": ".xml"}}}, "Avalonia.Themes.Fluent/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10"}, "compile": {"lib/net6.0/Avalonia.Themes.Fluent.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.Themes.Fluent.dll": {"related": ".xml"}}}, "Avalonia.Win32/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10", "Avalonia.Angle.Windows.Natives": "2.1.0.**********", "System.Numerics.Vectors": "4.5.0"}, "compile": {"lib/net6.0/Avalonia.Win32.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.Win32.dll": {"related": ".xml"}}}, "Avalonia.X11/11.0.10": {"type": "package", "dependencies": {"Avalonia": "11.0.10", "Avalonia.FreeDesktop": "11.0.10", "Avalonia.Skia": "11.0.10"}, "compile": {"lib/net6.0/Avalonia.X11.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Avalonia.X11.dll": {"related": ".xml"}}}, "CommunityToolkit.Mvvm/8.4.0": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "DynamicData/7.9.5": {"type": "package", "dependencies": {"System.Reactive": "5.0.0"}, "compile": {"lib/net6.0/DynamicData.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/DynamicData.dll": {"related": ".xml"}}}, "HarfBuzzSharp/7.3.0": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "7.3.0", "HarfBuzzSharp.NativeAssets.macOS": "7.3.0"}, "compile": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/HarfBuzzSharp.dll": {"related": ".pdb;.xml"}}}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0": {"type": "package", "dependencies": {"HarfBuzzSharp": "7.3.0"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libHarfBuzzSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props": {}, "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets": {}}}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "MicroCom.Runtime/0.11.0": {"type": "package", "compile": {"lib/net5.0/MicroCom.Runtime.dll": {}}, "runtime": {"lib/net5.0/MicroCom.Runtime.dll": {}}}, "Microsoft.Data.Sqlite.Core/9.0.7": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore/9.0.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.7", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.7": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.7": {"type": "package"}, "Microsoft.EntityFrameworkCore.Relational/9.0.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyModel": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.7"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.7": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.7", "Microsoft.EntityFrameworkCore.Relational": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyModel": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.7"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/9.0.7": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.7": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "ReactiveUI/18.3.1": {"type": "package", "dependencies": {"DynamicData": "7.9.5", "Splat": "14.4.1"}, "compile": {"lib/net6.0/ReactiveUI.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/ReactiveUI.dll": {"related": ".xml"}}}, "Selenium.WebDriver/4.34.0": {"type": "package", "compile": {"lib/net8.0/WebDriver.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/WebDriver.dll": {"related": ".xml"}}, "build": {"buildTransitive/Selenium.WebDriver.targets": {}}}, "Selenium.WebDriver.ChromeDriver/138.0.7204.15700": {"type": "package", "build": {"build/Selenium.WebDriver.ChromeDriver.targets": {}}}, "SkiaSharp/2.88.7": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.7", "SkiaSharp.NativeAssets.macOS": "2.88.7"}, "compile": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"related": ".pdb;.xml"}}}, "SkiaSharp.NativeAssets.Linux/2.88.7": {"type": "package", "dependencies": {"SkiaSharp": "2.88.7"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"assetType": "native", "rid": "linux-x64"}}}, "SkiaSharp.NativeAssets.macOS/2.88.7": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.WebAssembly/2.88.7": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props": {}, "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets": {}}}, "SkiaSharp.NativeAssets.Win32/2.88.7": {"type": "package", "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "Splat/14.4.1": {"type": "package", "compile": {"lib/net6.0/Splat.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Splat.dll": {"related": ".xml"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}}, "runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"assetType": "native", "rid": "browser-wasm"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-armel"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-mips64"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-s390x"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-ppc64le"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-s390x"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x86"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "System.ComponentModel.Annotations/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.IO.Pipelines/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Reactive/5.0.0": {"type": "package", "compile": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "runtime": {"lib/net5.0/System.Reactive.dll": {"related": ".xml"}}, "build": {"buildTransitive/net5.0/_._": {}}}, "System.Text.Json/9.0.7": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "Tmds.DBus.Protocol/0.15.0": {"type": "package", "dependencies": {"System.IO.Pipelines": "6.0.0"}, "compile": {"lib/net6.0/Tmds.DBus.Protocol.dll": {}}, "runtime": {"lib/net6.0/Tmds.DBus.Protocol.dll": {}}}}}, "libraries": {"Avalonia/11.0.10": {"sha512": "EH1FyD1SA7G/TfLmb7JKQlZiOBqr6VzttJMtA5Hnc/c1623zJej0PRuQlqn8Ad6qWKorrKEypBGA9Gye3uWDrA==", "type": "package", "path": "avalonia/11.0.10", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Avalonia.Analyzers.dll", "analyzers/dotnet/cs/Avalonia.Generators.dll", "avalonia.11.0.10.nupkg.sha512", "avalonia.nuspec", "build/Avalonia.Generators.props", "build/Avalonia.props", "build/Avalonia.targets", "build/AvaloniaBuildTasks.props", "build/AvaloniaBuildTasks.targets", "build/AvaloniaItemSchema.xaml", "build/AvaloniaPrivateApis.targets", "build/AvaloniaVersion.props", "buildTransitive/Avalonia.Generators.props", "buildTransitive/Avalonia.props", "buildTransitive/Avalonia.targets", "buildTransitive/AvaloniaBuildTasks.props", "buildTransitive/AvaloniaBuildTasks.targets", "buildTransitive/AvaloniaItemSchema.xaml", "buildTransitive/AvaloniaPrivateApis.targets", "lib/net461/Avalonia.Base.dll", "lib/net461/Avalonia.Base.xml", "lib/net461/Avalonia.Controls.dll", "lib/net461/Avalonia.Controls.xml", "lib/net461/Avalonia.DesignerSupport.dll", "lib/net461/Avalonia.DesignerSupport.xml", "lib/net461/Avalonia.Dialogs.dll", "lib/net461/Avalonia.Dialogs.xml", "lib/net461/Avalonia.Markup.Xaml.dll", "lib/net461/Avalonia.Markup.Xaml.xml", "lib/net461/Avalonia.Markup.dll", "lib/net461/Avalonia.Markup.xml", "lib/net461/Avalonia.Metal.dll", "lib/net461/Avalonia.Metal.xml", "lib/net461/Avalonia.MicroCom.dll", "lib/net461/Avalonia.MicroCom.xml", "lib/net461/Avalonia.OpenGL.dll", "lib/net461/Avalonia.OpenGL.xml", "lib/net461/Avalonia.dll", "lib/net461/Avalonia.xml", "lib/net6.0/Avalonia.Base.dll", "lib/net6.0/Avalonia.Base.xml", "lib/net6.0/Avalonia.Controls.dll", "lib/net6.0/Avalonia.Controls.xml", "lib/net6.0/Avalonia.DesignerSupport.dll", "lib/net6.0/Avalonia.DesignerSupport.xml", "lib/net6.0/Avalonia.Dialogs.dll", "lib/net6.0/Avalonia.Dialogs.xml", "lib/net6.0/Avalonia.Markup.Xaml.dll", "lib/net6.0/Avalonia.Markup.Xaml.xml", "lib/net6.0/Avalonia.Markup.dll", "lib/net6.0/Avalonia.Markup.xml", "lib/net6.0/Avalonia.Metal.dll", "lib/net6.0/Avalonia.Metal.xml", "lib/net6.0/Avalonia.MicroCom.dll", "lib/net6.0/Avalonia.MicroCom.xml", "lib/net6.0/Avalonia.OpenGL.dll", "lib/net6.0/Avalonia.OpenGL.xml", "lib/net6.0/Avalonia.dll", "lib/net6.0/Avalonia.xml", "lib/netcoreapp2.0/Avalonia.Base.dll", "lib/netcoreapp2.0/Avalonia.Base.xml", "lib/netcoreapp2.0/Avalonia.Controls.dll", "lib/netcoreapp2.0/Avalonia.Controls.xml", "lib/netcoreapp2.0/Avalonia.DesignerSupport.dll", "lib/netcoreapp2.0/Avalonia.DesignerSupport.xml", "lib/netcoreapp2.0/Avalonia.Dialogs.dll", "lib/netcoreapp2.0/Avalonia.Dialogs.xml", "lib/netcoreapp2.0/Avalonia.Markup.Xaml.dll", "lib/netcoreapp2.0/Avalonia.Markup.Xaml.xml", "lib/netcoreapp2.0/Avalonia.Markup.dll", "lib/netcoreapp2.0/Avalonia.Markup.xml", "lib/netcoreapp2.0/Avalonia.Metal.dll", "lib/netcoreapp2.0/Avalonia.Metal.xml", "lib/netcoreapp2.0/Avalonia.MicroCom.dll", "lib/netcoreapp2.0/Avalonia.MicroCom.xml", "lib/netcoreapp2.0/Avalonia.OpenGL.dll", "lib/netcoreapp2.0/Avalonia.OpenGL.xml", "lib/netcoreapp2.0/Avalonia.dll", "lib/netcoreapp2.0/Avalonia.xml", "lib/netstandard2.0/Avalonia.Base.dll", "lib/netstandard2.0/Avalonia.Base.xml", "lib/netstandard2.0/Avalonia.Controls.dll", "lib/netstandard2.0/Avalonia.Controls.xml", "lib/netstandard2.0/Avalonia.DesignerSupport.dll", "lib/netstandard2.0/Avalonia.DesignerSupport.xml", "lib/netstandard2.0/Avalonia.Dialogs.dll", "lib/netstandard2.0/Avalonia.Dialogs.xml", "lib/netstandard2.0/Avalonia.Markup.Xaml.dll", "lib/netstandard2.0/Avalonia.Markup.Xaml.xml", "lib/netstandard2.0/Avalonia.Markup.dll", "lib/netstandard2.0/Avalonia.Markup.xml", "lib/netstandard2.0/Avalonia.Metal.dll", "lib/netstandard2.0/Avalonia.Metal.xml", "lib/netstandard2.0/Avalonia.MicroCom.dll", "lib/netstandard2.0/Avalonia.MicroCom.xml", "lib/netstandard2.0/Avalonia.OpenGL.dll", "lib/netstandard2.0/Avalonia.OpenGL.xml", "lib/netstandard2.0/Avalonia.dll", "lib/netstandard2.0/Avalonia.xml", "ref/net461/Avalonia.Base.dll", "ref/net461/Avalonia.Base.xml", "ref/net461/Avalonia.Controls.dll", "ref/net461/Avalonia.Controls.xml", "ref/net461/Avalonia.DesignerSupport.dll", "ref/net461/Avalonia.DesignerSupport.xml", "ref/net461/Avalonia.Dialogs.dll", "ref/net461/Avalonia.Dialogs.xml", "ref/net461/Avalonia.Markup.Xaml.dll", "ref/net461/Avalonia.Markup.Xaml.xml", "ref/net461/Avalonia.Markup.dll", "ref/net461/Avalonia.Markup.xml", "ref/net461/Avalonia.Metal.dll", "ref/net461/Avalonia.Metal.xml", "ref/net461/Avalonia.MicroCom.dll", "ref/net461/Avalonia.MicroCom.xml", "ref/net461/Avalonia.OpenGL.dll", "ref/net461/Avalonia.OpenGL.xml", "ref/net461/Avalonia.dll", "ref/net461/Avalonia.xml", "ref/net6.0/Avalonia.Base.dll", "ref/net6.0/Avalonia.Base.xml", "ref/net6.0/Avalonia.Controls.dll", "ref/net6.0/Avalonia.Controls.xml", "ref/net6.0/Avalonia.DesignerSupport.dll", "ref/net6.0/Avalonia.DesignerSupport.xml", "ref/net6.0/Avalonia.Dialogs.dll", "ref/net6.0/Avalonia.Dialogs.xml", "ref/net6.0/Avalonia.Markup.Xaml.dll", "ref/net6.0/Avalonia.Markup.Xaml.xml", "ref/net6.0/Avalonia.Markup.dll", "ref/net6.0/Avalonia.Markup.xml", "ref/net6.0/Avalonia.Metal.dll", "ref/net6.0/Avalonia.Metal.xml", "ref/net6.0/Avalonia.MicroCom.dll", "ref/net6.0/Avalonia.MicroCom.xml", "ref/net6.0/Avalonia.OpenGL.dll", "ref/net6.0/Avalonia.OpenGL.xml", "ref/net6.0/Avalonia.dll", "ref/net6.0/Avalonia.xml", "ref/netcoreapp2.0/Avalonia.Base.dll", "ref/netcoreapp2.0/Avalonia.Base.xml", "ref/netcoreapp2.0/Avalonia.Controls.dll", "ref/netcoreapp2.0/Avalonia.Controls.xml", "ref/netcoreapp2.0/Avalonia.DesignerSupport.dll", "ref/netcoreapp2.0/Avalonia.DesignerSupport.xml", "ref/netcoreapp2.0/Avalonia.Dialogs.dll", "ref/netcoreapp2.0/Avalonia.Dialogs.xml", "ref/netcoreapp2.0/Avalonia.Markup.Xaml.dll", "ref/netcoreapp2.0/Avalonia.Markup.Xaml.xml", "ref/netcoreapp2.0/Avalonia.Markup.dll", "ref/netcoreapp2.0/Avalonia.Markup.xml", "ref/netcoreapp2.0/Avalonia.Metal.dll", "ref/netcoreapp2.0/Avalonia.Metal.xml", "ref/netcoreapp2.0/Avalonia.MicroCom.dll", "ref/netcoreapp2.0/Avalonia.MicroCom.xml", "ref/netcoreapp2.0/Avalonia.OpenGL.dll", "ref/netcoreapp2.0/Avalonia.OpenGL.xml", "ref/netcoreapp2.0/Avalonia.dll", "ref/netcoreapp2.0/Avalonia.xml", "ref/netstandard2.0/Avalonia.Base.dll", "ref/netstandard2.0/Avalonia.Base.xml", "ref/netstandard2.0/Avalonia.Controls.dll", "ref/netstandard2.0/Avalonia.Controls.xml", "ref/netstandard2.0/Avalonia.DesignerSupport.dll", "ref/netstandard2.0/Avalonia.DesignerSupport.xml", "ref/netstandard2.0/Avalonia.Dialogs.dll", "ref/netstandard2.0/Avalonia.Dialogs.xml", "ref/netstandard2.0/Avalonia.Markup.Xaml.dll", "ref/netstandard2.0/Avalonia.Markup.Xaml.xml", "ref/netstandard2.0/Avalonia.Markup.dll", "ref/netstandard2.0/Avalonia.Markup.xml", "ref/netstandard2.0/Avalonia.Metal.dll", "ref/netstandard2.0/Avalonia.Metal.xml", "ref/netstandard2.0/Avalonia.MicroCom.dll", "ref/netstandard2.0/Avalonia.MicroCom.xml", "ref/netstandard2.0/Avalonia.OpenGL.dll", "ref/netstandard2.0/Avalonia.OpenGL.xml", "ref/netstandard2.0/Avalonia.dll", "ref/netstandard2.0/Avalonia.xml", "tools/net461/designer/Avalonia.Designer.HostApp.exe", "tools/netcoreapp2.0/designer/Avalonia.Designer.HostApp.dll", "tools/netstandard2.0/Avalonia.Build.Tasks.dll"]}, "Avalonia.Angle.Windows.Natives/2.1.0.**********": {"sha512": "Zlkkb8ipxrxNWVPCJgMO19fpcpYPP+bpOQ+jPtCFj8v+TzVvPdnGHuyv9IMvSHhhMfEpps4m4hjaP4FORQYVAA==", "type": "package", "path": "avalonia.angle.windows.natives/2.1.0.**********", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.txt", "avalonia.angle.windows.natives.2.1.0.**********.nupkg.sha512", "avalonia.angle.windows.natives.nuspec", "runtimes/win-arm64/native/av_libglesv2.dll", "runtimes/win-x64/native/av_libglesv2.dll", "runtimes/win-x86/native/av_libglesv2.dll"]}, "Avalonia.BuildServices/0.0.29": {"sha512": "U4eJLQdoDNHXtEba7MZUCwrBErBTxFp6sUewXBOdAhU0Kwzwaa/EKFcYm8kpcysjzKtfB4S0S9n0uxKZFz/ikw==", "type": "package", "path": "avalonia.buildservices/0.0.29", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "avalonia.buildservices.0.0.29.nupkg.sha512", "avalonia.buildservices.nuspec", "build/Avalonia.BuildServices.targets", "buildTransitive/Avalonia.BuildServices.targets", "tools/netstandard2.0/Avalonia.BuildServices.Collector.dll", "tools/netstandard2.0/Avalonia.BuildServices.dll", "tools/netstandard2.0/runtimeconfig.json"]}, "Avalonia.Controls.DataGrid/11.0.10": {"sha512": "a8es+nGJvXK+rh76iFRkp7UHPvaN+WeMyH7S746r25FnrBYLkvtpEjGGKRWTFX+eO2cFcDh1qHJa63aaElivww==", "type": "package", "path": "avalonia.controls.datagrid/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.controls.datagrid.11.0.10.nupkg.sha512", "avalonia.controls.datagrid.nuspec", "lib/net6.0/Avalonia.Controls.DataGrid.dll", "lib/net6.0/Avalonia.Controls.DataGrid.xml", "lib/netstandard2.0/Avalonia.Controls.DataGrid.dll", "lib/netstandard2.0/Avalonia.Controls.DataGrid.xml"]}, "Avalonia.Desktop/11.0.10": {"sha512": "jPflQRB94sr3D7tgcUuSHnvK212ZtoM2oBZVQoP/musPWiu56LZ+o7+bAt8TMGhkUBMAylZ5u+tMGCe7EUwbEA==", "type": "package", "path": "avalonia.desktop/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.desktop.11.0.10.nupkg.sha512", "avalonia.desktop.nuspec", "lib/net6.0/Avalonia.Desktop.dll", "lib/net6.0/Avalonia.Desktop.xml", "lib/netstandard2.0/Avalonia.Desktop.dll", "lib/netstandard2.0/Avalonia.Desktop.xml"]}, "Avalonia.Fonts.Inter/11.0.10": {"sha512": "4hVv6u5o8NxfPv2pl1Mg0VTYzxHkc85Wnye3sQJCQtbrd8s9v3DSEYK68zT4wBFlCtc4JHUP85PfyZuoTkf08A==", "type": "package", "path": "avalonia.fonts.inter/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.fonts.inter.11.0.10.nupkg.sha512", "avalonia.fonts.inter.nuspec", "lib/net6.0/Avalonia.Fonts.Inter.dll", "lib/net6.0/Avalonia.Fonts.Inter.xml", "lib/netstandard2.0/Avalonia.Fonts.Inter.dll", "lib/netstandard2.0/Avalonia.Fonts.Inter.xml"]}, "Avalonia.FreeDesktop/11.0.10": {"sha512": "QLVn1pjCe5ez4cH5B+NmhT61xK502mjxPxkswIbag5FB45tuNOF5zRszH+dN81rNw+VSf/J6PVXdmHz/FojyDw==", "type": "package", "path": "avalonia.freedesktop/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.freedesktop.11.0.10.nupkg.sha512", "avalonia.freedesktop.nuspec", "lib/net6.0/Avalonia.FreeDesktop.dll", "lib/net6.0/Avalonia.FreeDesktop.xml", "lib/netstandard2.0/Avalonia.FreeDesktop.dll", "lib/netstandard2.0/Avalonia.FreeDesktop.xml"]}, "Avalonia.Native/11.0.10": {"sha512": "eJUPh4VOBomF36DzJQ+M+T1lVBVhpK3Ryx3xNnhVvYcW3dPNgugXApSbxulSipWnMfJh4DAKQD2Mt3ahy3Tp8Q==", "type": "package", "path": "avalonia.native/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.native.11.0.10.nupkg.sha512", "avalonia.native.nuspec", "lib/net6.0/Avalonia.Native.dll", "lib/net6.0/Avalonia.Native.xml", "lib/netstandard2.0/Avalonia.Native.dll", "lib/netstandard2.0/Avalonia.Native.xml", "runtimes/osx/native/libAvaloniaNative.dylib"]}, "Avalonia.ReactiveUI/11.0.10": {"sha512": "3haOqr1p4RguatFmKZ4dU1sPrw7oAadnoey6KCN60p3V7roBY0n28qAgwtrq0oH5wdXrYlhfqCws8aqew3LK8w==", "type": "package", "path": "avalonia.reactiveui/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.reactiveui.11.0.10.nupkg.sha512", "avalonia.reactiveui.nuspec", "lib/net6.0/Avalonia.ReactiveUI.dll", "lib/net6.0/Avalonia.ReactiveUI.xml", "lib/netstandard2.0/Avalonia.ReactiveUI.dll", "lib/netstandard2.0/Avalonia.ReactiveUI.xml"]}, "Avalonia.Remote.Protocol/11.0.10": {"sha512": "4/zXSx7P2+4g7nIkSd8aIfEfRnvHjpeSEBjJznzE5iJBYO98B78wDPWEkyN8sQBJnLghaNKKsGCApx3g5R0Gsg==", "type": "package", "path": "avalonia.remote.protocol/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.remote.protocol.11.0.10.nupkg.sha512", "avalonia.remote.protocol.nuspec", "lib/net6.0/Avalonia.Remote.Protocol.dll", "lib/net6.0/Avalonia.Remote.Protocol.xml", "lib/netstandard2.0/Avalonia.Remote.Protocol.dll", "lib/netstandard2.0/Avalonia.Remote.Protocol.xml"]}, "Avalonia.Skia/11.0.10": {"sha512": "0rDySePqUkg6cMBZEXp+5P04XgyIXOcqhsCaWhrwFAqxcp5qKjILSCNvt0pden3TqCRjuNuGaWGV+qyk58l9vA==", "type": "package", "path": "avalonia.skia/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.skia.11.0.10.nupkg.sha512", "avalonia.skia.nuspec", "lib/net6.0/Avalonia.Skia.dll", "lib/net6.0/Avalonia.Skia.xml", "lib/netstandard2.0/Avalonia.Skia.dll", "lib/netstandard2.0/Avalonia.Skia.xml"]}, "Avalonia.Themes.Fluent/11.0.10": {"sha512": "V7xu7ddtUFwYHb+u3S4he2/PGGUHkeTI66j3PyGTZi0Uz8Ma4prb/Lgz7Qd/vpO4lukxw1G4DmxB1tClNS9qUw==", "type": "package", "path": "avalonia.themes.fluent/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.themes.fluent.11.0.10.nupkg.sha512", "avalonia.themes.fluent.nuspec", "lib/net6.0/Avalonia.Themes.Fluent.dll", "lib/net6.0/Avalonia.Themes.Fluent.xml", "lib/netstandard2.0/Avalonia.Themes.Fluent.dll", "lib/netstandard2.0/Avalonia.Themes.Fluent.xml"]}, "Avalonia.Win32/11.0.10": {"sha512": "XD4fiRpiLHB6Q8e9Vevj42rYQvdnrqs56Jwu9T4UaClcuk578fUH1LP/vl9q/n8LrEUyng01Q7vtpp5sgwsXDw==", "type": "package", "path": "avalonia.win32/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.win32.11.0.10.nupkg.sha512", "avalonia.win32.nuspec", "lib/net6.0/Avalonia.Win32.dll", "lib/net6.0/Avalonia.Win32.xml", "lib/netstandard2.0/Avalonia.Win32.dll", "lib/netstandard2.0/Avalonia.Win32.xml"]}, "Avalonia.X11/11.0.10": {"sha512": "WUhPBIYqjJdBf1TgUARYNyH1clntZ435eQzR+db4fxrg4OemePX6nJTp1Lh7G/hTVEvRgWCoYFccqm/6vsfJQQ==", "type": "package", "path": "avalonia.x11/11.0.10", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "avalonia.x11.11.0.10.nupkg.sha512", "avalonia.x11.nuspec", "lib/net6.0/Avalonia.X11.dll", "lib/net6.0/Avalonia.X11.xml", "lib/netstandard2.0/Avalonia.X11.dll", "lib/netstandard2.0/Avalonia.X11.xml"]}, "CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "DynamicData/7.9.5": {"sha512": "xFwVha7o3qUtVYxco5p+7Urcztc/m1gmaEUxOG0i7LNe+vfCfyb0ECAsT2FLm3zOPHb0g8s9qVu5LfPKfRNVng==", "type": "package", "path": "dynamicdata/7.9.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "dynamicdata.7.9.5.nupkg.sha512", "dynamicdata.nuspec", "lib/net462/DynamicData.dll", "lib/net462/DynamicData.xml", "lib/net6.0/DynamicData.dll", "lib/net6.0/DynamicData.xml", "lib/netstandard2.0/DynamicData.dll", "lib/netstandard2.0/DynamicData.xml", "lib/uap10.0.16299/DynamicData.dll", "lib/uap10.0.16299/DynamicData.pri", "lib/uap10.0.16299/DynamicData.xml", "logo.png"]}, "HarfBuzzSharp/7.3.0": {"sha512": "OrQLaxtZMIeS2yHSUtsKzeSdk9CPaCpyJ/JCs+wLfRGatjE8MLUS6LGj6vdbGRxqRavcXs79C9O3oWe6FJR0JQ==", "type": "package", "path": "harfbuzzsharp/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "harfbuzzsharp.7.3.0.nupkg.sha512", "harfbuzzsharp.nuspec", "lib/monoandroid1.0/HarfBuzzSharp.dll", "lib/monoandroid1.0/HarfBuzzSharp.pdb", "lib/monoandroid1.0/HarfBuzzSharp.xml", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net462/HarfBuzzSharp.xml", "lib/net6.0-android30.0/HarfBuzzSharp.dll", "lib/net6.0-android30.0/HarfBuzzSharp.pdb", "lib/net6.0-android30.0/HarfBuzzSharp.xml", "lib/net6.0-ios13.6/HarfBuzzSharp.dll", "lib/net6.0-ios13.6/HarfBuzzSharp.pdb", "lib/net6.0-ios13.6/HarfBuzzSharp.xml", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.dll", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.pdb", "lib/net6.0-maccatalyst13.5/HarfBuzzSharp.xml", "lib/net6.0-macos10.15/HarfBuzzSharp.dll", "lib/net6.0-macos10.15/HarfBuzzSharp.pdb", "lib/net6.0-macos10.15/HarfBuzzSharp.xml", "lib/net6.0-tvos13.4/HarfBuzzSharp.dll", "lib/net6.0-tvos13.4/HarfBuzzSharp.pdb", "lib/net6.0-tvos13.4/HarfBuzzSharp.xml", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.xml", "lib/netcoreapp3.1/HarfBuzzSharp.dll", "lib/netcoreapp3.1/HarfBuzzSharp.pdb", "lib/netcoreapp3.1/HarfBuzzSharp.xml", "lib/netstandard1.3/HarfBuzzSharp.dll", "lib/netstandard1.3/HarfBuzzSharp.pdb", "lib/netstandard1.3/HarfBuzzSharp.xml", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.xml", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.xml", "lib/tizen40/HarfBuzzSharp.dll", "lib/tizen40/HarfBuzzSharp.pdb", "lib/tizen40/HarfBuzzSharp.xml", "lib/uap10.0.10240/HarfBuzzSharp.dll", "lib/uap10.0.10240/HarfBuzzSharp.pdb", "lib/uap10.0.10240/HarfBuzzSharp.xml", "lib/uap10.0.16299/HarfBuzzSharp.dll", "lib/uap10.0.16299/HarfBuzzSharp.pdb", "lib/uap10.0.16299/HarfBuzzSharp.xml", "lib/xamarinios1.0/HarfBuzzSharp.dll", "lib/xamarinios1.0/HarfBuzzSharp.pdb", "lib/xamarinios1.0/HarfBuzzSharp.xml", "lib/xamarinmac2.0/HarfBuzzSharp.dll", "lib/xamarinmac2.0/HarfBuzzSharp.pdb", "lib/xamarinmac2.0/HarfBuzzSharp.xml", "lib/xamarintvos1.0/HarfBuzzSharp.dll", "lib/xamarintvos1.0/HarfBuzzSharp.pdb", "lib/xamarintvos1.0/HarfBuzzSharp.xml", "lib/xamarinwatchos1.0/HarfBuzzSharp.dll", "lib/xamarinwatchos1.0/HarfBuzzSharp.pdb", "lib/xamarinwatchos1.0/HarfBuzzSharp.xml"]}, "HarfBuzzSharp.NativeAssets.Linux/7.3.0": {"sha512": "m6F2pEBTN0zTRgQ3caJQRGQkZZizZwHHCbu+rTv+gvwteNBOpqOLD5GE4dB9TFjNNpnyHXtfuMD86JuUra9UvA==", "type": "package", "path": "harfbuzzsharp.nativeassets.linux/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Linux.targets", "harfbuzzsharp.nativeassets.linux.7.3.0.nupkg.sha512", "harfbuzzsharp.nativeassets.linux.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libHarfBuzzSharp.so", "runtimes/linux-arm64/native/libHarfBuzzSharp.so", "runtimes/linux-musl-x64/native/libHarfBuzzSharp.so", "runtimes/linux-x64/native/libHarfBuzzSharp.so"]}, "HarfBuzzSharp.NativeAssets.macOS/7.3.0": {"sha512": "LWcFJ39j+dN0KK8c/GJJZPPZPL9TqT2FA42/LRGqzUMmSm5LYbINOMnPvUr7RuLR6RFSmKIrgrlgObR8G5ho2A==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.7.3.0.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.WebAssembly/7.3.0": {"sha512": "cnl4I6P+VeujfSSD3ZrC5f0TrTGt9EKgCOoZ3LpgLI2xobBKLi5bxOaN2oY6B0xVXxQEhEaWBotg7AuECg00Iw==", "type": "package", "path": "harfbuzzsharp.nativeassets.webassembly/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props", "build/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets", "build/netstandard1.0/libHarfBuzzSharp.a/2.0.23/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/2.0.6/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/mt,simd/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/simd/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.12/st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/simd,mt/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/simd,st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.34/st/libHarfBuzzSharp.a", "build/netstandard1.0/libHarfBuzzSharp.a/3.1.7/libHarfBuzzSharp.a", "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.props", "buildTransitive/netstandard1.0/HarfBuzzSharp.NativeAssets.WebAssembly.targets", "harfbuzzsharp.nativeassets.webassembly.7.3.0.nupkg.sha512", "harfbuzzsharp.nativeassets.webassembly.nuspec", "lib/netstandard1.0/_._"]}, "HarfBuzzSharp.NativeAssets.Win32/7.3.0": {"sha512": "ulEewLMk+dNmbmpy15ny/YusI6JNUWqchF080TV2jgfFBXPXjWm767JleDi/S7hp8eDeEN6GYIIxpvNr5fLvIw==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/7.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.7.3.0.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "MicroCom.Runtime/0.11.0": {"sha512": "MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA==", "type": "package", "path": "microcom.runtime/0.11.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/MicroCom.Runtime.dll", "lib/netstandard2.0/MicroCom.Runtime.dll", "microcom.runtime.0.11.0.nupkg.sha512", "microcom.runtime.nuspec"]}, "Microsoft.Data.Sqlite.Core/9.0.7": {"sha512": "yjlU0Wu0tAexFPlo/bbkYFMWyEkVHqr5AONyh91YJ4KH+hys+eAzHQxd14aZwtQOgpJ5s9r3QL9+tVJww8w69Q==", "type": "package", "path": "microsoft.data.sqlite.core/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Data.Sqlite.dll", "lib/net6.0/Microsoft.Data.Sqlite.xml", "lib/net8.0/Microsoft.Data.Sqlite.dll", "lib/net8.0/Microsoft.Data.Sqlite.xml", "lib/netstandard2.0/Microsoft.Data.Sqlite.dll", "lib/netstandard2.0/Microsoft.Data.Sqlite.xml", "microsoft.data.sqlite.core.9.0.7.nupkg.sha512", "microsoft.data.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore/9.0.7": {"sha512": "PbD0q5ax15r91jD4TN7xbDCjldZSz4JfpYN4ZZjAkWeUyROkV92Ydg0O2/1keFA+2u3KPsDkJMmBKv2zQ06ZVg==", "type": "package", "path": "microsoft.entityframeworkcore/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.7": {"sha512": "YUXNerEkCf4OANO+zjuMznpUW7R8XxSCqmBfYhBrbrJVc09i84KkNgeUTaOUXCGogSK/3d7ORRhMqfUobnejBg==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.7": {"sha512": "HqiPjAvjVOsyA1svnjL81/Wk2MRQYMK/lxKVWvw0f5IcA//VcxBepVSAqe7CFirdsPXqe8rFKEwZROWZTz7Jqw==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "microsoft.entityframeworkcore.analyzers.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/9.0.7": {"sha512": "Yo5joquG7L79H5BhtpqP8apu+KFOAYfvmj0dZnVkPElBY14wY5qva0SOcrDWzYw5BrJrhIArfCcJCJHBvMYiKg==", "type": "package", "path": "microsoft.entityframeworkcore.relational/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.7": {"sha512": "87dAv0nX4rBIa29L7sZdUZ1FE4NDn9J51g6WJ+j5dTUQwNEg52YDEmo+/TxBtRnBSAca9boo80k8F7+LzUo2qQ==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/_._", "microsoft.entityframeworkcore.sqlite.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.7": {"sha512": "DLB8n5Z7U8+xCkh+NSrvOlylCmveDg5RpPdqBftq5nU8Yt3vIdBg0X/YkESGDBWUL9h0vxuhgH2aqXL3FYz5tQ==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.xml", "microsoft.entityframeworkcore.sqlite.core.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.core.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"sha512": "30necCQehcg9lFkMEIE7HczcoYGML8GUH6jlincA18d896fLZM9wl5tpTPJHgzANQE/6KXRLZSWbgevgg5csSw==", "type": "package", "path": "microsoft.extensions.caching.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"sha512": "nDu6c8fwrHQYccLnWnvyElrdkL3rZ97TZNqL+niMFUcApVBHdpDmKcRvciGymJ4Y0iLDTOo5J2XhDQEbNb+dFg==", "type": "package", "path": "microsoft.extensions.caching.memory/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net9.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net9.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.9.0.7.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"sha512": "lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"sha512": "i05AYA91vgq0as84ROVCyltD2gnxaba/f1Qw2rG7mUsS0gv8cPTr1Gm7jPQHq7JTr4MJoQUcanLVs16tIOUJaQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"sha512": "iPK1FxbGFr2Xb+4Y+dTYI8Gupu9pOi8I3JPuPsrogUmEhe2hzZ9LpCmolMEBhVDo2ikcSr7G5zYiwaapHSQTew==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/9.0.7": {"sha512": "aXEt8QW1Fj9aC81GfkMtfip4wfbkEA7VBvNkx6Rx6ZKyqXIF/9qzRtH6v/2096IDK4lt6dlQp5Ajf+kjHfUdOA==", "type": "package", "path": "microsoft.extensions.dependencymodel/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/net9.0/Microsoft.Extensions.DependencyModel.dll", "lib/net9.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.9.0.7.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.7": {"sha512": "fdIeQpXYV8yxSWG03cCbU2Otdrq4NWuhnQLXokWLv3L9YcK055E7u8WFJvP+uuP4CFeCEoqZQL4yPcjuXhCZrg==", "type": "package", "path": "microsoft.extensions.logging/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.7.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"sha512": "sMM6NEAdUTE/elJ2wqjOi0iBWqZmSyaTByLF9e8XHv6DRJFFnOe0N+s8Uc6C91E4SboQCfLswaBIZ+9ZXA98AA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.7": {"sha512": "trJnF6cRWgR5uMmHpGoHmM1wOVFdIYlELlkO9zX+RfieK0321Y55zrcs4AaEymKup7dxgEN/uJU25CAcMNQRXw==", "type": "package", "path": "microsoft.extensions.options/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.7.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.7": {"sha512": "ti/zD9BuuO50IqlvhWQs9GHxkCmoph5BHjGiWKdg2t6Or8XoyAfRJiKag+uvd/fpASnNklfsB01WpZ4fhAe0VQ==", "type": "package", "path": "microsoft.extensions.primitives/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.7.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "ReactiveUI/18.3.1": {"sha512": "0tclGtjrRPfA2gbjiM7O3DeNmo6/TpDn7CMN6jgzDrbgrnysM7oEzjGEeXbtXaOxH6kEf6RiMKWobZoSgbBXhQ==", "type": "package", "path": "reactiveui/18.3.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "lib/monoandroid11.0/ReactiveUI.dll", "lib/monoandroid11.0/ReactiveUI.pdb", "lib/monoandroid11.0/ReactiveUI.xml", "lib/monoandroid12.0/ReactiveUI.dll", "lib/monoandroid12.0/ReactiveUI.pdb", "lib/monoandroid12.0/ReactiveUI.xml", "lib/net462/ReactiveUI.dll", "lib/net462/ReactiveUI.xml", "lib/net472/ReactiveUI.dll", "lib/net472/ReactiveUI.xml", "lib/net6.0-android31.0/ReactiveUI.dll", "lib/net6.0-android31.0/ReactiveUI.xml", "lib/net6.0-ios15.4/ReactiveUI.dll", "lib/net6.0-ios15.4/ReactiveUI.xml", "lib/net6.0-maccatalyst15.4/ReactiveUI.dll", "lib/net6.0-maccatalyst15.4/ReactiveUI.xml", "lib/net6.0-macos12.3/ReactiveUI.dll", "lib/net6.0-macos12.3/ReactiveUI.xml", "lib/net6.0-tvos15.4/ReactiveUI.dll", "lib/net6.0-tvos15.4/ReactiveUI.xml", "lib/net6.0/ReactiveUI.dll", "lib/net6.0/ReactiveUI.xml", "lib/netstandard2.0/ReactiveUI.dll", "lib/netstandard2.0/ReactiveUI.xml", "lib/tizen40/ReactiveUI.dll", "lib/tizen40/ReactiveUI.xml", "lib/uap10.0.16299/ReactiveUI.dll", "lib/uap10.0.16299/ReactiveUI.pri", "lib/uap10.0.16299/ReactiveUI.xml", "lib/xamarinios10/ReactiveUI.dll", "lib/xamarinios10/ReactiveUI.xml", "lib/xamarinmac20/ReactiveUI.dll", "lib/xamarinmac20/ReactiveUI.xml", "lib/xamarintvos10/ReactiveUI.dll", "lib/xamarintvos10/ReactiveUI.xml", "logo.png", "reactiveui.18.3.1.nupkg.sha512", "reactiveui.nuspec"]}, "Selenium.WebDriver/4.34.0": {"sha512": "uNx+GF7WugHDPV2zpGDPlbSn3STQ6n0xFskFSeJdhEuuUTkIDfAsYnjVUiQidWDpy7mKzTz53ae7Thjghl3dng==", "type": "package", "path": "selenium.webdriver/4.34.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Selenium.WebDriver.targets", "buildTransitive/Selenium.WebDriver.targets", "icon.png", "lib/net8.0/WebDriver.dll", "lib/net8.0/WebDriver.xml", "lib/netstandard2.0/WebDriver.dll", "lib/netstandard2.0/WebDriver.xml", "manager/linux/selenium-manager", "manager/macos/selenium-manager", "manager/windows/selenium-manager.exe", "selenium.webdriver.4.34.0.nupkg.sha512", "selenium.webdriver.nuspec"]}, "Selenium.WebDriver.ChromeDriver/138.0.7204.15700": {"sha512": "TXuEqxz4dHy+Xp+C+lQJAqqAACO8EWP92l+wc5XtWBF5JQsF0Qua/aaGgeTnNYwnQLwJcsvMJJjSzyvXdCYQKA==", "type": "package", "path": "selenium.webdriver.chromedriver/138.0.7204.15700", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "build/DefinePropertiesChromeDriver.targets", "build/Selenium.WebDriver.ChromeDriver.targets", "driver/linux64/LICENSE.chromedriver", "driver/linux64/chromedriver", "driver/mac64/LICENSE.chromedriver", "driver/mac64/chromedriver", "driver/mac64arm/LICENSE.chromedriver", "driver/mac64arm/chromedriver", "driver/win32/LICENSE.chromedriver", "driver/win32/chromedriver.exe", "nupkg-icon.png", "selenium.webdriver.chromedriver.138.0.7204.15700.nupkg.sha512", "selenium.webdriver.chromedriver.nuspec"]}, "SkiaSharp/2.88.7": {"sha512": "LJHAMrbWO00J7jXWLWehyjqFo29T4VzABimfJb4nICqpEe3c/KvQGWL4ItON8ymzhxYOeFgyxeRzuNzO4GHSug==", "type": "package", "path": "skiasharp/2.88.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/monoandroid1.0/SkiaSharp.dll", "lib/monoandroid1.0/SkiaSharp.pdb", "lib/monoandroid1.0/SkiaSharp.xml", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net462/SkiaSharp.xml", "lib/net6.0-android30.0/SkiaSharp.dll", "lib/net6.0-android30.0/SkiaSharp.pdb", "lib/net6.0-android30.0/SkiaSharp.xml", "lib/net6.0-ios13.6/SkiaSharp.dll", "lib/net6.0-ios13.6/SkiaSharp.pdb", "lib/net6.0-ios13.6/SkiaSharp.xml", "lib/net6.0-maccatalyst13.5/SkiaSharp.dll", "lib/net6.0-maccatalyst13.5/SkiaSharp.pdb", "lib/net6.0-maccatalyst13.5/SkiaSharp.xml", "lib/net6.0-macos10.15/SkiaSharp.dll", "lib/net6.0-macos10.15/SkiaSharp.pdb", "lib/net6.0-macos10.15/SkiaSharp.xml", "lib/net6.0-tizen7.0/SkiaSharp.dll", "lib/net6.0-tizen7.0/SkiaSharp.pdb", "lib/net6.0-tizen7.0/SkiaSharp.xml", "lib/net6.0-tvos13.4/SkiaSharp.dll", "lib/net6.0-tvos13.4/SkiaSharp.pdb", "lib/net6.0-tvos13.4/SkiaSharp.xml", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.xml", "lib/netcoreapp3.1/SkiaSharp.dll", "lib/netcoreapp3.1/SkiaSharp.pdb", "lib/netcoreapp3.1/SkiaSharp.xml", "lib/netstandard1.3/SkiaSharp.dll", "lib/netstandard1.3/SkiaSharp.pdb", "lib/netstandard1.3/SkiaSharp.xml", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.xml", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.xml", "lib/tizen40/SkiaSharp.dll", "lib/tizen40/SkiaSharp.pdb", "lib/tizen40/SkiaSharp.xml", "lib/uap10.0.10240/SkiaSharp.dll", "lib/uap10.0.10240/SkiaSharp.pdb", "lib/uap10.0.10240/SkiaSharp.xml", "lib/uap10.0.16299/SkiaSharp.dll", "lib/uap10.0.16299/SkiaSharp.pdb", "lib/uap10.0.16299/SkiaSharp.xml", "lib/xamarinios1.0/SkiaSharp.dll", "lib/xamarinios1.0/SkiaSharp.pdb", "lib/xamarinios1.0/SkiaSharp.xml", "lib/xamarinmac2.0/SkiaSharp.dll", "lib/xamarinmac2.0/SkiaSharp.pdb", "lib/xamarinmac2.0/SkiaSharp.xml", "lib/xamarintvos1.0/SkiaSharp.dll", "lib/xamarintvos1.0/SkiaSharp.pdb", "lib/xamarintvos1.0/SkiaSharp.xml", "lib/xamarinwatchos1.0/SkiaSharp.dll", "lib/xamarinwatchos1.0/SkiaSharp.pdb", "lib/xamarinwatchos1.0/SkiaSharp.xml", "skiasharp.2.88.7.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.NativeAssets.Linux/2.88.7": {"sha512": "i9VitS7/5D8Te3B1Gu7F6kakW9PYVnI3YC6MoR6NidreD9hDl1EIOQEBaa0eBsOsWNX5Bz92OVf6+7KbDrJvyg==", "type": "package", "path": "skiasharp.nativeassets.linux/2.88.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Linux.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Linux.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/linux-arm/native/libSkiaSharp.so", "runtimes/linux-arm64/native/libSkiaSharp.so", "runtimes/linux-musl-x64/native/libSkiaSharp.so", "runtimes/linux-x64/native/libSkiaSharp.so", "skiasharp.nativeassets.linux.2.88.7.nupkg.sha512", "skiasharp.nativeassets.linux.nuspec"]}, "SkiaSharp.NativeAssets.macOS/2.88.7": {"sha512": "3jNzco4VjcYPFNxR9aNWcgweFXbTSdM1VpNRzCS4X0i1A1OuNqcaulrAvmntNpujeWxHo9e6WGh6FN8Jf5+XhA==", "type": "package", "path": "skiasharp.nativeassets.macos/2.88.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.macOS.targets", "build/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "build/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net6.0-macos10.15/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/xamarinmac2.0/SkiaSharp.NativeAssets.macOS.targets", "lib/net462/_._", "lib/net6.0-macos10.15/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "lib/xamarinmac2.0/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.2.88.7.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.WebAssembly/2.88.7": {"sha512": "elmOOQTO0QXmnnHx7GliF7VNJqZkWgPqqPsXapEN0EEZJ9fGblYWmD6cqxTwaRTMCUFeLpn8+gTzY8j000MxZQ==", "type": "package", "path": "skiasharp.nativeassets.webassembly/2.88.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props", "build/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets", "build/netstandard1.0/libSkiaSharp.a/2.0.23/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/2.0.6/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/mt,simd/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/simd/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.12/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/simd,mt/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/simd,st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.34/st/libSkiaSharp.a", "build/netstandard1.0/libSkiaSharp.a/3.1.7/libSkiaSharp.a", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.props", "buildTransitive/netstandard1.0/SkiaSharp.NativeAssets.WebAssembly.targets", "lib/netstandard1.0/_._", "skiasharp.nativeassets.webassembly.2.88.7.nupkg.sha512", "skiasharp.nativeassets.webassembly.nuspec"]}, "SkiaSharp.NativeAssets.Win32/2.88.7": {"sha512": "BCXmWdQ0oVck9vRwC8U3ocSaTHEx28VB+6qw9OxGIMQ86iO5bp4Flqk3IXH0l9Pbr7vWAUOpI212iaL9mTaUZQ==", "type": "package", "path": "skiasharp.nativeassets.win32/2.88.7", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "THIRD-PARTY-NOTICES.txt", "build/net462/SkiaSharp.NativeAssets.Win32.targets", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "lib/net462/_._", "lib/net6.0/_._", "lib/netcoreapp3.1/_._", "lib/netstandard1.3/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.2.88.7.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "Splat/14.4.1": {"sha512": "Z1Mncnzm9pNIaIbZ/EWH6x5ESnKsmAvu8HP4StBRw+yhz0lzE7LCbt22TNTPaFrYLYbYCbGQIc/61yuSnpLidg==", "type": "package", "path": "splat/14.4.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "lib/net6.0/Splat.dll", "lib/net6.0/Splat.xml", "lib/netstandard2.0/Splat.dll", "lib/netstandard2.0/Splat.xml", "splat.14.4.1.nupkg.sha512", "splat.nuspec"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"sha512": "UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "type": "package", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid90/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.xml", "lib/net6.0-ios14.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-ios14.2/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-tvos10.0/SQLitePCLRaw.batteries_v2.dll", "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll", "lib/xamarinios10/SQLitePCLRaw.batteries_v2.dll", "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.bundle_e_sqlite3.nuspec"]}, "SQLitePCLRaw.core/2.1.10": {"sha512": "Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "type": "package", "path": "sqlitepclraw.core/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.core.dll", "sqlitepclraw.core.2.1.10.nupkg.sha512", "sqlitepclraw.core.nuspec"]}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"sha512": "mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "type": "package", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/net461/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets", "lib/net461/_._", "lib/netstandard2.0/_._", "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a", "runtimes/linux-arm/native/libe_sqlite3.so", "runtimes/linux-arm64/native/libe_sqlite3.so", "runtimes/linux-armel/native/libe_sqlite3.so", "runtimes/linux-mips64/native/libe_sqlite3.so", "runtimes/linux-musl-arm/native/libe_sqlite3.so", "runtimes/linux-musl-arm64/native/libe_sqlite3.so", "runtimes/linux-musl-s390x/native/libe_sqlite3.so", "runtimes/linux-musl-x64/native/libe_sqlite3.so", "runtimes/linux-ppc64le/native/libe_sqlite3.so", "runtimes/linux-s390x/native/libe_sqlite3.so", "runtimes/linux-x64/native/libe_sqlite3.so", "runtimes/linux-x86/native/libe_sqlite3.so", "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib", "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib", "runtimes/osx-arm64/native/libe_sqlite3.dylib", "runtimes/osx-x64/native/libe_sqlite3.dylib", "runtimes/win-arm/native/e_sqlite3.dll", "runtimes/win-arm64/native/e_sqlite3.dll", "runtimes/win-x64/native/e_sqlite3.dll", "runtimes/win-x86/native/e_sqlite3.dll", "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll", "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.lib.e_sqlite3.nuspec"]}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"sha512": "uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "type": "package", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll", "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.provider.e_sqlite3.nuspec"]}, "System.ComponentModel.Annotations/4.5.0": {"sha512": "UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg==", "type": "package", "path": "system.componentmodel.annotations/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/portable-net45+win8/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.4.5.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO.Pipelines/6.0.0": {"sha512": "mXX66shZ4xLlI3vNLaJ0lt8OIZdmXTvIqXRdQX5HLVGSkLhINLsVhyZuX2UdRFnOGkqnwmMUs40pIIQ7mna4+A==", "type": "package", "path": "system.io.pipelines/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.IO.Pipelines.dll", "lib/net461/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/netcoreapp3.1/System.IO.Pipelines.dll", "lib/netcoreapp3.1/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.6.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Reactive/5.0.0": {"sha512": "erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "type": "package", "path": "system.reactive/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "build/net5.0/_._", "build/netcoreapp3.1/System.Reactive.dll", "build/netcoreapp3.1/System.Reactive.targets", "build/netcoreapp3.1/System.Reactive.xml", "buildTransitive/net5.0/_._", "buildTransitive/netcoreapp3.1/System.Reactive.targets", "lib/net472/System.Reactive.dll", "lib/net472/System.Reactive.xml", "lib/net5.0-windows10.0.19041/System.Reactive.dll", "lib/net5.0-windows10.0.19041/System.Reactive.xml", "lib/net5.0/System.Reactive.dll", "lib/net5.0/System.Reactive.xml", "lib/netcoreapp3.1/_._", "lib/netstandard2.0/System.Reactive.dll", "lib/netstandard2.0/System.Reactive.xml", "lib/uap10.0.16299/System.Reactive.dll", "lib/uap10.0.16299/System.Reactive.pri", "lib/uap10.0.16299/System.Reactive.xml", "system.reactive.5.0.0.nupkg.sha512", "system.reactive.nuspec"]}, "System.Text.Json/9.0.7": {"sha512": "u/lN2FEEXs3ghj2ta8tWA4r2MS9Yni07K7jDmnz8h1UPDf0lIIIEMkWx383Zz4fJjJio7gDl+00RYuQ/7R8ZQw==", "type": "package", "path": "system.text.json/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.7.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "Tmds.DBus.Protocol/0.15.0": {"sha512": "QVo/Y39nTYcCKBqrZuwHjXdwaky0yTQPIT3qUTEEK2MZfDtZWrJ2XyZ59zH8LBgB2fL5cWaTuP2pBTpGz/GeDQ==", "type": "package", "path": "tmds.dbus.protocol/0.15.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0/Tmds.DBus.Protocol.dll", "lib/netstandard2.0/Tmds.DBus.Protocol.dll", "lib/netstandard2.1/Tmds.DBus.Protocol.dll", "tmds.dbus.protocol.0.15.0.nupkg.sha512", "tmds.dbus.protocol.nuspec"]}}, "projectFileDependencyGroups": {"net9.0": ["Avalonia >= 11.0.10", "Avalonia.Controls.DataGrid >= 11.0.10", "Avalonia.Desktop >= 11.0.10", "Avalonia.Fonts.Inter >= 11.0.10", "Avalonia.ReactiveUI >= 11.0.10", "Avalonia.Themes.Fluent >= 11.0.10", "CommunityToolkit.Mvvm >= 8.4.0", "Microsoft.EntityFrameworkCore.Sqlite >= 9.0.7", "Selenium.WebDriver >= 4.34.0", "Selenium.WebDriver.ChromeDriver >= 138.0.7204.15700"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/WhatsBroadcasterProDesktop.csproj", "projectName": "WhatsBroadcasterProDesktop", "projectPath": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/WhatsBroadcasterProDesktop.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Avalonia": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Controls.DataGrid": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Desktop": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Fonts.Inter": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.ReactiveUI": {"target": "Package", "version": "[11.0.10, )"}, "Avalonia.Themes.Fluent": {"target": "Package", "version": "[11.0.10, )"}, "CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.34.0, )"}, "Selenium.WebDriver.ChromeDriver": {"target": "Package", "version": "[138.0.7204.15700, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.202/PortableRuntimeIdentifierGraph.json"}}}}