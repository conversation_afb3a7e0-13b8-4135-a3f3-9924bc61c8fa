{"version": 2, "dgSpecHash": "fIks1enE7bs=", "success": true, "projectFilePath": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterProDesktop/WhatsBroadcasterProDesktop.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/avalonia/11.0.10/avalonia.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.angle.windows.natives/2.1.0.2023020321/avalonia.angle.windows.natives.2.1.0.2023020321.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.buildservices/0.0.29/avalonia.buildservices.0.0.29.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.controls.datagrid/11.0.10/avalonia.controls.datagrid.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.desktop/11.0.10/avalonia.desktop.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.fonts.inter/11.0.10/avalonia.fonts.inter.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.freedesktop/11.0.10/avalonia.freedesktop.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.native/11.0.10/avalonia.native.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.reactiveui/11.0.10/avalonia.reactiveui.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.remote.protocol/11.0.10/avalonia.remote.protocol.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.skia/11.0.10/avalonia.skia.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.themes.fluent/11.0.10/avalonia.themes.fluent.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.win32/11.0.10/avalonia.win32.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/avalonia.x11/11.0.10/avalonia.x11.11.0.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/communitytoolkit.mvvm/8.4.0/communitytoolkit.mvvm.8.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/dynamicdata/7.9.5/dynamicdata.7.9.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp/7.3.0/harfbuzzsharp.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp.nativeassets.linux/7.3.0/harfbuzzsharp.nativeassets.linux.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp.nativeassets.macos/7.3.0/harfbuzzsharp.nativeassets.macos.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp.nativeassets.webassembly/7.3.0/harfbuzzsharp.nativeassets.webassembly.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/harfbuzzsharp.nativeassets.win32/7.3.0/harfbuzzsharp.nativeassets.win32.7.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microcom.runtime/0.11.0/microcom.runtime.0.11.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.data.sqlite.core/9.0.7/microsoft.data.sqlite.core.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore/9.0.7/microsoft.entityframeworkcore.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.abstractions/9.0.7/microsoft.entityframeworkcore.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.analyzers/9.0.7/microsoft.entityframeworkcore.analyzers.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.relational/9.0.7/microsoft.entityframeworkcore.relational.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.sqlite/9.0.7/microsoft.entityframeworkcore.sqlite.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.sqlite.core/9.0.7/microsoft.entityframeworkcore.sqlite.core.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/9.0.7/microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.memory/9.0.7/microsoft.extensions.caching.memory.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.7/microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.7/microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.7/microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencymodel/9.0.7/microsoft.extensions.dependencymodel.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.7/microsoft.extensions.logging.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.7/microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/9.0.7/microsoft.extensions.options.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.7/microsoft.extensions.primitives.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.win32.systemevents/8.0.0/microsoft.win32.systemevents.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/reactiveui/18.3.1/reactiveui.18.3.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/selenium.webdriver/4.34.0/selenium.webdriver.4.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/selenium.webdriver.chromedriver/138.0.7204.15700/selenium.webdriver.chromedriver.138.0.7204.15700.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp/2.88.7/skiasharp.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp.nativeassets.linux/2.88.7/skiasharp.nativeassets.linux.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp.nativeassets.macos/2.88.7/skiasharp.nativeassets.macos.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp.nativeassets.webassembly/2.88.7/skiasharp.nativeassets.webassembly.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/skiasharp.nativeassets.win32/2.88.7/skiasharp.nativeassets.win32.2.88.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/splat/14.4.1/splat.14.4.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.bundle_e_sqlite3/2.1.10/sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.core/2.1.10/sqlitepclraw.core.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3/2.1.10/sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.provider.e_sqlite3/2.1.10/sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.componentmodel.annotations/4.5.0/system.componentmodel.annotations.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.drawing.common/8.0.0/system.drawing.common.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/6.0.0/system.io.pipelines.6.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.3/system.memory.4.5.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.numerics.vectors/4.5.0/system.numerics.vectors.4.5.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reactive/5.0.0/system.reactive.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/9.0.7/system.text.json.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/tmds.dbus.protocol/0.15.0/tmds.dbus.protocol.0.15.0.nupkg.sha512"], "logs": []}