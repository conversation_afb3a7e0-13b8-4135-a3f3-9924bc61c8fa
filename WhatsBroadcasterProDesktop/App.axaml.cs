using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using WhatsBroadcasterProDesktop.Views;
using WhatsBroadcasterProDesktop.ViewModels;
using WhatsBroadcasterProDesktop.Services;

namespace WhatsBroadcasterProDesktop;

public partial class App : Application
{
    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
    }

    public override void OnFrameworkInitializationCompleted()
    {
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            // Initialize services
            var databaseService = new DatabaseService();
            var whatsAppService = new WhatsAppService();
            
            // Initialize database
            databaseService.InitializeAsync().Wait();
            
            // Create main window with services
            var mainViewModel = new MainWindowViewModel(databaseService, whatsAppService);
            desktop.MainWindow = new MainWindow
            {
                DataContext = mainViewModel
            };
        }

        base.OnFrameworkInitializationCompleted();
    }
}
