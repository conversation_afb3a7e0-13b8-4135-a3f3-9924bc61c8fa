<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="WhatsBroadcasterProDesktop.App"
             RequestedThemeVariant="Default">
    <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.Resources>
        <!-- WhatsApp Color Palette -->
        <SolidColorBrush x:Key="WhatsAppGreen" Color="#25D366"/>
        <SolidColorBrush x:Key="WhatsAppDarkGreen" Color="#128C7E"/>
        <SolidColorBrush x:Key="WhatsAppLightGreen" Color="#DCF8C6"/>

        <!-- Primary Colors -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#25D366"/>
        <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#128C7E"/>
        <SolidColorBrush x:Key="PrimaryLightBrush" Color="#E8F5E8"/>

        <!-- Secondary Colors -->
        <SolidColorBrush x:Key="SecondaryBrush" Color="#34B7F1"/>
        <SolidColorBrush x:Key="SecondaryDarkBrush" Color="#0288D1"/>
        <SolidColorBrush x:Key="SecondaryLightBrush" Color="#E3F2FD"/>

        <!-- Status Colors -->
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>

        <!-- Background Colors -->
        <SolidColorBrush x:Key="BackgroundBrush" Color="#F0F2F5"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>

        <!-- Text Colors -->
        <SolidColorBrush x:Key="TextPrimaryBrush" Color="#1F2937"/>
        <SolidColorBrush x:Key="TextSecondaryBrush" Color="#6B7280"/>
        <SolidColorBrush x:Key="TextHintBrush" Color="#9CA3AF"/>

        <!-- Border Colors -->
        <SolidColorBrush x:Key="BorderBrush" Color="#D1D5DB"/>
        <SolidColorBrush x:Key="DividerBrush" Color="#E5E7EB"/>
    </Application.Resources>

    <Application.Styles>
        <FluentTheme />
        <StyleInclude Source="avares://Avalonia.Controls.DataGrid/Themes/Fluent.xaml"/>

        <!-- Card Style -->
        <Style Selector="Border.card">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="BoxShadow" Value="0 2 8 0 #10000000"/>
        </Style>

        <!-- Button Styles -->
        <Style Selector="Button.primary">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="Button.secondary">
            <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="Button.success">
            <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="Button.warning">
            <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="Button.danger">
            <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <!-- Text Styles -->
        <Style Selector="TextBlock.header">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>

        <Style Selector="TextBlock.subheader">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
        </Style>

        <Style Selector="TextBlock.body">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="LineHeight" Value="1.5"/>
        </Style>

        <Style Selector="TextBlock.caption">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        </Style>

        <Style Selector="TextBlock.hint">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="{StaticResource TextHintBrush}"/>
            <Setter Property="FontStyle" Value="Italic"/>
        </Style>
    </Application.Styles>
</Application>
