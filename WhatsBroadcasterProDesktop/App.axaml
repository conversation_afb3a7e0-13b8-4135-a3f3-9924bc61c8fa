<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="WhatsBroadcasterProDesktop.App"
             RequestedThemeVariant="Default">
    <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.Styles>
        <FluentTheme />
        <StyleInclude Source="avares://Avalonia.Controls.DataGrid/Themes/Fluent.xaml"/>
    </Application.Styles>

    <Application.Resources>
        <!-- Define colors and styles -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#007ACC"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#28A745"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#DC3545"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FFC107"/>
        <SolidColorBrush x:Key="InfoBrush" Color="#17A2B8"/>
        
        <!-- Card Style -->
        <Style Selector="Border.card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Margin" Value="8"/>
        </Style>
        
        <!-- Button Styles -->
        <Style Selector="Button.primary">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <Style Selector="Button.success">
            <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <Style Selector="Button.danger">
            <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <!-- Header Style -->
        <Style Selector="TextBlock.header">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
        </Style>
        
        <!-- Subheader Style -->
        <Style Selector="TextBlock.subheader">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
    </Application.Resources>
</Application>
