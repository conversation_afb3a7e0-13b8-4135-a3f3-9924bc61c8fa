<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="WhatsBroadcasterProDesktop.App"
             RequestedThemeVariant="Default">
    <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.Resources>
        <!-- Arabic Font Resources -->
        <FontFamily x:Key="ArabicFont">Segoe UI, Tahoma, Arial Unicode MS, Microsoft Sans Serif</FontFamily>

        <!-- WhatsApp Color Palette -->
        <SolidColorBrush x:Key="WhatsAppGreen" Color="#25D366"/>
        <SolidColorBrush x:Key="WhatsAppDarkGreen" Color="#128C7E"/>
        <SolidColorBrush x:Key="WhatsAppLightGreen" Color="#DCF8C6"/>

        <!-- Primary Colors -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#25D366"/>
        <SolidColorBrush x:Key="PrimaryDarkBrush" Color="#128C7E"/>
        <SolidColorBrush x:Key="PrimaryLightBrush" Color="#E8F5E8"/>

        <!-- Secondary Colors -->
        <SolidColorBrush x:Key="SecondaryBrush" Color="#34B7F1"/>
        <SolidColorBrush x:Key="SecondaryDarkBrush" Color="#0288D1"/>
        <SolidColorBrush x:Key="SecondaryLightBrush" Color="#E3F2FD"/>

        <!-- Status Colors -->
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>

        <!-- Background Colors -->
        <SolidColorBrush x:Key="BackgroundBrush" Color="#F0F2F5"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>

        <!-- Text Colors -->
        <SolidColorBrush x:Key="TextPrimaryBrush" Color="#1F2937"/>
        <SolidColorBrush x:Key="TextSecondaryBrush" Color="#6B7280"/>
        <SolidColorBrush x:Key="TextHintBrush" Color="#9CA3AF"/>
        <SolidColorBrush x:Key="TextOnPrimaryBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="TextOnSecondaryBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="TextOnDarkBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="TextOnLightBrush" Color="#1F2937"/>

        <!-- Border Colors -->
        <SolidColorBrush x:Key="BorderBrush" Color="#D1D5DB"/>
        <SolidColorBrush x:Key="DividerBrush" Color="#E5E7EB"/>
    </Application.Resources>

    <Application.Styles>
        <FluentTheme />
        <StyleInclude Source="avares://Avalonia.Controls.DataGrid/Themes/Fluent.xaml"/>

        <!-- Card Style -->
        <Style Selector="Border.card">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="BoxShadow" Value="0 2 8 0 #10000000"/>
        </Style>

        <!-- Button Styles -->
        <Style Selector="Button.primary">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="Button.primary:pointerover">
            <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.primary:pressed">
            <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Opacity" Value="0.8"/>
        </Style>

        <Style Selector="Button.secondary">
            <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="Button.secondary:pointerover">
            <Setter Property="Background" Value="{StaticResource SecondaryDarkBrush}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.secondary:pressed">
            <Setter Property="Background" Value="{StaticResource SecondaryDarkBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Opacity" Value="0.8"/>
        </Style>

        <Style Selector="Button.success">
            <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="Button.success:pointerover">
            <Setter Property="Background" Value="#45A049"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.success:pressed">
            <Setter Property="Background" Value="#45A049"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Opacity" Value="0.8"/>
        </Style>

        <Style Selector="Button.warning">
            <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="Button.warning:pointerover">
            <Setter Property="Background" Value="#F57C00"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.warning:pressed">
            <Setter Property="Background" Value="#F57C00"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Opacity" Value="0.8"/>
        </Style>

        <Style Selector="Button.danger">
            <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="Button.danger:pointerover">
            <Setter Property="Background" Value="#D32F2F"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.danger:pressed">
            <Setter Property="Background" Value="#D32F2F"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Opacity" Value="0.8"/>
        </Style>

        <!-- Default Button Styles -->
        <Style Selector="Button">
            <Setter Property="Foreground" Value="Black"/>
        </Style>

        <Style Selector="Button:pointerover">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="Opacity" Value="0.8"/>
        </Style>

        <Style Selector="Button:pressed">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="Opacity" Value="0.6"/>
        </Style>

        <!-- Global Text Styles for Arabic Support -->
        <Style Selector="Window">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="FlowDirection" Value="RightToLeft"/>
        </Style>

        <Style Selector="TextBlock">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="TextAlignment" Value="Right"/>
        </Style>

        <Style Selector="TextBox">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
            <Setter Property="TextAlignment" Value="Right"/>
        </Style>

        <Style Selector="Button">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="Label">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="ComboBox">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="CheckBox">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="MenuItem">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="ToolTip">
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <!-- Text Styles -->
        <Style Selector="TextBlock.header">
            <Setter Property="FontSize" Value="28"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="TextBlock.subheader">
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="TextBlock.body">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
            <Setter Property="LineHeight" Value="1.5"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="TextBlock.caption">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="TextBlock.hint">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="{StaticResource TextHintBrush}"/>
            <Setter Property="FontStyle" Value="Italic"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <!-- DataGrid Styles -->
        <Style Selector="DataGrid">
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="RowBackground" Value="{StaticResource SurfaceBrush}"/>
        </Style>

        <Style Selector="DataGridColumnHeader">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="DataGridRow">
            <Setter Property="MinHeight" Value="45"/>
        </Style>

        <Style Selector="DataGridRow:selected">
            <Setter Property="Background" Value="{StaticResource PrimaryLightBrush}"/>
        </Style>

        <Style Selector="DataGridCell">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="DataGridCell TextBlock">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="DataGridTextColumn">
            <Setter Property="Foreground" Value="Black"/>
        </Style>

        <!-- Additional styles for table text -->
        <Style Selector="DataGrid TextBlock">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="DataGridRow TextBlock">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <!-- ItemsControl styles for message logs -->
        <Style Selector="ItemsControl TextBlock">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>

        <Style Selector="Border TextBlock">
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="FontFamily" Value="{StaticResource ArabicFont}"/>
        </Style>
    </Application.Styles>
</Application>
