using System.Collections.ObjectModel;
using System.Reactive;
using System.Reactive.Linq;
using ReactiveUI;
using WhatsBroadcasterProDesktop.Models;
using WhatsBroadcasterProDesktop.Services;
using WhatsBroadcasterProDesktop.Views;

namespace WhatsBroadcasterProDesktop.ViewModels;

public class MainWindowViewModel : ViewModelBase
{
    private readonly IDatabaseService _databaseService;
    private readonly IWhatsAppService _whatsAppService;

    private int _contactsCount;
    private int _groupsCount;
    private int _messagesCount;
    private string _connectionStatus = "غير متصل";
    private bool _isConnected;
    private string _statusMessage = "جاهز";
    private string _currentTime = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");

    public MainWindowViewModel(IDatabaseService databaseService, IWhatsAppService whatsAppService)
    {
        _databaseService = databaseService;
        _whatsAppService = whatsAppService;

        // Initialize commands
        ConnectWhatsAppCommand = ReactiveCommand.CreateFromTask(ConnectWhatsAppAsync);
        DisconnectWhatsAppCommand = ReactiveCommand.CreateFromTask(DisconnectWhatsAppAsync);
        OpenContactsCommand = ReactiveCommand.Create(OpenContacts);
        OpenGroupsCommand = ReactiveCommand.Create(OpenGroups);
        OpenBroadcastCommand = ReactiveCommand.Create(OpenBroadcast);
        OpenSelectiveMessageCommand = ReactiveCommand.Create(OpenSelectiveMessage);
        OpenMessageStatusCommand = ReactiveCommand.Create(OpenMessageStatus);
        RefreshDataCommand = ReactiveCommand.CreateFromTask(RefreshDataAsync);

        // Subscribe to WhatsApp events
        _whatsAppService.ConnectionStatusChanged += OnConnectionStatusChanged;

        // Initialize data
        _ = RefreshDataAsync();

        // Update time every second
        Observable.Timer(TimeSpan.Zero, TimeSpan.FromSeconds(1))
            .ObserveOn(RxApp.MainThreadScheduler)
            .Subscribe(_ => CurrentTime = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"));
    }

    public int ContactsCount
    {
        get => _contactsCount;
        set => this.RaiseAndSetIfChanged(ref _contactsCount, value);
    }

    public int GroupsCount
    {
        get => _groupsCount;
        set => this.RaiseAndSetIfChanged(ref _groupsCount, value);
    }

    public int MessagesCount
    {
        get => _messagesCount;
        set => this.RaiseAndSetIfChanged(ref _messagesCount, value);
    }

    public string ConnectionStatus
    {
        get => _connectionStatus;
        set => this.RaiseAndSetIfChanged(ref _connectionStatus, value);
    }

    public bool IsConnected
    {
        get => _isConnected;
        set => this.RaiseAndSetIfChanged(ref _isConnected, value);
    }

    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    public string CurrentTime
    {
        get => _currentTime;
        set => this.RaiseAndSetIfChanged(ref _currentTime, value);
    }

    public ObservableCollection<BroadcastMessage> RecentMessages { get; } = new();

    // Commands
    public ReactiveCommand<Unit, Unit> ConnectWhatsAppCommand { get; }
    public ReactiveCommand<Unit, Unit> DisconnectWhatsAppCommand { get; }
    public ReactiveCommand<Unit, Unit> OpenContactsCommand { get; }
    public ReactiveCommand<Unit, Unit> OpenGroupsCommand { get; }
    public ReactiveCommand<Unit, Unit> OpenBroadcastCommand { get; }
    public ReactiveCommand<Unit, Unit> OpenSelectiveMessageCommand { get; }
    public ReactiveCommand<Unit, Unit> OpenMessageStatusCommand { get; }
    public ReactiveCommand<Unit, Unit> RefreshDataCommand { get; }

    private async Task ConnectWhatsAppAsync()
    {
        try
        {
            StatusMessage = "جاري الاتصال بواتساب...";
            await _whatsAppService.ConnectAsync();
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في الاتصال: {ex.Message}";
        }
    }

    private async Task DisconnectWhatsAppAsync()
    {
        try
        {
            await _whatsAppService.DisconnectAsync();
            StatusMessage = "تم قطع الاتصال من واتساب";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في قطع الاتصال: {ex.Message}";
        }
    }

    private void OpenContacts()
    {
        try
        {
            StatusMessage = "جاري فتح نافذة إدارة الأرقام...";

            var contactsViewModel = new ContactsWindowViewModel(_databaseService);
            var contactsWindow = new ContactsWindow
            {
                DataContext = contactsViewModel
            };

            contactsWindow.Show();
            contactsWindow.Activate();

            StatusMessage = "تم فتح نافذة إدارة الأرقام بنجاح";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في فتح نافذة الأرقام: {ex.Message}";
            Console.WriteLine($"Error opening contacts window: {ex}");
        }
    }

    private void OpenGroups()
    {
        try
        {
            var groupsWindow = new GroupsWindow
            {
                DataContext = new GroupsWindowViewModel(_databaseService)
            };
            groupsWindow.Show();
            groupsWindow.Activate();
            StatusMessage = "تم فتح نافذة إدارة المجموعات";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في فتح نافذة المجموعات: {ex.Message}";
        }
    }

    private void OpenBroadcast()
    {
        try
        {
            var broadcastWindow = new BroadcastWindow
            {
                DataContext = new BroadcastWindowViewModel(_databaseService, _whatsAppService)
            };
            broadcastWindow.Show();
            broadcastWindow.Activate();
            StatusMessage = "تم فتح نافذة الإرسال الجماعي";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في فتح نافذة الإرسال: {ex.Message}";
        }
    }

    private void OpenSelectiveMessage()
    {
        try
        {
            var selectiveMessageWindow = new SelectiveMessageWindow
            {
                DataContext = new SelectiveMessageViewModel(_databaseService, _whatsAppService)
            };
            selectiveMessageWindow.Show();
            selectiveMessageWindow.Activate();
            StatusMessage = "تم فتح نافذة الإرسال الانتقائي";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في فتح نافذة الإرسال الانتقائي: {ex.Message}";
        }
    }

    private void OpenMessageStatus()
    {
        try
        {
            var messageStatusWindow = new MessageStatusWindow
            {
                DataContext = new MessageStatusViewModel(_databaseService)
            };
            messageStatusWindow.Show();
            messageStatusWindow.Activate();
            StatusMessage = "تم فتح تقرير حالة الرسائل";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في فتح تقرير الرسائل: {ex.Message}";
        }
    }

    private async Task RefreshDataAsync()
    {
        try
        {
            StatusMessage = "جاري تحديث البيانات...";

            // Load statistics
            var contacts = await _databaseService.GetContactsAsync();
            ContactsCount = contacts.Count;

            var groups = await _databaseService.GetGroupsAsync();
            GroupsCount = groups.Count;

            var messages = await _databaseService.GetBroadcastMessagesAsync();
            MessagesCount = messages.Count;

            // Load recent messages
            RecentMessages.Clear();
            foreach (var message in messages.Take(10))
            {
                RecentMessages.Add(message);
            }

            // Update connection status
            IsConnected = _whatsAppService.IsConnected;
            ConnectionStatus = IsConnected ? "متصل بواتساب" : "غير متصل";

            StatusMessage = "تم تحديث البيانات بنجاح";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تحديث البيانات: {ex.Message}";
        }
    }

    private void OnConnectionStatusChanged(object? sender, string status)
    {
        ConnectionStatus = status;
        IsConnected = _whatsAppService.IsConnected;
    }
}
