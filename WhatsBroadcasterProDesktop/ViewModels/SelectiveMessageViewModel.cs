using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using ReactiveUI;
using WhatsBroadcasterProDesktop.Models;
using WhatsBroadcasterProDesktop.Services;

namespace WhatsBroadcasterProDesktop.ViewModels;

public class SelectableContact : ReactiveObject
{
    private bool _isSelected;
    
    public Contact Contact { get; }
    
    public bool IsSelected
    {
        get => _isSelected;
        set => this.RaiseAndSetIfChanged(ref _isSelected, value);
    }
    
    public string Name => Contact.Name;
    public string PhoneNumber => Contact.PhoneNumber;
    public string? Email => Contact.Email;
    public string GroupNames => Contact.GroupNames;
    
    public SelectableContact(Contact contact)
    {
        Contact = contact;
    }
}

public class SelectiveMessageViewModel : ReactiveObject
{
    private readonly IDatabaseService _databaseService;
    private readonly IWhatsAppService _whatsAppService;
    
    private string _searchText = string.Empty;
    private Models.Group? _selectedGroupFilter;
    private string _messageContent = string.Empty;
    private int _delayBetweenMessages = 5;
    private bool _saveToHistory = true;
    private bool _isSending;
    private double _sendProgress;
    private string _sendProgressText = string.Empty;
    private string _statusMessage = "جاهز للإرسال";

    public SelectiveMessageViewModel(IDatabaseService databaseService, IWhatsAppService whatsAppService)
    {
        _databaseService = databaseService;
        _whatsAppService = whatsAppService;
        
        // Initialize commands
        SelectAllCommand = ReactiveCommand.Create(SelectAll);
        UnselectAllCommand = ReactiveCommand.Create(UnselectAll);
        SendMessagesCommand = ReactiveCommand.CreateFromTask(SendMessagesAsync, this.WhenAnyValue(x => x.CanSendMessages));
        
        // Load data
        LoadDataAsync();
        
        // Subscribe to property changes for filtering
        this.WhenAnyValue(x => x.SearchText, x => x.SelectedGroupFilter)
            .Subscribe(_ => FilterContacts());
        
        // Subscribe to selection changes
        SelectableContacts.CollectionChanged += (s, e) => this.RaisePropertyChanged(nameof(SelectedContactsCount));
    }

    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }

    public Models.Group? SelectedGroupFilter
    {
        get => _selectedGroupFilter;
        set => this.RaiseAndSetIfChanged(ref _selectedGroupFilter, value);
    }

    public string MessageContent
    {
        get => _messageContent;
        set => this.RaiseAndSetIfChanged(ref _messageContent, value);
    }

    public int DelayBetweenMessages
    {
        get => _delayBetweenMessages;
        set => this.RaiseAndSetIfChanged(ref _delayBetweenMessages, value);
    }

    public bool SaveToHistory
    {
        get => _saveToHistory;
        set => this.RaiseAndSetIfChanged(ref _saveToHistory, value);
    }

    public bool IsSending
    {
        get => _isSending;
        set => this.RaiseAndSetIfChanged(ref _isSending, value);
    }

    public double SendProgress
    {
        get => _sendProgress;
        set => this.RaiseAndSetIfChanged(ref _sendProgress, value);
    }

    public string SendProgressText
    {
        get => _sendProgressText;
        set => this.RaiseAndSetIfChanged(ref _sendProgressText, value);
    }

    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    public bool CanSendMessages => !string.IsNullOrWhiteSpace(MessageContent) && 
                                  SelectedContactsCount > 0 && 
                                  !IsSending;

    public int TotalContactsCount => AllContacts.Count;
    public int SelectedContactsCount => SelectableContacts.Count(c => c.IsSelected);

    public ObservableCollection<SelectableContact> AllContacts { get; } = new();
    public ObservableCollection<SelectableContact> SelectableContacts { get; } = new();
    public ObservableCollection<SelectableContact> FilteredContacts { get; } = new();
    public ObservableCollection<Models.Group> Groups { get; } = new();

    public ReactiveCommand<Unit, Unit> SelectAllCommand { get; }
    public ReactiveCommand<Unit, Unit> UnselectAllCommand { get; }
    public ReactiveCommand<Unit, Unit> SendMessagesCommand { get; }

    private async void LoadDataAsync()
    {
        try
        {
            StatusMessage = "جاري تحميل البيانات...";
            
            // Load contacts
            var contacts = await _databaseService.GetContactsAsync();
            AllContacts.Clear();
            SelectableContacts.Clear();
            
            foreach (var contact in contacts)
            {
                var selectableContact = new SelectableContact(contact);
                selectableContact.WhenAnyValue(x => x.IsSelected)
                    .Subscribe(_ => 
                    {
                        this.RaisePropertyChanged(nameof(SelectedContactsCount));
                        this.RaisePropertyChanged(nameof(CanSendMessages));
                    });
                
                AllContacts.Add(selectableContact);
                SelectableContacts.Add(selectableContact);
            }
            
            // Load groups
            var groups = await _databaseService.GetGroupsAsync();
            Groups.Clear();
            foreach (var group in groups)
            {
                Groups.Add(group);
            }
            
            FilterContacts();
            StatusMessage = $"تم تحميل {contacts.Count} رقم";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
        }
    }

    private void FilterContacts()
    {
        var filtered = SelectableContacts.AsEnumerable();

        // Filter by search text
        if (!string.IsNullOrWhiteSpace(SearchText))
        {
            var searchLower = SearchText.ToLower();
            filtered = filtered.Where(c => 
                c.Name.ToLower().Contains(searchLower) ||
                c.PhoneNumber.Contains(searchLower) ||
                (c.Email?.ToLower().Contains(searchLower) ?? false));
        }

        // Filter by group
        if (SelectedGroupFilter != null)
        {
            filtered = filtered.Where(c => c.GroupNames.Contains(SelectedGroupFilter.Name));
        }

        FilteredContacts.Clear();
        foreach (var contact in filtered)
        {
            FilteredContacts.Add(contact);
        }
        
        this.RaisePropertyChanged(nameof(TotalContactsCount));
    }

    private void SelectAll()
    {
        foreach (var contact in FilteredContacts)
        {
            contact.IsSelected = true;
        }
        StatusMessage = $"تم تحديد {FilteredContacts.Count} رقم";
    }

    private void UnselectAll()
    {
        foreach (var contact in FilteredContacts)
        {
            contact.IsSelected = false;
        }
        StatusMessage = "تم إلغاء تحديد جميع الأرقام";
    }

    private async Task SendMessagesAsync()
    {
        try
        {
            IsSending = true;
            var selectedContacts = SelectableContacts.Where(c => c.IsSelected).ToList();
            
            if (selectedContacts.Count == 0)
            {
                StatusMessage = "لم يتم تحديد أي أرقام للإرسال";
                return;
            }

            StatusMessage = $"جاري إرسال الرسائل إلى {selectedContacts.Count} رقم...";
            
            for (int i = 0; i < selectedContacts.Count; i++)
            {
                var contact = selectedContacts[i];
                
                try
                {
                    SendProgressText = $"إرسال إلى {contact.Name} ({i + 1}/{selectedContacts.Count})";
                    SendProgress = ((double)(i + 1) / selectedContacts.Count) * 100;
                    
                    // Send message via WhatsApp service
                    await _whatsAppService.SendMessageAsync(contact.PhoneNumber, MessageContent);
                    
                    // Save to history if enabled
                    if (SaveToHistory)
                    {
                        var broadcastMessage = new BroadcastMessage
                        {
                            Content = MessageContent,
                            Type = MessageType.Text,
                            Status = BroadcastStatus.Completed,
                            CreatedAt = DateTime.Now,
                            TotalRecipients = selectedContacts.Count,
                            SuccessfulSends = selectedContacts.Count
                        };
                        
                        await _databaseService.SaveBroadcastMessageAsync(broadcastMessage);
                    }
                    
                    // Wait for delay
                    if (i < selectedContacts.Count - 1) // Don't delay after the last message
                    {
                        await Task.Delay(DelayBetweenMessages * 1000);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error sending to {contact.Name}: {ex.Message}");
                }
            }
            
            StatusMessage = $"تم إرسال الرسائل بنجاح إلى {selectedContacts.Count} رقم";
            SendProgressText = "تم الانتهاء من الإرسال";
            SendProgress = 100;
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في الإرسال: {ex.Message}";
        }
        finally
        {
            IsSending = false;
        }
    }
}
