using System.Collections.ObjectModel;
using System.Reactive;
using ReactiveUI;
using WhatsBroadcasterProDesktop.Models;
using WhatsBroadcasterProDesktop.Services;

namespace WhatsBroadcasterProDesktop.ViewModels;

public class BroadcastWindowViewModel : ViewModelBase
{
    private readonly IDatabaseService _databaseService;
    private readonly IWhatsAppService _whatsAppService;
    
    private Group? _selectedGroup;
    private string _messageContent = string.Empty;
    private int _delayBetweenMessages = 5;
    private bool _saveToHistory = true;
    private bool _isSending = false;
    private double _sendProgress = 0;
    private string _sendProgressText = string.Empty;
    private string _statusMessage = "جاهز";
    private int _selectedGroupContactsCount = 0;

    public BroadcastWindowViewModel(IDatabaseService databaseService, IWhatsAppService whatsAppService)
    {
        _databaseService = databaseService;
        _whatsAppService = whatsAppService;

        // Initialize commands
        SendBroadcastCommand = ReactiveCommand.CreateFromTask(SendBroadcastAsync, this.WhenAnyValue(x => x.CanSend));

        // Initialize data
        _ = LoadDataAsync();
    }

    public Group? SelectedGroup
    {
        get => _selectedGroup;
        set
        {
            this.RaiseAndSetIfChanged(ref _selectedGroup, value);
            _ = UpdateGroupContactsCountAsync();
            this.RaisePropertyChanged(nameof(CanSend));
            this.RaisePropertyChanged(nameof(EstimatedTime));
        }
    }

    public string MessageContent
    {
        get => _messageContent;
        set
        {
            this.RaiseAndSetIfChanged(ref _messageContent, value);
            this.RaisePropertyChanged(nameof(CanSend));
        }
    }

    public int DelayBetweenMessages
    {
        get => _delayBetweenMessages;
        set
        {
            this.RaiseAndSetIfChanged(ref _delayBetweenMessages, value);
            this.RaisePropertyChanged(nameof(EstimatedTime));
        }
    }

    public bool SaveToHistory
    {
        get => _saveToHistory;
        set => this.RaiseAndSetIfChanged(ref _saveToHistory, value);
    }

    public bool IsSending
    {
        get => _isSending;
        set => this.RaiseAndSetIfChanged(ref _isSending, value);
    }

    public double SendProgress
    {
        get => _sendProgress;
        set => this.RaiseAndSetIfChanged(ref _sendProgress, value);
    }

    public string SendProgressText
    {
        get => _sendProgressText;
        set => this.RaiseAndSetIfChanged(ref _sendProgressText, value);
    }

    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    public int SelectedGroupContactsCount
    {
        get => _selectedGroupContactsCount;
        set => this.RaiseAndSetIfChanged(ref _selectedGroupContactsCount, value);
    }

    public bool CanSend => 
        SelectedGroup != null && 
        !string.IsNullOrWhiteSpace(MessageContent) && 
        SelectedGroupContactsCount > 0 && 
        !IsSending &&
        _whatsAppService.IsConnected;

    public string EstimatedTime
    {
        get
        {
            if (SelectedGroupContactsCount == 0) return "غير محدد";
            var totalSeconds = SelectedGroupContactsCount * DelayBetweenMessages;
            var minutes = totalSeconds / 60;
            var seconds = totalSeconds % 60;
            return $"{minutes:00}:{seconds:00}";
        }
    }

    public ObservableCollection<Group> Groups { get; } = new();

    // Commands
    public ReactiveCommand<Unit, Unit> SendBroadcastCommand { get; }

    private async Task LoadDataAsync()
    {
        try
        {
            StatusMessage = "جاري تحميل المجموعات...";

            var groups = await _databaseService.GetGroupsAsync();
            Groups.Clear();
            foreach (var group in groups.Where(g => g.ContactGroups.Any()))
            {
                Groups.Add(group);
            }

            StatusMessage = $"تم تحميل {groups.Count} مجموعة";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
        }
    }

    private async Task UpdateGroupContactsCountAsync()
    {
        if (SelectedGroup == null)
        {
            SelectedGroupContactsCount = 0;
            return;
        }

        try
        {
            var contacts = await _databaseService.GetContactsByGroupAsync(SelectedGroup.Id);
            SelectedGroupContactsCount = contacts.Count;
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تحميل أرقام المجموعة: {ex.Message}";
            SelectedGroupContactsCount = 0;
        }
    }

    private async Task SendBroadcastAsync()
    {
        if (!CanSend) return;

        try
        {
            IsSending = true;
            SendProgress = 0;
            StatusMessage = "جاري إرسال الرسائل...";

            // Get contacts from selected group
            var contacts = await _databaseService.GetContactsByGroupAsync(SelectedGroup!.Id);
            
            if (contacts.Count == 0)
            {
                StatusMessage = "لا توجد أرقام في المجموعة المحددة";
                return;
            }

            // Create broadcast message record
            var broadcastMessage = new BroadcastMessage
            {
                Content = MessageContent,
                GroupId = SelectedGroup.Id,
                Type = MessageType.Text,
                Status = BroadcastStatus.InProgress,
                CreatedAt = DateTime.Now
            };

            if (SaveToHistory)
            {
                broadcastMessage = await _databaseService.SaveBroadcastMessageAsync(broadcastMessage);
            }

            // Send messages
            var results = await _whatsAppService.SendBroadcastMessageAsync(contacts, MessageContent);
            
            // Update progress
            var successCount = results.Count(r => r);
            var failCount = results.Count(r => !r);

            // Update broadcast message status
            if (SaveToHistory)
            {
                broadcastMessage.Status = BroadcastStatus.Completed;
                broadcastMessage.SuccessfulSends = successCount;
                broadcastMessage.FailedSends = failCount;
                await _databaseService.SaveBroadcastMessageAsync(broadcastMessage);
            }

            SendProgress = 100;
            SendProgressText = $"تم الإرسال: {successCount} نجح، {failCount} فشل";
            StatusMessage = $"تم إنهاء الإرسال - نجح: {successCount}، فشل: {failCount}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في الإرسال: {ex.Message}";
        }
        finally
        {
            IsSending = false;
        }
    }
}
