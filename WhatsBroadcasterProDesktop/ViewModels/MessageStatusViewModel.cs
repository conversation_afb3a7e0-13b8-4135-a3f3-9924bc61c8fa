using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using ReactiveUI;
using WhatsBroadcasterProDesktop.Models;
using WhatsBroadcasterProDesktop.Services;

namespace WhatsBroadcasterProDesktop.ViewModels;

public class MessageLogViewModel : ReactiveObject
{
    public MessageLog MessageLog { get; }
    
    public MessageLogViewModel(MessageLog messageLog)
    {
        MessageLog = messageLog;
    }
    
    public Contact Contact => MessageLog.Contact;
    public BroadcastMessage BroadcastMessage => MessageLog.BroadcastMessage;
    public DateTime SentAt => MessageLog.SentAt;
    public int RetryCount => MessageLog.RetryCount;
    
    public string StatusText => MessageLog.Status switch
    {
        DeliveryStatus.Pending => "في الانتظار",
        DeliveryStatus.Sent => "تم الإرسال",
        DeliveryStatus.Delivered => "تم التسليم",
        DeliveryStatus.Failed => "فشل",
        DeliveryStatus.Read => "تم القراءة",
        _ => "غير معروف"
    };
    
    public string StatusColor => MessageLog.Status switch
    {
        DeliveryStatus.Pending => "#FFC107",
        DeliveryStatus.Sent => "#28A745",
        DeliveryStatus.Delivered => "#007ACC",
        DeliveryStatus.Failed => "#DC3545",
        DeliveryStatus.Read => "#6F42C1",
        _ => "#6C757D"
    };
}

public class MessageStatusViewModel : ReactiveObject
{
    private readonly IDatabaseService _databaseService;
    
    private BroadcastMessage? _selectedBroadcast;
    private string? _selectedStatusFilter;
    private string _statusMessage = "جاري تحميل البيانات...";
    private DateTime _lastUpdated = DateTime.Now;

    public MessageStatusViewModel(IDatabaseService databaseService)
    {
        _databaseService = databaseService;
        
        // Initialize commands
        RefreshCommand = ReactiveCommand.CreateFromTask(RefreshDataAsync);
        
        // Initialize status filters
        StatusFilters.Add("جميع الحالات");
        StatusFilters.Add("في الانتظار");
        StatusFilters.Add("تم الإرسال");
        StatusFilters.Add("تم التسليم");
        StatusFilters.Add("فشل الإرسال");
        StatusFilters.Add("تم القراءة");
        
        // Subscribe to filter changes
        this.WhenAnyValue(x => x.SelectedBroadcast, x => x.SelectedStatusFilter)
            .Subscribe(_ => FilterMessages());
        
        // Load initial data
        LoadDataAsync();
    }

    public BroadcastMessage? SelectedBroadcast
    {
        get => _selectedBroadcast;
        set => this.RaiseAndSetIfChanged(ref _selectedBroadcast, value);
    }

    public string? SelectedStatusFilter
    {
        get => _selectedStatusFilter;
        set => this.RaiseAndSetIfChanged(ref _selectedStatusFilter, value);
    }

    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    public DateTime LastUpdated
    {
        get => _lastUpdated;
        set => this.RaiseAndSetIfChanged(ref _lastUpdated, value);
    }

    // Statistics Properties
    public int TotalMessages => AllMessageLogs.Count;
    public int SentMessages => AllMessageLogs.Count(m => m.MessageLog.Status == DeliveryStatus.Sent);
    public int DeliveredMessages => AllMessageLogs.Count(m => m.MessageLog.Status == DeliveryStatus.Delivered);
    public int ReadMessages => AllMessageLogs.Count(m => m.MessageLog.Status == DeliveryStatus.Read);
    public int FailedMessages => AllMessageLogs.Count(m => m.MessageLog.Status == DeliveryStatus.Failed);
    
    public double DeliveryRate => TotalMessages > 0 ? 
        (double)(SentMessages + DeliveredMessages + ReadMessages) / TotalMessages : 0;

    public ObservableCollection<BroadcastMessage> BroadcastMessages { get; } = new();
    public ObservableCollection<string> StatusFilters { get; } = new();
    public ObservableCollection<MessageLogViewModel> AllMessageLogs { get; } = new();
    public ObservableCollection<MessageLogViewModel> FilteredMessageLogs { get; } = new();

    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }

    private async void LoadDataAsync()
    {
        try
        {
            StatusMessage = "جاري تحميل البيانات...";
            
            // Load broadcast messages
            var broadcasts = await _databaseService.GetBroadcastMessagesAsync();
            BroadcastMessages.Clear();
            foreach (var broadcast in broadcasts.OrderByDescending(b => b.CreatedAt))
            {
                BroadcastMessages.Add(broadcast);
            }
            
            // Load message logs
            var messageLogs = await _databaseService.GetMessageLogsAsync();
            AllMessageLogs.Clear();
            
            foreach (var log in messageLogs.OrderByDescending(l => l.SentAt))
            {
                AllMessageLogs.Add(new MessageLogViewModel(log));
            }
            
            FilterMessages();
            UpdateStatistics();
            
            StatusMessage = $"تم تحميل {TotalMessages} رسالة";
            LastUpdated = DateTime.Now;
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
        }
    }

    private void FilterMessages()
    {
        var filtered = AllMessageLogs.AsEnumerable();

        // Filter by broadcast message
        if (SelectedBroadcast != null)
        {
            filtered = filtered.Where(m => m.BroadcastMessage.Id == SelectedBroadcast.Id);
        }

        // Filter by status
        if (!string.IsNullOrEmpty(SelectedStatusFilter) && SelectedStatusFilter != "جميع الحالات")
        {
            var status = SelectedStatusFilter switch
            {
                "في الانتظار" => DeliveryStatus.Pending,
                "تم الإرسال" => DeliveryStatus.Sent,
                "تم التسليم" => DeliveryStatus.Delivered,
                "فشل الإرسال" => DeliveryStatus.Failed,
                "تم القراءة" => DeliveryStatus.Read,
                _ => (DeliveryStatus?)null
            };
            
            if (status.HasValue)
            {
                filtered = filtered.Where(m => m.MessageLog.Status == status.Value);
            }
        }

        FilteredMessageLogs.Clear();
        foreach (var message in filtered)
        {
            FilteredMessageLogs.Add(message);
        }
        
        UpdateStatistics();
    }

    private void UpdateStatistics()
    {
        this.RaisePropertyChanged(nameof(TotalMessages));
        this.RaisePropertyChanged(nameof(SentMessages));
        this.RaisePropertyChanged(nameof(DeliveredMessages));
        this.RaisePropertyChanged(nameof(ReadMessages));
        this.RaisePropertyChanged(nameof(FailedMessages));
        this.RaisePropertyChanged(nameof(DeliveryRate));
    }

    private async Task RefreshDataAsync()
    {
        LoadDataAsync();
        await Task.Delay(100); // Small delay for UI feedback
    }
}
