using System;
using System.Reactive;
using System.Threading.Tasks;
using ReactiveUI;
using Avalonia.Media.Imaging;
using WhatsBroadcasterProDesktop.Services;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;

namespace WhatsBroadcasterProDesktop.ViewModels
{
    public class QRCodeWindowViewModel : ViewModelBase
    {
        private readonly IWhatsAppService _whatsAppService;
        private Avalonia.Media.Imaging.Bitmap? _qrCodeImage;
        private bool _isLoading = true;
        private bool _hasError = false;
        private bool _isQRCodeVisible = false;
        private bool _isConnected = false;
        private string _statusMessage = "جاري التحضير...";
        private string _errorMessage = "";

        public QRCodeWindowViewModel(IWhatsAppService whatsAppService)
        {
            _whatsAppService = whatsAppService;

            // Initialize commands
            RefreshQRCommand = ReactiveCommand.CreateFromTask(RefreshQRCodeAsync);
            OpenInBrowserCommand = ReactiveCommand.Create(OpenInBrowser);
            CancelCommand = ReactiveCommand.Create(Cancel);

            // Subscribe to connection status changes
            _whatsAppService.ConnectionStatusChanged += OnConnectionStatusChanged;

            // Start QR code capture
            _ = Task.Run(StartQRCodeCaptureAsync);
        }

        // Properties
        public Avalonia.Media.Imaging.Bitmap? QRCodeImage
        {
            get => _qrCodeImage;
            set => this.RaiseAndSetIfChanged(ref _qrCodeImage, value);
        }

        public bool IsLoading
        {
            get => _isLoading;
            set => this.RaiseAndSetIfChanged(ref _isLoading, value);
        }

        public bool HasError
        {
            get => _hasError;
            set => this.RaiseAndSetIfChanged(ref _hasError, value);
        }

        public bool IsQRCodeVisible
        {
            get => _isQRCodeVisible;
            set => this.RaiseAndSetIfChanged(ref _isQRCodeVisible, value);
        }

        public bool IsConnected
        {
            get => _isConnected;
            set => this.RaiseAndSetIfChanged(ref _isConnected, value);
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set => this.RaiseAndSetIfChanged(ref _errorMessage, value);
        }

        // Commands
        public ReactiveCommand<Unit, Unit> RefreshQRCommand { get; }
        public ReactiveCommand<Unit, Unit> OpenInBrowserCommand { get; }
        public ReactiveCommand<Unit, Unit> CancelCommand { get; }

        // Events
        public event EventHandler? ConnectionSuccessful;
        public event EventHandler? WindowCloseRequested;

        private async Task StartQRCodeCaptureAsync()
        {
            try
            {
                IsLoading = true;
                HasError = false;
                IsQRCodeVisible = false;
                IsConnected = false;
                StatusMessage = "جاري تشغيل واتساب ويب...";

                // Connect to WhatsApp Web
                await _whatsAppService.ConnectAsync();

                // Start monitoring for QR code and connection
                await MonitorConnectionAsync();
            }
            catch (Exception ex)
            {
                IsLoading = false;
                HasError = true;
                ErrorMessage = $"خطأ في الاتصال: {ex.Message}";
                StatusMessage = "فشل في الاتصال";
            }
        }

        private async Task MonitorConnectionAsync()
        {
            var timeout = TimeSpan.FromMinutes(5);
            var startTime = DateTime.Now;

            while (DateTime.Now - startTime < timeout && !IsConnected)
            {
                try
                {
                    // Check if connected
                    if (_whatsAppService.IsConnected)
                    {
                        IsLoading = false;
                        IsQRCodeVisible = false;
                        IsConnected = true;
                        StatusMessage = "متصل بنجاح!";
                        ConnectionSuccessful?.Invoke(this, EventArgs.Empty);
                        return;
                    }

                    // Try to capture QR code
                    await CaptureQRCodeAsync();

                    await Task.Delay(3000); // Check every 3 seconds
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error monitoring connection: {ex.Message}");
                    await Task.Delay(2000);
                }
            }

            if (!IsConnected)
            {
                IsLoading = false;
                HasError = true;
                ErrorMessage = "انتهت مهلة انتظار الاتصال. يرجى المحاولة مرة أخرى.";
                StatusMessage = "انتهت المهلة";
            }
        }

        private async Task CaptureQRCodeAsync()
        {
            try
            {
                // Get the current status to check if QR is available
                var status = await _whatsAppService.GetCurrentStatusAsync();
                
                if (status.Contains("QR") || status.Contains("رمز"))
                {
                    StatusMessage = "جاري التقاط رمز QR...";
                    
                    // Try to capture QR code screenshot
                    var qrImage = await CaptureQRCodeScreenshotAsync();
                    if (qrImage != null)
                    {
                        QRCodeImage = qrImage;
                        IsLoading = false;
                        IsQRCodeVisible = true;
                        HasError = false;
                        StatusMessage = "امسح رمز QR بهاتفك";
                    }
                    else
                    {
                        StatusMessage = "جاري البحث عن رمز QR...";
                    }
                }
                else if (status.Contains("متصل"))
                {
                    IsConnected = true;
                }
                else
                {
                    StatusMessage = status;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error capturing QR code: {ex.Message}");
                StatusMessage = "جاري المحاولة...";
            }
        }

        private async Task<Avalonia.Media.Imaging.Bitmap?> CaptureQRCodeScreenshotAsync()
        {
            try
            {
                // Capture QR code using WhatsApp service
                var qrBytes = await _whatsAppService.CaptureQRCodeAsync();

                if (qrBytes != null && qrBytes.Length > 0)
                {
                    // Convert byte array to Bitmap
                    using var stream = new MemoryStream(qrBytes);
                    var bitmap = new System.Drawing.Bitmap(stream);

                    // Convert System.Drawing.Bitmap to Avalonia.Media.Imaging.Bitmap
                    using var memoryStream = new MemoryStream();
                    bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png);
                    memoryStream.Position = 0;

                    return new Avalonia.Media.Imaging.Bitmap(memoryStream);
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error taking QR screenshot: {ex.Message}");
                return null;
            }
        }

        private async Task RefreshQRCodeAsync()
        {
            try
            {
                IsLoading = true;
                HasError = false;
                IsQRCodeVisible = false;
                StatusMessage = "جاري تحديث رمز QR...";

                // Refresh the WhatsApp Web page
                await _whatsAppService.DisconnectAsync();
                await Task.Delay(2000);
                await StartQRCodeCaptureAsync();
            }
            catch (Exception ex)
            {
                IsLoading = false;
                HasError = true;
                ErrorMessage = $"خطأ في التحديث: {ex.Message}";
                StatusMessage = "فشل في التحديث";
            }
        }

        private void OpenInBrowser()
        {
            try
            {
                // Open WhatsApp Web in default browser
                var url = "https://web.whatsapp.com";
                
                if (OperatingSystem.IsWindows())
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = url,
                        UseShellExecute = true
                    });
                }
                else if (OperatingSystem.IsMacOS())
                {
                    System.Diagnostics.Process.Start("open", url);
                }
                else if (OperatingSystem.IsLinux())
                {
                    System.Diagnostics.Process.Start("xdg-open", url);
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"خطأ في فتح المتصفح: {ex.Message}";
                HasError = true;
            }
        }

        private void Cancel()
        {
            WindowCloseRequested?.Invoke(this, EventArgs.Empty);
        }

        private void OnConnectionStatusChanged(object? sender, string status)
        {
            StatusMessage = status;
            
            if (status.Contains("متصل بنجاح") || status.Contains("جاهز للإرسال"))
            {
                IsLoading = false;
                IsQRCodeVisible = false;
                IsConnected = true;
                ConnectionSuccessful?.Invoke(this, EventArgs.Empty);
            }
            else if (status.Contains("QR") || status.Contains("رمز"))
            {
                _ = Task.Run(CaptureQRCodeAsync);
            }
        }

        public void Cleanup()
        {
            _whatsAppService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            QRCodeImage?.Dispose();
        }
    }
}
