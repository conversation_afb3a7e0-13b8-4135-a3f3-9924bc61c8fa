using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using ReactiveUI;
using WhatsBroadcasterProDesktop.Models;
using WhatsBroadcasterProDesktop.Services;

namespace WhatsBroadcasterProDesktop.ViewModels;

public class AddContactViewModel : ReactiveObject
{
    private readonly IDatabaseService _databaseService;
    private string _name = string.Empty;
    private string _phoneNumber = string.Empty;
    private string _email = string.Empty;
    private string _notes = string.Empty;
    private Models.Group? _selectedGroup;
    private string _validationMessage = string.Empty;
    private bool _hasValidationError;

    public AddContactViewModel(IDatabaseService databaseService)
    {
        _databaseService = databaseService;
        
        // Initialize commands
        SaveCommand = ReactiveCommand.CreateFromTask(SaveAsync, this.WhenAnyValue(x => x.CanSave));
        CancelCommand = ReactiveCommand.Create(Cancel);

        // Load available groups
        LoadGroupsAsync();
        
        // Validate on property changes
        this.WhenAnyValue(x => x.Name, x => x.PhoneNumber)
            .Subscribe(_ => ValidateInput());
    }

    public string Name
    {
        get => _name;
        set => this.RaiseAndSetIfChanged(ref _name, value);
    }

    public string PhoneNumber
    {
        get => _phoneNumber;
        set => this.RaiseAndSetIfChanged(ref _phoneNumber, value);
    }

    public string Email
    {
        get => _email;
        set => this.RaiseAndSetIfChanged(ref _email, value);
    }

    public string Notes
    {
        get => _notes;
        set => this.RaiseAndSetIfChanged(ref _notes, value);
    }

    public Models.Group? SelectedGroup
    {
        get => _selectedGroup;
        set => this.RaiseAndSetIfChanged(ref _selectedGroup, value);
    }

    public string ValidationMessage
    {
        get => _validationMessage;
        set => this.RaiseAndSetIfChanged(ref _validationMessage, value);
    }

    public bool HasValidationError
    {
        get => _hasValidationError;
        set => this.RaiseAndSetIfChanged(ref _hasValidationError, value);
    }

    public bool CanSave => !string.IsNullOrWhiteSpace(Name) && 
                          !string.IsNullOrWhiteSpace(PhoneNumber) && 
                          !HasValidationError;

    public ObservableCollection<Models.Group> AvailableGroups { get; } = new();

    public ReactiveCommand<Unit, Unit> SaveCommand { get; }
    public ReactiveCommand<Unit, Unit> CancelCommand { get; }

    public event EventHandler<Contact>? ContactAdded;
    public event EventHandler? Cancelled;

    private async void LoadGroupsAsync()
    {
        try
        {
            var groups = await _databaseService.GetGroupsAsync();
            AvailableGroups.Clear();
            foreach (var group in groups)
            {
                AvailableGroups.Add(group);
            }
        }
        catch (Exception ex)
        {
            ValidationMessage = $"خطأ في تحميل المجموعات: {ex.Message}";
            HasValidationError = true;
        }
    }

    private void ValidateInput()
    {
        HasValidationError = false;
        ValidationMessage = string.Empty;

        // Validate name
        if (string.IsNullOrWhiteSpace(Name))
        {
            return; // Don't show error until user starts typing
        }

        // Validate phone number
        if (!string.IsNullOrWhiteSpace(PhoneNumber))
        {
            if (!IsValidPhoneNumber(PhoneNumber))
            {
                ValidationMessage = "رقم الهاتف غير صحيح. يجب أن يبدأ برمز الدولة (مثل +966) ويحتوي على أرقام فقط";
                HasValidationError = true;
                return;
            }
        }

        // Validate email if provided
        if (!string.IsNullOrWhiteSpace(Email) && !IsValidEmail(Email))
        {
            ValidationMessage = "البريد الإلكتروني غير صحيح";
            HasValidationError = true;
            return;
        }

        this.RaisePropertyChanged(nameof(CanSave));
    }

    private bool IsValidPhoneNumber(string phoneNumber)
    {
        // Remove spaces and dashes
        var cleanNumber = phoneNumber.Replace(" ", "").Replace("-", "");
        
        // Check if it starts with + and contains only digits after that
        var phoneRegex = new Regex(@"^\+[1-9]\d{1,14}$");
        return phoneRegex.IsMatch(cleanNumber);
    }

    private bool IsValidEmail(string email)
    {
        var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
        return emailRegex.IsMatch(email);
    }

    private async Task SaveAsync()
    {
        try
        {
            var contact = new Contact
            {
                Name = Name.Trim(),
                PhoneNumber = PhoneNumber.Trim(),
                Email = string.IsNullOrWhiteSpace(Email) ? null : Email.Trim(),
                Notes = string.IsNullOrWhiteSpace(Notes) ? null : Notes.Trim(),
                CreatedAt = DateTime.Now
            };

            var savedContact = await _databaseService.AddContactAsync(contact);

            // Add to group if selected
            if (SelectedGroup != null)
            {
                await _databaseService.AddContactToGroupAsync(savedContact.Id, SelectedGroup.Id);
            }

            ContactAdded?.Invoke(this, savedContact);
        }
        catch (Exception ex)
        {
            ValidationMessage = $"خطأ في حفظ الرقم: {ex.Message}";
            HasValidationError = true;
        }
    }

    private void Cancel()
    {
        Cancelled?.Invoke(this, EventArgs.Empty);
    }
}
