using System;
using System.Collections.ObjectModel;
using System.Reactive;
using System.Text;
using System.IO;
using ReactiveUI;
using WhatsBroadcasterProDesktop.Models;
using WhatsBroadcasterProDesktop.Services;
using WhatsBroadcasterProDesktop.Views;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Platform.Storage;

namespace WhatsBroadcasterProDesktop.ViewModels;

public class ContactsWindowViewModel : ViewModelBase
{
    private readonly IDatabaseService _databaseService;
    
    private string _searchText = string.Empty;
    private Group? _selectedGroup;
    private Contact? _selectedContact;
    private string _statusMessage = "جاهز";
    private int _contactsCount;

    public ContactsWindowViewModel(IDatabaseService databaseService)
    {
        _databaseService = databaseService;

        // Initialize commands
        AddContactCommand = ReactiveCommand.Create(AddContact);
        EditContactCommand = ReactiveCommand.Create<Contact>(EditContact);
        DeleteContactCommand = ReactiveCommand.CreateFromTask<Contact>(DeleteContactAsync);
        SearchCommand = ReactiveCommand.CreateFromTask(SearchAsync);
        RefreshCommand = ReactiveCommand.CreateFromTask(LoadDataAsync);
        ImportContactsCommand = ReactiveCommand.Create(ImportContacts);
        ExportContactsCommand = ReactiveCommand.Create(ExportContacts);
        ExportToExcelCommand = ReactiveCommand.CreateFromTask(ExportToExcelAsync);
        ImportFromExcelCommand = ReactiveCommand.CreateFromTask(ImportFromExcelAsync);
        ExportToCsvCommand = ReactiveCommand.CreateFromTask(ExportToCsvAsync);
        ImportFromCsvCommand = ReactiveCommand.CreateFromTask(ImportFromCsvAsync);

        // Initialize data
        StatusMessage = "جاري تحميل البيانات...";
        _ = LoadDataAsync();
    }

    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }

    public Group? SelectedGroup
    {
        get => _selectedGroup;
        set => this.RaiseAndSetIfChanged(ref _selectedGroup, value);
    }

    public Contact? SelectedContact
    {
        get => _selectedContact;
        set => this.RaiseAndSetIfChanged(ref _selectedContact, value);
    }

    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    public int ContactsCount
    {
        get => _contactsCount;
        set => this.RaiseAndSetIfChanged(ref _contactsCount, value);
    }

    public bool HasContacts => FilteredContacts.Count > 0;

    public ObservableCollection<Contact> Contacts { get; } = new();
    public ObservableCollection<Contact> FilteredContacts { get; } = new();
    public ObservableCollection<Group> Groups { get; } = new();

    // Commands
    public ReactiveCommand<Unit, Unit> AddContactCommand { get; }
    public ReactiveCommand<Contact, Unit> EditContactCommand { get; }
    public ReactiveCommand<Contact, Unit> DeleteContactCommand { get; }
    public ReactiveCommand<Unit, Unit> SearchCommand { get; }
    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
    public ReactiveCommand<Unit, Unit> ImportContactsCommand { get; }
    public ReactiveCommand<Unit, Unit> ExportContactsCommand { get; }
    public ReactiveCommand<Unit, Unit> ExportToExcelCommand { get; }
    public ReactiveCommand<Unit, Unit> ImportFromExcelCommand { get; }
    public ReactiveCommand<Unit, Unit> ExportToCsvCommand { get; }
    public ReactiveCommand<Unit, Unit> ImportFromCsvCommand { get; }

    private async Task LoadDataAsync()
    {
        try
        {
            StatusMessage = "جاري تحميل البيانات...";

            // Load contacts
            var contacts = await _databaseService.GetContactsAsync();
            Contacts.Clear();
            foreach (var contact in contacts)
            {
                Contacts.Add(contact);
            }

            // Load groups
            var groups = await _databaseService.GetGroupsAsync();
            Groups.Clear();
            Groups.Add(new Group { Id = 0, Name = "جميع المجموعات" });
            foreach (var group in groups)
            {
                Groups.Add(group);
            }

            ContactsCount = contacts.Count;
            await ApplyFiltersAsync();
            StatusMessage = $"تم تحميل {contacts.Count} رقم";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
        }
    }

    private async Task SearchAsync()
    {
        await ApplyFiltersAsync();
    }

    private async Task ApplyFiltersAsync()
    {
        try
        {
            var filtered = Contacts.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filtered = filtered.Where(c =>
                    c.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    c.PhoneNumber.Contains(SearchText) ||
                    (!string.IsNullOrEmpty(c.Email) && c.Email.Contains(SearchText, StringComparison.OrdinalIgnoreCase)));
            }

            // Apply group filter
            if (SelectedGroup != null && SelectedGroup.Id > 0)
            {
                filtered = filtered.Where(c =>
                    c.ContactGroups.Any(cg => cg.GroupId == SelectedGroup.Id));
            }

            FilteredContacts.Clear();
            foreach (var contact in filtered)
            {
                FilteredContacts.Add(contact);
            }

            // Notify HasContacts property changed
            this.RaisePropertyChanged(nameof(HasContacts));

            StatusMessage = $"عرض {FilteredContacts.Count} من {Contacts.Count} رقم";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في التصفية: {ex.Message}";
        }
    }

    private void AddContact()
    {
        try
        {
            var addContactViewModel = new AddContactViewModel(_databaseService);
            var addContactWindow = new AddContactWindow
            {
                DataContext = addContactViewModel
            };

            // Handle events
            addContactViewModel.ContactAdded += async (sender, contact) =>
            {
                // Add to collections
                Contacts.Add(contact);
                await ApplyFiltersAsync();
                StatusMessage = $"تم إضافة الرقم '{contact.Name}' بنجاح";
                addContactWindow.Close();
            };

            addContactViewModel.Cancelled += (sender, e) =>
            {
                addContactWindow.Close();
            };

            addContactWindow.Show();
            addContactWindow.Activate();
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في فتح نافذة إضافة الرقم: {ex.Message}";
        }
    }

    private void EditContact(Contact contact)
    {
        // TODO: Open edit contact dialog
        StatusMessage = $"تعديل {contact.Name} - سيتم تطوير هذه الميزة قريباً";
    }

    private async Task DeleteContactAsync(Contact contact)
    {
        try
        {
            // TODO: Show confirmation dialog
            await _databaseService.DeleteContactAsync(contact.Id);
            Contacts.Remove(contact);
            FilteredContacts.Remove(contact);
            ContactsCount = Contacts.Count;
            StatusMessage = $"تم حذف {contact.Name}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في حذف الرقم: {ex.Message}";
        }
    }

    private void ImportContacts()
    {
        StatusMessage = "سيتم تطوير ميزة الاستيراد قريباً";
    }

    private void ExportContacts()
    {
        StatusMessage = "سيتم تطوير ميزة التصدير قريباً";
    }

    private async Task ExportToExcelAsync()
    {
        try
        {
            StatusMessage = "جاري تصدير البيانات إلى CSV (متوافق مع Excel)...";

            var storageProvider = TopLevel.GetTopLevel(Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop ? desktop.MainWindow : null)?.StorageProvider;
            if (storageProvider == null)
            {
                StatusMessage = "خطأ في الوصول لنظام الملفات";
                return;
            }

            var file = await storageProvider.SaveFilePickerAsync(new FilePickerSaveOptions
            {
                Title = "حفظ ملف CSV (متوافق مع Excel)",
                DefaultExtension = "csv",
                SuggestedFileName = $"contacts_{DateTime.Now:yyyy-MM-dd}.csv",
                FileTypeChoices = new[]
                {
                    new FilePickerFileType("CSV Files (Excel Compatible)") { Patterns = new[] { "*.csv" } },
                    new FilePickerFileType("Text Files") { Patterns = new[] { "*.txt" } }
                }
            });

            if (file != null)
            {
                var csvContent = GenerateExcelCompatibleCsvContent();
                await using var stream = await file.OpenWriteAsync();
                await using var writer = new StreamWriter(stream, Encoding.UTF8);

                // Add BOM for Excel compatibility with Arabic text
                writer.Write('\uFEFF');
                await writer.WriteAsync(csvContent);

                StatusMessage = $"تم تصدير {Contacts.Count} رقم إلى CSV (متوافق مع Excel) بنجاح";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تصدير CSV: {ex.Message}";
        }
    }

    private async Task ImportFromExcelAsync()
    {
        try
        {
            StatusMessage = "جاري استيراد البيانات من Excel...";

            var storageProvider = TopLevel.GetTopLevel(Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop ? desktop.MainWindow : null)?.StorageProvider;
            if (storageProvider == null)
            {
                StatusMessage = "خطأ في الوصول لنظام الملفات";
                return;
            }

            var files = await storageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
            {
                Title = "اختر ملف Excel",
                AllowMultiple = false,
                FileTypeFilter = new[]
                {
                    new FilePickerFileType("Excel Files") { Patterns = new[] { "*.xlsx", "*.xls" } }
                }
            });

            if (files?.Count > 0)
            {
                await using var stream = await files[0].OpenReadAsync();
                using var reader = new StreamReader(stream);
                var content = await reader.ReadToEndAsync();

                var importedCount = await ImportCsvContentAsync(content);
                StatusMessage = $"تم استيراد {importedCount} رقم من Excel بنجاح";
                await LoadDataAsync();
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في استيراد Excel: {ex.Message}";
        }
    }

    private async Task ExportToCsvAsync()
    {
        try
        {
            StatusMessage = "جاري تصدير البيانات إلى CSV...";

            var storageProvider = TopLevel.GetTopLevel(Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop ? desktop.MainWindow : null)?.StorageProvider;
            if (storageProvider == null)
            {
                StatusMessage = "خطأ في الوصول لنظام الملفات";
                return;
            }

            var file = await storageProvider.SaveFilePickerAsync(new FilePickerSaveOptions
            {
                Title = "حفظ ملف CSV",
                DefaultExtension = "csv",
                SuggestedFileName = $"contacts_{DateTime.Now:yyyy-MM-dd}.csv",
                FileTypeChoices = new[]
                {
                    new FilePickerFileType("CSV Files") { Patterns = new[] { "*.csv" } }
                }
            });

            if (file != null)
            {
                var csvContent = GenerateCsvContent();
                await using var stream = await file.OpenWriteAsync();
                await using var writer = new StreamWriter(stream, Encoding.UTF8);
                await writer.WriteAsync(csvContent);

                StatusMessage = $"تم تصدير {Contacts.Count} رقم إلى CSV بنجاح";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تصدير CSV: {ex.Message}";
        }
    }

    private async Task ImportFromCsvAsync()
    {
        try
        {
            StatusMessage = "جاري استيراد البيانات من CSV...";

            var storageProvider = TopLevel.GetTopLevel(Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop ? desktop.MainWindow : null)?.StorageProvider;
            if (storageProvider == null)
            {
                StatusMessage = "خطأ في الوصول لنظام الملفات";
                return;
            }

            var files = await storageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
            {
                Title = "اختر ملف CSV",
                AllowMultiple = false,
                FileTypeFilter = new[]
                {
                    new FilePickerFileType("CSV Files") { Patterns = new[] { "*.csv" } }
                }
            });

            if (files?.Count > 0)
            {
                await using var stream = await files[0].OpenReadAsync();
                using var reader = new StreamReader(stream);
                var content = await reader.ReadToEndAsync();

                var importedCount = await ImportCsvContentAsync(content);
                StatusMessage = $"تم استيراد {importedCount} رقم من CSV بنجاح";
                await LoadDataAsync();
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في استيراد CSV: {ex.Message}";
        }
    }

    private string GenerateCsvContent()
    {
        var csv = new StringBuilder();

        // Header
        csv.AppendLine("الاسم,رقم الهاتف,البريد الإلكتروني,الملاحظات");

        // Data
        foreach (var contact in Contacts)
        {
            csv.AppendLine($"\"{contact.Name}\",\"{contact.PhoneNumber}\",\"{contact.Email ?? ""}\",\"{contact.Notes ?? ""}\"");
        }

        return csv.ToString();
    }

    private string GenerateExcelCompatibleCsvContent()
    {
        var csv = new StringBuilder();

        // Header with semicolon separator (better for Excel in some regions)
        csv.AppendLine("الاسم;رقم الهاتف;البريد الإلكتروني;الملاحظات");

        // Data with semicolon separator
        foreach (var contact in Contacts)
        {
            var name = EscapeCsvField(contact.Name);
            var phone = EscapeCsvField(contact.PhoneNumber);
            var email = EscapeCsvField(contact.Email ?? "");
            var notes = EscapeCsvField(contact.Notes ?? "");

            csv.AppendLine($"{name};{phone};{email};{notes}");
        }

        return csv.ToString();
    }

    private string EscapeCsvField(string field)
    {
        if (string.IsNullOrEmpty(field))
            return "";

        // If field contains semicolon, comma, newline, or quotes, wrap in quotes
        if (field.Contains(';') || field.Contains(',') || field.Contains('\n') || field.Contains('\r') || field.Contains('"'))
        {
            // Escape quotes by doubling them
            field = field.Replace("\"", "\"\"");
            return $"\"{field}\"";
        }

        return field;
    }

    private async Task<int> ImportCsvContentAsync(string content)
    {
        var lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        var importedCount = 0;

        // Skip header if exists
        var startIndex = lines.Length > 0 && (lines[0].Contains("الاسم") || lines[0].Contains("Name")) ? 1 : 0;

        for (int i = startIndex; i < lines.Length; i++)
        {
            try
            {
                var parts = ParseCsvLine(lines[i]);
                if (parts.Length >= 2 && !string.IsNullOrWhiteSpace(parts[0]) && !string.IsNullOrWhiteSpace(parts[1]))
                {
                    var contact = new Contact
                    {
                        Name = parts[0].Trim(),
                        PhoneNumber = parts[1].Trim(),
                        Email = parts.Length > 2 ? parts[2].Trim() : null,
                        Notes = parts.Length > 3 ? parts[3].Trim() : null
                    };

                    // Check if contact already exists
                    if (!Contacts.Any(c => c.PhoneNumber == contact.PhoneNumber))
                    {
                        await _databaseService.AddContactAsync(contact);
                        importedCount++;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but continue with other lines
                Console.WriteLine($"Error importing line {i + 1}: {ex.Message}");
            }
        }

        return importedCount;
    }

    private string[] ParseCsvLine(string line)
    {
        // Try semicolon first, then comma
        var separators = new char[] { ';', ',' };

        foreach (var separator in separators)
        {
            var result = ParseCsvLineWithSeparator(line, separator);
            if (result.Length > 1) // If we got multiple fields, this is probably the right separator
            {
                return result;
            }
        }

        // Fallback to comma separator
        return ParseCsvLineWithSeparator(line, ',');
    }

    private string[] ParseCsvLineWithSeparator(string line, char separator)
    {
        var result = new List<string>();
        var current = new StringBuilder();
        bool inQuotes = false;

        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];

            if (c == '"')
            {
                if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                {
                    // Double quote - add single quote to result
                    current.Append('"');
                    i++; // Skip next quote
                }
                else
                {
                    inQuotes = !inQuotes;
                }
            }
            else if (c == separator && !inQuotes)
            {
                result.Add(current.ToString().Trim());
                current.Clear();
            }
            else
            {
                current.Append(c);
            }
        }

        result.Add(current.ToString().Trim());
        return result.ToArray();
    }
}
