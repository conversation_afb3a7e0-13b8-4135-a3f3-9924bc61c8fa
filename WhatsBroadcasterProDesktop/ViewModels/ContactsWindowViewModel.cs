using System;
using System.Collections.ObjectModel;
using System.Reactive;
using ReactiveUI;
using WhatsBroadcasterProDesktop.Models;
using WhatsBroadcasterProDesktop.Services;
using WhatsBroadcasterProDesktop.Views;

namespace WhatsBroadcasterProDesktop.ViewModels;

public class ContactsWindowViewModel : ViewModelBase
{
    private readonly IDatabaseService _databaseService;
    
    private string _searchText = string.Empty;
    private Group? _selectedGroup;
    private Contact? _selectedContact;
    private string _statusMessage = "جاهز";
    private int _contactsCount;

    public ContactsWindowViewModel(IDatabaseService databaseService)
    {
        _databaseService = databaseService;

        // Initialize commands
        AddContactCommand = ReactiveCommand.Create(AddContact);
        EditContactCommand = ReactiveCommand.Create<Contact>(EditContact);
        DeleteContactCommand = ReactiveCommand.CreateFromTask<Contact>(DeleteContactAsync);
        SearchCommand = ReactiveCommand.CreateFromTask(SearchAsync);
        RefreshCommand = ReactiveCommand.CreateFromTask(LoadDataAsync);
        ImportContactsCommand = ReactiveCommand.Create(ImportContacts);
        ExportContactsCommand = ReactiveCommand.Create(ExportContacts);

        // Initialize data
        StatusMessage = "جاري تحميل البيانات...";
        _ = LoadDataAsync();
    }

    public string SearchText
    {
        get => _searchText;
        set => this.RaiseAndSetIfChanged(ref _searchText, value);
    }

    public Group? SelectedGroup
    {
        get => _selectedGroup;
        set => this.RaiseAndSetIfChanged(ref _selectedGroup, value);
    }

    public Contact? SelectedContact
    {
        get => _selectedContact;
        set => this.RaiseAndSetIfChanged(ref _selectedContact, value);
    }

    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    public int ContactsCount
    {
        get => _contactsCount;
        set => this.RaiseAndSetIfChanged(ref _contactsCount, value);
    }

    public bool HasContacts => FilteredContacts.Count > 0;

    public ObservableCollection<Contact> Contacts { get; } = new();
    public ObservableCollection<Contact> FilteredContacts { get; } = new();
    public ObservableCollection<Group> Groups { get; } = new();

    // Commands
    public ReactiveCommand<Unit, Unit> AddContactCommand { get; }
    public ReactiveCommand<Contact, Unit> EditContactCommand { get; }
    public ReactiveCommand<Contact, Unit> DeleteContactCommand { get; }
    public ReactiveCommand<Unit, Unit> SearchCommand { get; }
    public ReactiveCommand<Unit, Unit> RefreshCommand { get; }
    public ReactiveCommand<Unit, Unit> ImportContactsCommand { get; }
    public ReactiveCommand<Unit, Unit> ExportContactsCommand { get; }

    private async Task LoadDataAsync()
    {
        try
        {
            StatusMessage = "جاري تحميل البيانات...";

            // Load contacts
            var contacts = await _databaseService.GetContactsAsync();
            Contacts.Clear();
            foreach (var contact in contacts)
            {
                Contacts.Add(contact);
            }

            // Load groups
            var groups = await _databaseService.GetGroupsAsync();
            Groups.Clear();
            Groups.Add(new Group { Id = 0, Name = "جميع المجموعات" });
            foreach (var group in groups)
            {
                Groups.Add(group);
            }

            ContactsCount = contacts.Count;
            await ApplyFiltersAsync();
            StatusMessage = $"تم تحميل {contacts.Count} رقم";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
        }
    }

    private async Task SearchAsync()
    {
        await ApplyFiltersAsync();
    }

    private async Task ApplyFiltersAsync()
    {
        try
        {
            var filtered = Contacts.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filtered = filtered.Where(c =>
                    c.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    c.PhoneNumber.Contains(SearchText) ||
                    (!string.IsNullOrEmpty(c.Email) && c.Email.Contains(SearchText, StringComparison.OrdinalIgnoreCase)));
            }

            // Apply group filter
            if (SelectedGroup != null && SelectedGroup.Id > 0)
            {
                filtered = filtered.Where(c =>
                    c.ContactGroups.Any(cg => cg.GroupId == SelectedGroup.Id));
            }

            FilteredContacts.Clear();
            foreach (var contact in filtered)
            {
                FilteredContacts.Add(contact);
            }

            // Notify HasContacts property changed
            this.RaisePropertyChanged(nameof(HasContacts));

            StatusMessage = $"عرض {FilteredContacts.Count} من {Contacts.Count} رقم";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في التصفية: {ex.Message}";
        }
    }

    private void AddContact()
    {
        try
        {
            var addContactViewModel = new AddContactViewModel(_databaseService);
            var addContactWindow = new AddContactWindow
            {
                DataContext = addContactViewModel
            };

            // Handle events
            addContactViewModel.ContactAdded += async (sender, contact) =>
            {
                // Add to collections
                Contacts.Add(contact);
                await ApplyFiltersAsync();
                StatusMessage = $"تم إضافة الرقم '{contact.Name}' بنجاح";
                addContactWindow.Close();
            };

            addContactViewModel.Cancelled += (sender, e) =>
            {
                addContactWindow.Close();
            };

            addContactWindow.Show();
            addContactWindow.Activate();
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في فتح نافذة إضافة الرقم: {ex.Message}";
        }
    }

    private void EditContact(Contact contact)
    {
        // TODO: Open edit contact dialog
        StatusMessage = $"تعديل {contact.Name} - سيتم تطوير هذه الميزة قريباً";
    }

    private async Task DeleteContactAsync(Contact contact)
    {
        try
        {
            // TODO: Show confirmation dialog
            await _databaseService.DeleteContactAsync(contact.Id);
            Contacts.Remove(contact);
            FilteredContacts.Remove(contact);
            ContactsCount = Contacts.Count;
            StatusMessage = $"تم حذف {contact.Name}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في حذف الرقم: {ex.Message}";
        }
    }

    private void ImportContacts()
    {
        StatusMessage = "سيتم تطوير ميزة الاستيراد قريباً";
    }

    private void ExportContacts()
    {
        StatusMessage = "سيتم تطوير ميزة التصدير قريباً";
    }
}
