using System.Collections.ObjectModel;
using System.Reactive;
using ReactiveUI;
using WhatsBroadcasterProDesktop.Models;
using WhatsBroadcasterProDesktop.Services;

namespace WhatsBroadcasterProDesktop.ViewModels;

public class GroupsWindowViewModel : ViewModelBase
{
    private readonly IDatabaseService _databaseService;
    
    private Group? _selectedGroup;
    private string _statusMessage = "جاهز";

    public GroupsWindowViewModel(IDatabaseService databaseService)
    {
        _databaseService = databaseService;

        // Initialize commands
        AddGroupCommand = ReactiveCommand.Create(AddGroup);
        EditGroupCommand = ReactiveCommand.Create<Group>(EditGroup);
        DeleteGroupCommand = ReactiveCommand.CreateFromTask<Group>(DeleteGroupAsync);

        // Initialize data
        _ = LoadDataAsync();
    }

    public Group? SelectedGroup
    {
        get => _selectedGroup;
        set => this.RaiseAndSetIfChanged(ref _selectedGroup, value);
    }

    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    public ObservableCollection<Group> Groups { get; } = new();

    // Commands
    public ReactiveCommand<Unit, Unit> AddGroupCommand { get; }
    public ReactiveCommand<Group, Unit> EditGroupCommand { get; }
    public ReactiveCommand<Group, Unit> DeleteGroupCommand { get; }

    private async Task LoadDataAsync()
    {
        try
        {
            StatusMessage = "جاري تحميل المجموعات...";

            var groups = await _databaseService.GetGroupsAsync();
            Groups.Clear();
            foreach (var group in groups)
            {
                Groups.Add(group);
            }

            StatusMessage = $"تم تحميل {groups.Count} مجموعة";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في تحميل البيانات: {ex.Message}";
        }
    }

    private void AddGroup()
    {
        StatusMessage = "سيتم تطوير نافذة إضافة المجموعات قريباً";
    }

    private void EditGroup(Group group)
    {
        StatusMessage = $"تعديل {group.Name} - سيتم تطوير هذه الميزة قريباً";
    }

    private async Task DeleteGroupAsync(Group group)
    {
        try
        {
            await _databaseService.DeleteGroupAsync(group.Id);
            Groups.Remove(group);
            StatusMessage = $"تم حذف مجموعة {group.Name}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"خطأ في حذف المجموعة: {ex.Message}";
        }
    }
}
