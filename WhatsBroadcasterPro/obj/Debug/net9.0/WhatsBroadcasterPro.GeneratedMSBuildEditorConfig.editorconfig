is_global = true
build_property.MvvmToolkitEnableINotifyPropertyChangingSupport = true
build_property._MvvmToolkitIsUsingWindowsRuntimePack = false
build_property.CsWinRTComponent = 
build_property.CsWinRTAotOptimizerEnabled = 
build_property.CsWinRTAotWarningLevel = 
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = WhatsBroadcasterPro
build_property.ProjectDir = /Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/App.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = WhatsBroadcasterPro.App.xaml
build_metadata.AdditionalFiles.TargetPath = App.xaml
build_metadata.AdditionalFiles.RelativePath = App.xaml

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/AppShell.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = WhatsBroadcasterPro.AppShell.xaml
build_metadata.AdditionalFiles.TargetPath = AppShell.xaml
build_metadata.AdditionalFiles.RelativePath = AppShell.xaml

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/MainPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = WhatsBroadcasterPro.MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = MainPage.xaml

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/Resources/Styles/Colors.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = WhatsBroadcasterPro.Resources.Styles.Colors.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Colors.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Colors.xaml

[/Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/Resources/Styles/Styles.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = WhatsBroadcasterPro.Resources.Styles.Styles.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Styles.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Styles.xaml
