{"version": 2, "dgSpecHash": "OMyWk4glCzs=", "success": true, "projectFilePath": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/WhatsBroadcasterPro.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/communitytoolkit.mvvm/8.4.0/communitytoolkit.mvvm.8.4.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.data.sqlite.core/9.0.7/microsoft.data.sqlite.core.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore/9.0.7/microsoft.entityframeworkcore.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.abstractions/9.0.7/microsoft.entityframeworkcore.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.analyzers/9.0.7/microsoft.entityframeworkcore.analyzers.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.relational/9.0.7/microsoft.entityframeworkcore.relational.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.sqlite/9.0.7/microsoft.entityframeworkcore.sqlite.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.entityframeworkcore.sqlite.core/9.0.7/microsoft.entityframeworkcore.sqlite.core.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/9.0.7/microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.caching.memory/9.0.7/microsoft.extensions.caching.memory.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/9.0.0/microsoft.extensions.configuration.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.7/microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.7/microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.7/microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencymodel/9.0.7/microsoft.extensions.dependencymodel.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.7/microsoft.extensions.logging.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.7/microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.debug/9.0.0/microsoft.extensions.logging.debug.9.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/9.0.7/microsoft.extensions.options.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.7/microsoft.extensions.primitives.9.0.7.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls/9.0.14/microsoft.maui.controls.9.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.build.tasks/9.0.14/microsoft.maui.controls.build.tasks.9.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.core/9.0.14/microsoft.maui.controls.core.9.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.controls.xaml/9.0.14/microsoft.maui.controls.xaml.9.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.core/9.0.14/microsoft.maui.core.9.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.essentials/9.0.14/microsoft.maui.essentials.9.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.graphics/9.0.14/microsoft.maui.graphics.9.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.maui.resizetizer/9.0.14/microsoft.maui.resizetizer.9.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/selenium.webdriver/4.34.0/selenium.webdriver.4.34.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/selenium.webdriver.chromedriver/138.0.7204.15700/selenium.webdriver.chromedriver.138.0.7204.15700.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.bundle_e_sqlite3/2.1.10/sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.core/2.1.10/sqlitepclraw.core.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3/2.1.10/sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/sqlitepclraw.provider.e_sqlite3/2.1.10/sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.memory/4.5.3/system.memory.4.5.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/9.0.7/system.text.json.9.0.7.nupkg.sha512"], "logs": []}