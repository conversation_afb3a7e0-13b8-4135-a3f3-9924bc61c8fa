{"version": 3, "targets": {"net9.0": {"CommunityToolkit.Mvvm/8.4.0": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "Microsoft.Data.Sqlite.Core/9.0.7": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore/9.0.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.7", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.7": {"type": "package", "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.7": {"type": "package"}, "Microsoft.EntityFrameworkCore.Relational/9.0.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.7": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyModel": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.7"}, "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.7": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.7", "Microsoft.EntityFrameworkCore.Relational": "9.0.7", "Microsoft.Extensions.Caching.Memory": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyModel": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.7"}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/9.0.7": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "compile": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Options/9.0.7": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "compile": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.7": {"type": "package", "compile": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Maui.Controls/9.0.14": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Build.Tasks": "9.0.14", "Microsoft.Maui.Controls.Core": "9.0.14", "Microsoft.Maui.Controls.Xaml": "9.0.14", "Microsoft.Maui.Resizetizer": "9.0.14"}}, "Microsoft.Maui.Controls.Build.Tasks/9.0.14": {"type": "package", "dependencies": {"Microsoft.Maui.Controls.Core": "9.0.14", "Microsoft.Maui.Controls.Xaml": "9.0.14"}, "build": {"buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.props": {}, "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.targets": {}}}, "Microsoft.Maui.Controls.Core/9.0.14": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Maui.Core": "9.0.14"}, "compile": {"lib/net9.0/Microsoft.Maui.Controls.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Microsoft.Maui.Controls.dll": {"related": ".pdb;.xml"}}, "resource": {"lib/net9.0/ar/Microsoft.Maui.Controls.resources.dll": {"locale": "ar"}, "lib/net9.0/ca/Microsoft.Maui.Controls.resources.dll": {"locale": "ca"}, "lib/net9.0/cs/Microsoft.Maui.Controls.resources.dll": {"locale": "cs"}, "lib/net9.0/da/Microsoft.Maui.Controls.resources.dll": {"locale": "da"}, "lib/net9.0/de/Microsoft.Maui.Controls.resources.dll": {"locale": "de"}, "lib/net9.0/el/Microsoft.Maui.Controls.resources.dll": {"locale": "el"}, "lib/net9.0/es/Microsoft.Maui.Controls.resources.dll": {"locale": "es"}, "lib/net9.0/fi/Microsoft.Maui.Controls.resources.dll": {"locale": "fi"}, "lib/net9.0/fr/Microsoft.Maui.Controls.resources.dll": {"locale": "fr"}, "lib/net9.0/he/Microsoft.Maui.Controls.resources.dll": {"locale": "he"}, "lib/net9.0/hi/Microsoft.Maui.Controls.resources.dll": {"locale": "hi"}, "lib/net9.0/hr/Microsoft.Maui.Controls.resources.dll": {"locale": "hr"}, "lib/net9.0/hu/Microsoft.Maui.Controls.resources.dll": {"locale": "hu"}, "lib/net9.0/id/Microsoft.Maui.Controls.resources.dll": {"locale": "id"}, "lib/net9.0/it/Microsoft.Maui.Controls.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Maui.Controls.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Maui.Controls.resources.dll": {"locale": "ko"}, "lib/net9.0/ms/Microsoft.Maui.Controls.resources.dll": {"locale": "ms"}, "lib/net9.0/nb/Microsoft.Maui.Controls.resources.dll": {"locale": "nb"}, "lib/net9.0/nl/Microsoft.Maui.Controls.resources.dll": {"locale": "nl"}, "lib/net9.0/pl/Microsoft.Maui.Controls.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Maui.Controls.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/pt/Microsoft.Maui.Controls.resources.dll": {"locale": "pt"}, "lib/net9.0/ro/Microsoft.Maui.Controls.resources.dll": {"locale": "ro"}, "lib/net9.0/ru/Microsoft.Maui.Controls.resources.dll": {"locale": "ru"}, "lib/net9.0/sk/Microsoft.Maui.Controls.resources.dll": {"locale": "sk"}, "lib/net9.0/sv/Microsoft.Maui.Controls.resources.dll": {"locale": "sv"}, "lib/net9.0/th/Microsoft.Maui.Controls.resources.dll": {"locale": "th"}, "lib/net9.0/tr/Microsoft.Maui.Controls.resources.dll": {"locale": "tr"}, "lib/net9.0/uk/Microsoft.Maui.Controls.resources.dll": {"locale": "uk"}, "lib/net9.0/vi/Microsoft.Maui.Controls.resources.dll": {"locale": "vi"}, "lib/net9.0/zh-HK/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-HK"}, "lib/net9.0/zh-Hans/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Maui.Controls.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Maui.Controls.Xaml/9.0.14": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Maui.Controls.Core": "9.0.14", "Microsoft.Maui.Core": "9.0.14"}, "compile": {"lib/net9.0/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Microsoft.Maui.Controls.Xaml.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Core/9.0.14": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Maui.Essentials": "9.0.14", "Microsoft.Maui.Graphics": "9.0.14"}, "compile": {"lib/net9.0/Microsoft.Maui.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Microsoft.Maui.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/Microsoft.Maui.Core.props": {}, "buildTransitive/Microsoft.Maui.Core.targets": {}}}, "Microsoft.Maui.Essentials/9.0.14": {"type": "package", "dependencies": {"Microsoft.Maui.Graphics": "9.0.14"}, "compile": {"lib/net9.0/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Microsoft.Maui.Essentials.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Graphics/9.0.14": {"type": "package", "compile": {"lib/net9.0/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net9.0/Microsoft.Maui.Graphics.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Maui.Resizetizer/9.0.14": {"type": "package", "build": {"buildTransitive/Microsoft.Maui.Resizetizer.props": {}, "buildTransitive/Microsoft.Maui.Resizetizer.targets": {}}}, "Selenium.WebDriver/4.34.0": {"type": "package", "compile": {"lib/net8.0/WebDriver.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/WebDriver.dll": {"related": ".xml"}}, "build": {"buildTransitive/Selenium.WebDriver.targets": {}}}, "Selenium.WebDriver.ChromeDriver/138.0.7204.15700": {"type": "package", "build": {"build/Selenium.WebDriver.ChromeDriver.targets": {}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}}, "runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"assetType": "native", "rid": "browser-wasm"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-armel"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-mips64"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-s390x"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-ppc64le"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-s390x"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x86"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "compile": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Text.Json/9.0.7": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "Microsoft.Data.Sqlite.Core/9.0.7": {"sha512": "yjlU0Wu0tAexFPlo/bbkYFMWyEkVHqr5AONyh91YJ4KH+hys+eAzHQxd14aZwtQOgpJ5s9r3QL9+tVJww8w69Q==", "type": "package", "path": "microsoft.data.sqlite.core/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net6.0/Microsoft.Data.Sqlite.dll", "lib/net6.0/Microsoft.Data.Sqlite.xml", "lib/net8.0/Microsoft.Data.Sqlite.dll", "lib/net8.0/Microsoft.Data.Sqlite.xml", "lib/netstandard2.0/Microsoft.Data.Sqlite.dll", "lib/netstandard2.0/Microsoft.Data.Sqlite.xml", "microsoft.data.sqlite.core.9.0.7.nupkg.sha512", "microsoft.data.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore/9.0.7": {"sha512": "PbD0q5ax15r91jD4TN7xbDCjldZSz4JfpYN4ZZjAkWeUyROkV92Ydg0O2/1keFA+2u3KPsDkJMmBKv2zQ06ZVg==", "type": "package", "path": "microsoft.entityframeworkcore/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net8.0/Microsoft.EntityFrameworkCore.props", "lib/net8.0/Microsoft.EntityFrameworkCore.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.7": {"sha512": "YUXNerEkCf4OANO+zjuMznpUW7R8XxSCqmBfYhBrbrJVc09i84KkNgeUTaOUXCGogSK/3d7ORRhMqfUobnejBg==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.7": {"sha512": "HqiPjAvjVOsyA1svnjL81/Wk2MRQYMK/lxKVWvw0f5IcA//VcxBepVSAqe7CFirdsPXqe8rFKEwZROWZTz7Jqw==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "docs/PACKAGE.md", "microsoft.entityframeworkcore.analyzers.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/9.0.7": {"sha512": "Yo5joquG7L79H5BhtpqP8apu+KFOAYfvmj0dZnVkPElBY14wY5qva0SOcrDWzYw5BrJrhIArfCcJCJHBvMYiKg==", "type": "package", "path": "microsoft.entityframeworkcore.relational/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.7": {"sha512": "87dAv0nX4rBIa29L7sZdUZ1FE4NDn9J51g6WJ+j5dTUQwNEg52YDEmo+/TxBtRnBSAca9boo80k8F7+LzUo2qQ==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/_._", "microsoft.entityframeworkcore.sqlite.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.7": {"sha512": "DLB8n5Z7U8+xCkh+NSrvOlylCmveDg5RpPdqBftq5nU8Yt3vIdBg0X/YkESGDBWUL9h0vxuhgH2aqXL3FYz5tQ==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll", "lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.xml", "microsoft.entityframeworkcore.sqlite.core.9.0.7.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.core.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/9.0.7": {"sha512": "30necCQehcg9lFkMEIE7HczcoYGML8GUH6jlincA18d896fLZM9wl5tpTPJHgzANQE/6KXRLZSWbgevgg5csSw==", "type": "package", "path": "microsoft.extensions.caching.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/9.0.7": {"sha512": "nDu6c8fwrHQYccLnWnvyElrdkL3rZ97TZNqL+niMFUcApVBHdpDmKcRvciGymJ4Y0iLDTOo5J2XhDQEbNb+dFg==", "type": "package", "path": "microsoft.extensions.caching.memory/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net9.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net9.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.9.0.7.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration/9.0.0": {"sha512": "YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "type": "package", "path": "microsoft.extensions.configuration/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"sha512": "lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"sha512": "i05AYA91vgq0as84ROVCyltD2gnxaba/f1Qw2rG7mUsS0gv8cPTr1Gm7jPQHq7JTr4MJoQUcanLVs16tIOUJaQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"sha512": "iPK1FxbGFr2Xb+4Y+dTYI8Gupu9pOi8I3JPuPsrogUmEhe2hzZ9LpCmolMEBhVDo2ikcSr7G5zYiwaapHSQTew==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/9.0.7": {"sha512": "aXEt8QW1Fj9aC81GfkMtfip4wfbkEA7VBvNkx6Rx6ZKyqXIF/9qzRtH6v/2096IDK4lt6dlQp5Ajf+kjHfUdOA==", "type": "package", "path": "microsoft.extensions.dependencymodel/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/net9.0/Microsoft.Extensions.DependencyModel.dll", "lib/net9.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.9.0.7.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.7": {"sha512": "fdIeQpXYV8yxSWG03cCbU2Otdrq4NWuhnQLXokWLv3L9YcK055E7u8WFJvP+uuP4CFeCEoqZQL4yPcjuXhCZrg==", "type": "package", "path": "microsoft.extensions.logging/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.7.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"sha512": "sMM6NEAdUTE/elJ2wqjOi0iBWqZmSyaTByLF9e8XHv6DRJFFnOe0N+s8Uc6C91E4SboQCfLswaBIZ+9ZXA98AA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"sha512": "4wGlHsrLhYjLw4sFkfRixu2w4DK7dv60OjbvgbLGhUJk0eUPxYHhnszZ/P18nnAkfrPryvtOJ3ZTVev0kpqM6A==", "type": "package", "path": "microsoft.extensions.logging.debug/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net9.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net9.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.9.0.0.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.7": {"sha512": "trJnF6cRWgR5uMmHpGoHmM1wOVFdIYlELlkO9zX+RfieK0321Y55zrcs4AaEymKup7dxgEN/uJU25CAcMNQRXw==", "type": "package", "path": "microsoft.extensions.options/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.7.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.7": {"sha512": "ti/zD9BuuO50IqlvhWQs9GHxkCmoph5BHjGiWKdg2t6Or8XoyAfRJiKag+uvd/fpASnNklfsB01WpZ4fhAe0VQ==", "type": "package", "path": "microsoft.extensions.primitives/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.7.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Maui.Controls/9.0.14": {"sha512": "zPrAhFdnk5BhW9srz41e5+W9aBXfL3/5Z7Kg4I8NexvK5pjqoI8Ge9iY4XPB6SA9hFS9kg4dfloi/gV9LdEb7w==", "type": "package", "path": "microsoft.maui.controls/9.0.14", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "microsoft.maui.controls.9.0.14.nupkg.sha512", "microsoft.maui.controls.nuspec"]}, "Microsoft.Maui.Controls.Build.Tasks/9.0.14": {"sha512": "ullr7uBqL2Ix6e/EvC8jNLd8xNp0w1Lp2dK14XBg0dXYf84mUwLsahMd5mDPj/CT5nqA0in03YaEiX4KtmVsAw==", "type": "package", "path": "microsoft.maui.controls.build.tasks/9.0.14", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.iOS.targets", "buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.MacCatalyst.targets", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Sdk.Windows.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.BindingSourceGen.dll", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.BindingSourceGen.pdb", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.After.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.Before.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.dll", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.pdb", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.props", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Common.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.DefaultItems.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Globs.props", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SingleProject.Before.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SingleProject.targets", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SourceGen.dll", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.SourceGen.pdb", "buildTransitive/netstandard2.0/Microsoft.Maui.Controls.targets", "buildTransitive/netstandard2.0/Mono.Cecil.Mdb.dll", "buildTransitive/netstandard2.0/Mono.Cecil.Mdb.pdb", "buildTransitive/netstandard2.0/Mono.Cecil.Pdb.dll", "buildTransitive/netstandard2.0/Mono.Cecil.Pdb.pdb", "buildTransitive/netstandard2.0/Mono.Cecil.Rocks.dll", "buildTransitive/netstandard2.0/Mono.Cecil.Rocks.pdb", "buildTransitive/netstandard2.0/Mono.Cecil.dll", "buildTransitive/netstandard2.0/Mono.Cecil.pdb", "buildTransitive/netstandard2.0/System.CodeDom.dll", "buildTransitive/netstandard2.0/ar/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ca/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/cs/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/da/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/de/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/el/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/es/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/fi/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/fr/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/he/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/hi/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/hr/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/hu/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/id/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/it/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ja/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ko/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/maui-blazor.aotprofile", "buildTransitive/netstandard2.0/maui.aotprofile", "buildTransitive/netstandard2.0/ms/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/nb/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/nl/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/pl/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/proguard.cfg", "buildTransitive/netstandard2.0/pt-BR/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/pt/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ro/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/ru/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/sk/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/sv/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/th/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/tr/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/uk/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/vi/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/zh-HK/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/zh-Hans/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "buildTransitive/netstandard2.0/zh-Hant/Microsoft.Maui.Controls.Build.Tasks.resources.dll", "microsoft.maui.controls.build.tasks.9.0.14.nupkg.sha512", "microsoft.maui.controls.build.tasks.nuspec"]}, "Microsoft.Maui.Controls.Core/9.0.14": {"sha512": "7elrbAhr8304iPwKlVXVzUKIRTDJKWEoh4WoMXf/jPFm4tfS6vmc+mlzWgm1pzisKhebb3bX1dxXxeOx2IHGkg==", "type": "package", "path": "microsoft.maui.controls.core/9.0.14", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-android35.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Controls.aar", "lib/net9.0-android35.0/Microsoft.Maui.Controls.dll", "lib/net9.0-android35.0/Microsoft.Maui.Controls.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Controls.xml", "lib/net9.0-android35.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-android35.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-ios18.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.xml", "lib/net9.0-ios18.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-ios18.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-maccatalyst18.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.xml", "lib/net9.0-maccatalyst18.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-maccatalyst18.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-tizen7.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.xml", "lib/net9.0-tizen7.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-tizen7.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-windows10.0.19041/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.pri", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.xml", "lib/net9.0-windows10.0.19041/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.19041/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0-windows10.0.20348/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.pri", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.xml", "lib/net9.0-windows10.0.20348/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/zh-<PERSON>/Microsoft.Maui.Controls.resources.dll", "lib/net9.0-windows10.0.20348/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/net9.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/net9.0/Microsoft.Maui.Controls.dll", "lib/net9.0/Microsoft.Maui.Controls.pdb", "lib/net9.0/Microsoft.Maui.Controls.xml", "lib/net9.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/da/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/de/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/el/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/es/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/he/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/id/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/it/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/th/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Maui.Controls.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.dll", "lib/netstandard2.0/Microsoft.Maui.Controls.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.xml", "lib/netstandard2.0/ar/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ca/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/cs/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/da/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/de/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/el/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/es/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/fi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/fr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/he/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/hi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/hr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/hu/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/id/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/it/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ja/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ko/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ms/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/nb/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/nl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/pl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/pt/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ro/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/ru/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/sk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/sv/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/th/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/tr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/uk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/vi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/zh-<PERSON>/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.0/zh-Hant/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.DesignTools.dll", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.DesignTools.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.dll", "lib/netstandard2.1/Microsoft.Maui.Controls.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.xml", "lib/netstandard2.1/ar/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ca/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/cs/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/da/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/de/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/el/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/es/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/fi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/fr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/he/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/hi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/hr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/hu/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/id/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/it/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ja/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ko/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ms/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/nb/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/nl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/pl/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/pt-BR/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/pt/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ro/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/ru/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/sk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/sv/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/th/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/tr/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/uk/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/vi/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/zh-HK/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/zh-<PERSON>/Microsoft.Maui.Controls.resources.dll", "lib/netstandard2.1/zh-Hant/Microsoft.Maui.Controls.resources.dll", "microsoft.maui.controls.core.9.0.14.nupkg.sha512", "microsoft.maui.controls.core.nuspec"]}, "Microsoft.Maui.Controls.Xaml/9.0.14": {"sha512": "5HzEJT8lLO8wfNCAv8BkDi6UeUCjS5rn2LlWoKZWMTveZMmqwzYXalrPgNP1QR7JpsU4qlx9gKOy61A7tJFHuA==", "type": "package", "path": "microsoft.maui.controls.xaml/9.0.14", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-android35.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-ios18.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-ios18.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-maccatalyst18.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-maccatalyst18.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-tizen7.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-tizen7.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-windows10.0.19041/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-windows10.0.19041/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0-windows10.0.20348/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0-windows10.0.20348/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Controls.Xaml.xml", "lib/net9.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/net9.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/net9.0/Microsoft.Maui.Controls.Xaml.dll", "lib/net9.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/net9.0/Microsoft.Maui.Controls.Xaml.xml", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/netstandard2.0/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.Xaml.dll", "lib/netstandard2.0/Microsoft.Maui.Controls.Xaml.pdb", "lib/netstandard2.0/Microsoft.Maui.Controls.Xaml.xml", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.Xaml.DesignTools.dll", "lib/netstandard2.1/Design/Microsoft.Maui.Controls.Xaml.DesignTools.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.Xaml.dll", "lib/netstandard2.1/Microsoft.Maui.Controls.Xaml.pdb", "lib/netstandard2.1/Microsoft.Maui.Controls.Xaml.xml", "microsoft.maui.controls.xaml.9.0.14.nupkg.sha512", "microsoft.maui.controls.xaml.nuspec"]}, "Microsoft.Maui.Core/9.0.14": {"sha512": "nLTTA80+xkfC5YC9sDVUL2dFG1m9hfvVNWCzNsjZf1jJZ2oKJ6oXCBT+TgQuY/pClM4Plq0mjIL7uNWUMF0Kuw==", "type": "package", "path": "microsoft.maui.core/9.0.14", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/Microsoft.Maui.Core.After.targets", "buildTransitive/Microsoft.Maui.Core.Before.targets", "buildTransitive/Microsoft.Maui.Core.BundledVersions.targets", "buildTransitive/Microsoft.Maui.Core.props", "buildTransitive/Microsoft.Maui.Core.targets", "buildTransitive/WinUI.targets", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.props", "buildTransitive/net6.0-windows10.0.17763.0/Microsoft.Maui.Core.targets", "lib/net9.0-android35.0/Microsoft.Maui.aar", "lib/net9.0-android35.0/Microsoft.Maui.dll", "lib/net9.0-android35.0/Microsoft.Maui.pdb", "lib/net9.0-android35.0/Microsoft.Maui.xml", "lib/net9.0-android35.0/maui.aar", "lib/net9.0-ios18.0/Microsoft.Maui.dll", "lib/net9.0-ios18.0/Microsoft.Maui.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.xml", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.xml", "lib/net9.0-tizen7.0/Microsoft.Maui.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.xml", "lib/net9.0-windows10.0.19041/Microsoft.Maui.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.pri", "lib/net9.0-windows10.0.19041/Microsoft.Maui.xml", "lib/net9.0-windows10.0.20348/Microsoft.Maui.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.pri", "lib/net9.0-windows10.0.20348/Microsoft.Maui.xml", "lib/net9.0/Microsoft.Maui.dll", "lib/net9.0/Microsoft.Maui.pdb", "lib/net9.0/Microsoft.Maui.xml", "lib/netstandard2.0/Microsoft.Maui.dll", "lib/netstandard2.0/Microsoft.Maui.pdb", "lib/netstandard2.0/Microsoft.Maui.xml", "lib/netstandard2.1/Microsoft.Maui.dll", "lib/netstandard2.1/Microsoft.Maui.pdb", "lib/netstandard2.1/Microsoft.Maui.xml", "microsoft.maui.core.9.0.14.nupkg.sha512", "microsoft.maui.core.nuspec"]}, "Microsoft.Maui.Essentials/9.0.14": {"sha512": "63lagHcmONGoxlhTCasAqhrPWkgiXy5GwnFCFuEb+WdTfslgXMsFPtrwD+FJQ8xKiouetgT5VCIfZs3klXQa+w==", "type": "package", "path": "microsoft.maui.essentials/9.0.14", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Microsoft.Maui.Essentials.aar", "lib/net9.0-android35.0/Microsoft.Maui.Essentials.dll", "lib/net9.0-android35.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Essentials.xml", "lib/net9.0-ios18.0/Microsoft.Maui.Essentials.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Essentials.xml", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Essentials.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Essentials.xml", "lib/net9.0-tizen7.0/Microsoft.Maui.Essentials.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Essentials.xml", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Essentials.xml", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Essentials.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Essentials.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Essentials.xml", "lib/net9.0/Microsoft.Maui.Essentials.dll", "lib/net9.0/Microsoft.Maui.Essentials.pdb", "lib/net9.0/Microsoft.Maui.Essentials.xml", "lib/netstandard2.0/Microsoft.Maui.Essentials.dll", "lib/netstandard2.0/Microsoft.Maui.Essentials.pdb", "lib/netstandard2.0/Microsoft.Maui.Essentials.xml", "lib/netstandard2.1/Microsoft.Maui.Essentials.dll", "lib/netstandard2.1/Microsoft.Maui.Essentials.pdb", "lib/netstandard2.1/Microsoft.Maui.Essentials.xml", "microsoft.maui.essentials.9.0.14.nupkg.sha512", "microsoft.maui.essentials.nuspec"]}, "Microsoft.Maui.Graphics/9.0.14": {"sha512": "wCpUcyipEwUnw6kmLBdeO+DJhYQv3gfcLTv1aFcdhkCUx3Pj4eKuLPCUsGVRvt1PFbI6ZeUPBKMNT5wn7kNyGA==", "type": "package", "path": "microsoft.maui.graphics/9.0.14", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0-android35.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-android35.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-android35.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-ios18.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-ios18.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-ios18.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-maccatalyst18.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-macos15.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-macos15.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-macos15.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-tizen7.0/Microsoft.Maui.Graphics.dll", "lib/net9.0-tizen7.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0-tizen7.0/Microsoft.Maui.Graphics.xml", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.dll", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.pdb", "lib/net9.0-windows10.0.19041/Microsoft.Maui.Graphics.xml", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.dll", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.pdb", "lib/net9.0-windows10.0.20348/Microsoft.Maui.Graphics.xml", "lib/net9.0/Microsoft.Maui.Graphics.dll", "lib/net9.0/Microsoft.Maui.Graphics.pdb", "lib/net9.0/Microsoft.Maui.Graphics.xml", "lib/netstandard2.0/Microsoft.Maui.Graphics.dll", "lib/netstandard2.0/Microsoft.Maui.Graphics.pdb", "lib/netstandard2.0/Microsoft.Maui.Graphics.xml", "lib/netstandard2.1/Microsoft.Maui.Graphics.dll", "lib/netstandard2.1/Microsoft.Maui.Graphics.pdb", "lib/netstandard2.1/Microsoft.Maui.Graphics.xml", "microsoft.maui.graphics.9.0.14.nupkg.sha512", "microsoft.maui.graphics.nuspec"]}, "Microsoft.Maui.Resizetizer/9.0.14": {"sha512": "iaiBB5DGY4wD0Py0lIw3FSFQ5qvXcd3A6x0vbqfNpeEEFUddMjR8Wzs4gsNDSugMobXKra7gYuDgbWGlSDrIPw==", "type": "package", "path": "microsoft.maui.resizetizer/9.0.14", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/ExCSS.dll", "buildTransitive/Fizzler.dll", "buildTransitive/HarfBuzzSharp.dll", "buildTransitive/HarfBuzzSharp.pdb", "buildTransitive/Microsoft.Bcl.AsyncInterfaces.dll", "buildTransitive/Microsoft.Maui.Resizetizer.After.targets", "buildTransitive/Microsoft.Maui.Resizetizer.Before.targets", "buildTransitive/Microsoft.Maui.Resizetizer.dll", "buildTransitive/Microsoft.Maui.Resizetizer.pdb", "buildTransitive/Microsoft.Maui.Resizetizer.props", "buildTransitive/Microsoft.Maui.Resizetizer.targets", "buildTransitive/ShimSkiaSharp.dll", "buildTransitive/SkiaSharp.HarfBuzz.dll", "buildTransitive/SkiaSharp.HarfBuzz.pdb", "buildTransitive/SkiaSharp.dll", "buildTransitive/SkiaSharp.pdb", "buildTransitive/Svg.Custom.dll", "buildTransitive/Svg.Model.dll", "buildTransitive/Svg.Skia.dll", "buildTransitive/System.Buffers.dll", "buildTransitive/System.IO.UnmanagedMemoryStream.dll", "buildTransitive/System.Memory.dll", "buildTransitive/System.Numerics.Vectors.dll", "buildTransitive/System.ObjectModel.dll", "buildTransitive/System.Runtime.CompilerServices.Unsafe.dll", "buildTransitive/arm/libHarfBuzzSharp.so", "buildTransitive/arm/libSkiaSharp.so", "buildTransitive/arm64/libHarfBuzzSharp.dll", "buildTransitive/arm64/libHarfBuzzSharp.so", "buildTransitive/arm64/libSkiaSharp.dll", "buildTransitive/arm64/libSkiaSharp.so", "buildTransitive/libHarfBuzzSharp.dylib", "buildTransitive/libSkiaSharp.dylib", "buildTransitive/musl-x64/libHarfBuzzSharp.so", "buildTransitive/musl-x64/libSkiaSharp.so", "buildTransitive/x64/libHarfBuzzSharp.dll", "buildTransitive/x64/libHarfBuzzSharp.so", "buildTransitive/x64/libSkiaSharp.dll", "buildTransitive/x64/libSkiaSharp.so", "buildTransitive/x86/libHarfBuzzSharp.dll", "buildTransitive/x86/libSkiaSharp.dll", "microsoft.maui.resizetizer.9.0.14.nupkg.sha512", "microsoft.maui.resizetizer.nuspec"]}, "Selenium.WebDriver/4.34.0": {"sha512": "uNx+GF7WugHDPV2zpGDPlbSn3STQ6n0xFskFSeJdhEuuUTkIDfAsYnjVUiQidWDpy7mKzTz53ae7Thjghl3dng==", "type": "package", "path": "selenium.webdriver/4.34.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Selenium.WebDriver.targets", "buildTransitive/Selenium.WebDriver.targets", "icon.png", "lib/net8.0/WebDriver.dll", "lib/net8.0/WebDriver.xml", "lib/netstandard2.0/WebDriver.dll", "lib/netstandard2.0/WebDriver.xml", "manager/linux/selenium-manager", "manager/macos/selenium-manager", "manager/windows/selenium-manager.exe", "selenium.webdriver.4.34.0.nupkg.sha512", "selenium.webdriver.nuspec"]}, "Selenium.WebDriver.ChromeDriver/138.0.7204.15700": {"sha512": "TXuEqxz4dHy+Xp+C+lQJAqqAACO8EWP92l+wc5XtWBF5JQsF0Qua/aaGgeTnNYwnQLwJcsvMJJjSzyvXdCYQKA==", "type": "package", "path": "selenium.webdriver.chromedriver/138.0.7204.15700", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "README.md", "build/DefinePropertiesChromeDriver.targets", "build/Selenium.WebDriver.ChromeDriver.targets", "driver/linux64/LICENSE.chromedriver", "driver/linux64/chromedriver", "driver/mac64/LICENSE.chromedriver", "driver/mac64/chromedriver", "driver/mac64arm/LICENSE.chromedriver", "driver/mac64arm/chromedriver", "driver/win32/LICENSE.chromedriver", "driver/win32/chromedriver.exe", "nupkg-icon.png", "selenium.webdriver.chromedriver.138.0.7204.15700.nupkg.sha512", "selenium.webdriver.chromedriver.nuspec"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"sha512": "UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "type": "package", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid90/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.xml", "lib/net6.0-ios14.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-ios14.2/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-tvos10.0/SQLitePCLRaw.batteries_v2.dll", "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll", "lib/xamarinios10/SQLitePCLRaw.batteries_v2.dll", "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.bundle_e_sqlite3.nuspec"]}, "SQLitePCLRaw.core/2.1.10": {"sha512": "Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "type": "package", "path": "sqlitepclraw.core/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.core.dll", "sqlitepclraw.core.2.1.10.nupkg.sha512", "sqlitepclraw.core.nuspec"]}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"sha512": "mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "type": "package", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/net461/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net8.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net9.0/SQLitePCLRaw.lib.e_sqlite3.targets", "lib/net461/_._", "lib/netstandard2.0/_._", "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net8.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a", "runtimes/linux-arm/native/libe_sqlite3.so", "runtimes/linux-arm64/native/libe_sqlite3.so", "runtimes/linux-armel/native/libe_sqlite3.so", "runtimes/linux-mips64/native/libe_sqlite3.so", "runtimes/linux-musl-arm/native/libe_sqlite3.so", "runtimes/linux-musl-arm64/native/libe_sqlite3.so", "runtimes/linux-musl-s390x/native/libe_sqlite3.so", "runtimes/linux-musl-x64/native/libe_sqlite3.so", "runtimes/linux-ppc64le/native/libe_sqlite3.so", "runtimes/linux-s390x/native/libe_sqlite3.so", "runtimes/linux-x64/native/libe_sqlite3.so", "runtimes/linux-x86/native/libe_sqlite3.so", "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib", "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib", "runtimes/osx-arm64/native/libe_sqlite3.dylib", "runtimes/osx-x64/native/libe_sqlite3.dylib", "runtimes/win-arm/native/e_sqlite3.dll", "runtimes/win-arm64/native/e_sqlite3.dll", "runtimes/win-x64/native/e_sqlite3.dll", "runtimes/win-x86/native/e_sqlite3.dll", "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll", "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.lib.e_sqlite3.nuspec"]}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"sha512": "uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "type": "package", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll", "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512", "sqlitepclraw.provider.e_sqlite3.nuspec"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Json/9.0.7": {"sha512": "u/lN2FEEXs3ghj2ta8tWA4r2MS9Yni07K7jDmnz8h1UPDf0lIIIEMkWx383Zz4fJjJio7gDl+00RYuQ/7R8ZQw==", "type": "package", "path": "system.text.json/9.0.7", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.7.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["CommunityToolkit.Mvvm >= 8.4.0", "Microsoft.EntityFrameworkCore.Sqlite >= 9.0.7", "Microsoft.Extensions.Logging.Debug >= 9.0.0", "Microsoft.Maui.Controls >= 9.0.14", "Selenium.WebDriver >= 4.34.0", "Selenium.WebDriver.ChromeDriver >= 138.0.7204.15700"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/WhatsBroadcasterPro.csproj", "projectName": "WhatsBroadcasterPro", "projectPath": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/WhatsBroadcasterPro.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Projects/whatsApp/WhatsBroadcasterPro/obj/", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[9.0.14, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.34.0, )"}, "Selenium.WebDriver.ChromeDriver": {"target": "Package", "version": "[138.0.7204.15700, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.202/PortableRuntimeIdentifierGraph.json"}}}}