﻿using Microsoft.Extensions.Logging;
using WhatsBroadcasterPro.Services;
using WhatsBroadcasterPro.ViewModels;
using WhatsBroadcasterPro.Views;

namespace WhatsBroadcasterPro;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
		var builder = MauiApp.CreateBuilder();
		builder
			.UseMauiApp<App>()
			.ConfigureFonts(fonts =>
			{
				fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
				fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
			});

		// Register services
		builder.Services.AddSingleton<IDatabaseService, DatabaseService>();
		builder.Services.AddSingleton<IWhatsAppService, WhatsAppService>();

		// Register ViewModels
		builder.Services.AddTransient<MainViewModel>();
		builder.Services.AddTransient<ContactsViewModel>();
		builder.Services.AddTransient<GroupsViewModel>();
		builder.Services.AddTransient<BroadcastViewModel>();

		// Register Views
		builder.Services.AddTransient<MainPage>();
		builder.Services.AddTransient<ContactsPage>();
		builder.Services.AddTransient<GroupsPage>();
		builder.Services.AddTransient<BroadcastPage>();

#if DEBUG
		builder.Logging.AddDebug();
#endif

		return builder.Build();
	}
}
