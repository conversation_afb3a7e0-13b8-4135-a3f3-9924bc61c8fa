<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="WhatsBroadcasterPro.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:WhatsBroadcasterPro"
    xmlns:views="clr-namespace:WhatsBroadcasterPro.Views"
    Shell.FlyoutBehavior="Flyout"
    Title="WhatsBroadcaster Pro">

    <!-- Flyout Header -->
    <Shell.FlyoutHeader>
        <Border BackgroundColor="#007ACC" Padding="20">
            <StackLayout Orientation="Horizontal">
                <Label Text="📱" FontSize="32" VerticalOptions="Center" TextColor="White" />
                <StackLayout Margin="15,0,0,0">
                    <Label Text="WhatsBroadcaster Pro"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="White" />
                    <Label Text="إدارة رسائل الواتساب الجماعية"
                           FontSize="12"
                           TextColor="LightGray" />
                </StackLayout>
            </StackLayout>
        </Border>
    </Shell.FlyoutHeader>

    <!-- Main Pages -->
    <FlyoutItem Title="الرئيسية" Icon="home.png">
        <ShellContent
            Title="الرئيسية"
            ContentTemplate="{DataTemplate local:MainPage}"
            Route="main" />
    </FlyoutItem>

    <FlyoutItem Title="إدارة الأرقام" Icon="contacts.png">
        <ShellContent
            Title="إدارة الأرقام"
            ContentTemplate="{DataTemplate views:ContactsPage}"
            Route="contacts" />
    </FlyoutItem>

    <FlyoutItem Title="إدارة المجموعات" Icon="groups.png">
        <ShellContent
            Title="إدارة المجموعات"
            ContentTemplate="{DataTemplate views:GroupsPage}"
            Route="groups" />
    </FlyoutItem>

    <FlyoutItem Title="الإرسال الجماعي" Icon="broadcast.png">
        <ShellContent
            Title="الإرسال الجماعي"
            ContentTemplate="{DataTemplate views:BroadcastPage}"
            Route="broadcast" />
    </FlyoutItem>

    <!-- Flyout Footer -->
    <Shell.FlyoutFooter>
        <Border BackgroundColor="LightGray" Padding="20">
            <StackLayout>
                <Label Text="الإصدار 1.0"
                       FontSize="12"
                       TextColor="Gray"
                       HorizontalOptions="Center" />
                <Label Text="© 2024 WhatsBroadcaster Pro"
                       FontSize="10"
                       TextColor="Gray"
                       HorizontalOptions="Center" />
            </StackLayout>
        </Border>
    </Shell.FlyoutFooter>

</Shell>
