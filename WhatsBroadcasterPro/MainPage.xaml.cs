﻿using WhatsBroadcasterPro.Services;
using WhatsBroadcasterPro.Views;

namespace WhatsBroadcasterPro;

public partial class MainPage : ContentPage
{
	private readonly IDatabaseService _databaseService;
	private readonly IWhatsAppService _whatsAppService;

	public MainPage(IDatabaseService databaseService, IWhatsAppService whatsAppService)
	{
		InitializeComponent();
		_databaseService = databaseService;
		_whatsAppService = whatsAppService;

		Loaded += OnPageLoaded;
	}

	private async void OnPageLoaded(object sender, EventArgs e)
	{
		await LoadDashboardData();
	}

	private async Task LoadDashboardData()
	{
		try
		{
			// Load contacts count
			var contacts = await _databaseService.GetContactsAsync();
			ContactsCountLabel.Text = $"{contacts.Count} رقم";

			// Load groups count
			var groups = await _databaseService.GetGroupsAsync();
			GroupsCountLabel.Text = $"{groups.Count} مجموعة";

			// Load recent messages
			var recentMessages = await _databaseService.GetBroadcastMessagesAsync();
			RecentMessagesCollectionView.ItemsSource = recentMessages.Take(5).ToList();

			// Update connection status
			UpdateConnectionStatus();
		}
		catch (Exception ex)
		{
			await DisplayAlert("خطأ", $"حدث خطأ في تحميل البيانات: {ex.Message}", "موافق");
		}
	}

	private void UpdateConnectionStatus()
	{
		if (_whatsAppService.IsConnected)
		{
			ConnectionStatusLabel.Text = "متصل";
			ConnectionStatusLabel.TextColor = Colors.Green;
			ConnectButton.Text = "قطع الاتصال";
			ConnectButton.BackgroundColor = Colors.Red;
		}
		else
		{
			ConnectionStatusLabel.Text = "غير متصل";
			ConnectionStatusLabel.TextColor = Colors.Red;
			ConnectButton.Text = "الاتصال بواتساب";
			ConnectButton.BackgroundColor = Colors.Orange;
		}
	}

	private async void OnContactsClicked(object sender, EventArgs e)
	{
		await Shell.Current.GoToAsync("//contacts");
	}

	private async void OnGroupsClicked(object sender, EventArgs e)
	{
		await Shell.Current.GoToAsync("//groups");
	}

	private async void OnBroadcastClicked(object sender, EventArgs e)
	{
		await Shell.Current.GoToAsync("//broadcast");
	}

	private async void OnConnectWhatsAppClicked(object sender, EventArgs e)
	{
		try
		{
			if (_whatsAppService.IsConnected)
			{
				await _whatsAppService.DisconnectAsync();
			}
			else
			{
				ConnectButton.Text = "جاري الاتصال...";
				ConnectButton.IsEnabled = false;

				await _whatsAppService.ConnectAsync();
			}

			UpdateConnectionStatus();
		}
		catch (Exception ex)
		{
			await DisplayAlert("خطأ", $"حدث خطأ في الاتصال: {ex.Message}", "موافق");
		}
		finally
		{
			ConnectButton.IsEnabled = true;
		}
	}

	protected override async void OnAppearing()
	{
		base.OnAppearing();
		await LoadDashboardData();
	}
}

