﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WhatsBroadcasterPro.MainPage"
             Title="WhatsBroadcaster Pro">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" BackgroundColor="#007ACC" Padding="20">
            <StackLayout Orientation="Horizontal" HorizontalOptions="Center">
                <Label Text="📱" FontSize="32" VerticalOptions="Center" />
                <Label Text="WhatsBroadcaster Pro"
                       FontSize="24"
                       FontAttributes="Bold"
                       TextColor="White"
                       VerticalOptions="Center"
                       Margin="10,0,0,0" />
            </StackLayout>
        </Border>

        <!-- Main Content -->
        <ScrollView Grid.Row="1" Padding="20">
            <VerticalStackLayout Spacing="20">

                <!-- Dashboard Cards -->
                <Label Text="لوحة التحكم" FontSize="20" FontAttributes="Bold" Margin="0,0,0,10" />

                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto" ColumnSpacing="15" RowSpacing="15">

                    <!-- Contacts Card -->
                    <Border Grid.Row="0" Grid.Column="0"
                            BackgroundColor="LightBlue"
                            Stroke="DodgerBlue"
                            StrokeThickness="2"
                            Padding="20">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="10" />
                        </Border.StrokeShape>
                        <StackLayout>
                            <Label Text="👥" FontSize="32" HorizontalOptions="Center" />
                            <Label Text="إدارة الأرقام" FontSize="16" FontAttributes="Bold" HorizontalOptions="Center" />
                            <Label x:Name="ContactsCountLabel" Text="0 رقم" FontSize="14" HorizontalOptions="Center" />
                            <Button Text="إدارة الأرقام"
                                    BackgroundColor="DodgerBlue"
                                    TextColor="White"
                                    Margin="0,10,0,0"
                                    Clicked="OnContactsClicked" />
                        </StackLayout>
                    </Border>

                    <!-- Groups Card -->
                    <Border Grid.Row="0" Grid.Column="1"
                            BackgroundColor="LightGreen"
                            Stroke="Green"
                            StrokeThickness="2"
                            Padding="20">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="10" />
                        </Border.StrokeShape>
                        <StackLayout>
                            <Label Text="📁" FontSize="32" HorizontalOptions="Center" />
                            <Label Text="إدارة المجموعات" FontSize="16" FontAttributes="Bold" HorizontalOptions="Center" />
                            <Label x:Name="GroupsCountLabel" Text="0 مجموعة" FontSize="14" HorizontalOptions="Center" />
                            <Button Text="إدارة المجموعات"
                                    BackgroundColor="Green"
                                    TextColor="White"
                                    Margin="0,10,0,0"
                                    Clicked="OnGroupsClicked" />
                        </StackLayout>
                    </Border>

                    <!-- Broadcast Card -->
                    <Border Grid.Row="1" Grid.Column="0"
                            BackgroundColor="LightCoral"
                            Stroke="Red"
                            StrokeThickness="2"
                            Padding="20">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="10" />
                        </Border.StrokeShape>
                        <StackLayout>
                            <Label Text="📢" FontSize="32" HorizontalOptions="Center" />
                            <Label Text="إرسال جماعي" FontSize="16" FontAttributes="Bold" HorizontalOptions="Center" />
                            <Label Text="إرسال رسائل للمجموعات" FontSize="14" HorizontalOptions="Center" />
                            <Button Text="إرسال رسائل"
                                    BackgroundColor="Red"
                                    TextColor="White"
                                    Margin="0,10,0,0"
                                    Clicked="OnBroadcastClicked" />
                        </StackLayout>
                    </Border>

                    <!-- WhatsApp Connection Card -->
                    <Border Grid.Row="1" Grid.Column="1"
                            BackgroundColor="LightYellow"
                            Stroke="Orange"
                            StrokeThickness="2"
                            Padding="20">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="10" />
                        </Border.StrokeShape>
                        <StackLayout>
                            <Label Text="🔗" FontSize="32" HorizontalOptions="Center" />
                            <Label Text="اتصال واتساب" FontSize="16" FontAttributes="Bold" HorizontalOptions="Center" />
                            <Label x:Name="ConnectionStatusLabel" Text="غير متصل" FontSize="14" HorizontalOptions="Center" />
                            <Button x:Name="ConnectButton"
                                    Text="الاتصال بواتساب"
                                    BackgroundColor="Orange"
                                    TextColor="White"
                                    Margin="0,10,0,0"
                                    Clicked="OnConnectWhatsAppClicked" />
                        </StackLayout>
                    </Border>
                </Grid>

                <!-- Recent Messages -->
                <Label Text="الرسائل الأخيرة" FontSize="18" FontAttributes="Bold" Margin="0,20,0,10" />
                <CollectionView x:Name="RecentMessagesCollectionView" HeightRequest="200">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Border BackgroundColor="White"
                                    Stroke="LightGray"
                                    StrokeThickness="1"
                                    Padding="15"
                                    Margin="0,5">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="5" />
                                </Border.StrokeShape>
                                <Grid ColumnDefinitions="*,Auto">
                                    <StackLayout Grid.Column="0">
                                        <Label Text="{Binding Content}" FontSize="14" FontAttributes="Bold" />
                                        <Label Text="{Binding Group.Name, StringFormat='المجموعة: {0}'}" FontSize="12" TextColor="Gray" />
                                        <Label Text="{Binding CreatedAt, StringFormat='التاريخ: {0:yyyy/MM/dd HH:mm}'}" FontSize="12" TextColor="Gray" />
                                    </StackLayout>
                                    <Label Grid.Column="1"
                                           Text="{Binding Status}"
                                           FontSize="12"
                                           VerticalOptions="Center"
                                           BackgroundColor="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                                           TextColor="White"
                                           Padding="8,4" />
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

            </VerticalStackLayout>
        </ScrollView>
    </Grid>

</ContentPage>
