﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<OutputType>Exe</OutputType>
		<UseWPF>true</UseWPF>
		<UseWindowsForms>false</UseWindowsForms>
		<RootNamespace>WhatsBroadcasterPro</RootNamespace>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<AssemblyTitle>WhatsBroadcaster Pro</AssemblyTitle>
		<AssemblyVersion>1.0.0.0</AssemblyVersion>
		<FileVersion>1.0.0.0</FileVersion>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.7" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
		<PackageReference Include="Selenium.WebDriver" Version="4.34.0" />
		<PackageReference Include="Selenium.WebDriver.ChromeDriver" Version="138.0.7204.15700" />
	</ItemGroup>

</Project>
