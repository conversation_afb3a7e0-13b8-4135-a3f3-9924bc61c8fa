using Microsoft.EntityFrameworkCore;
using WhatsBroadcasterPro.Models;

namespace WhatsBroadcasterPro.Data
{
    public class WhatsAppDbContext : DbContext
    {
        public DbSet<Contact> Contacts { get; set; }
        public DbSet<Group> Groups { get; set; }
        public DbSet<ContactGroup> ContactGroups { get; set; }
        public DbSet<BroadcastMessage> BroadcastMessages { get; set; }
        public DbSet<MessageLog> MessageLogs { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var dbPath = Path.Combine(FileSystem.AppDataDirectory, "whatsapp_broadcaster.db");
            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Contact entity
            modelBuilder.Entity<Contact>(entity =>
            {
                entity.HasIndex(e => e.PhoneNumber).IsUnique();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            });

            // Configure Group entity
            modelBuilder.Entity<Group>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
            });

            // Configure ContactGroup many-to-many relationship
            modelBuilder.Entity<ContactGroup>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.ContactId, e.GroupId }).IsUnique();

                entity.HasOne(e => e.Contact)
                    .WithMany(e => e.ContactGroups)
                    .HasForeignKey(e => e.ContactId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Group)
                    .WithMany(e => e.ContactGroups)
                    .HasForeignKey(e => e.GroupId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.AddedAt).HasDefaultValueSql("datetime('now')");
            });

            // Configure BroadcastMessage entity
            modelBuilder.Entity<BroadcastMessage>(entity =>
            {
                entity.HasOne(e => e.Group)
                    .WithMany(e => e.BroadcastMessages)
                    .HasForeignKey(e => e.GroupId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.Property(e => e.CreatedAt).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.Status).HasConversion<string>();
                entity.Property(e => e.Type).HasConversion<string>();
            });

            // Configure MessageLog entity
            modelBuilder.Entity<MessageLog>(entity =>
            {
                entity.HasOne(e => e.BroadcastMessage)
                    .WithMany(e => e.MessageLogs)
                    .HasForeignKey(e => e.BroadcastMessageId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Contact)
                    .WithMany(e => e.MessageLogs)
                    .HasForeignKey(e => e.ContactId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.SentAt).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.Status).HasConversion<string>();
            });

            // Seed default data
            modelBuilder.Entity<Group>().HasData(
                new Group { Id = 1, Name = "العملاء", Description = "مجموعة العملاء الرئيسية", Color = "#007ACC" },
                new Group { Id = 2, Name = "الأصدقاء", Description = "مجموعة الأصدقاء", Color = "#28A745" },
                new Group { Id = 3, Name = "العائلة", Description = "أفراد العائلة", Color = "#DC3545" }
            );
        }
    }
}
