using System.ComponentModel.DataAnnotations;

namespace WhatsBroadcasterPro.Models
{
    public enum MessageStatus
    {
        Pending,
        Sending,
        Sent,
        Failed,
        Scheduled
    }

    public enum MessageType
    {
        Text,
        Image,
        Document,
        Video
    }

    public class BroadcastMessage
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(1000)]
        public string Content { get; set; } = string.Empty;

        public MessageType Type { get; set; } = MessageType.Text;

        [StringLength(500)]
        public string? MediaPath { get; set; }

        public int? GroupId { get; set; }

        public MessageStatus Status { get; set; } = MessageStatus.Pending;

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? ScheduledAt { get; set; }

        public DateTime? SentAt { get; set; }

        public int TotalRecipients { get; set; }

        public int SuccessfulSends { get; set; }

        public int FailedSends { get; set; }

        // Navigation properties
        public virtual Group? Group { get; set; }
        public virtual ICollection<MessageLog> MessageLogs { get; set; } = new List<MessageLog>();
    }
}
