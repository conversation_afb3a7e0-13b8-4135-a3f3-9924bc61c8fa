using System.ComponentModel.DataAnnotations;

namespace WhatsBroadcasterPro.Models
{
    public class Group
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(7)]
        public string Color { get; set; } = "#007ACC"; // Default blue color

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime? UpdatedAt { get; set; }

        // Navigation property
        public virtual ICollection<ContactGroup> ContactGroups { get; set; } = new List<ContactGroup>();
        public virtual ICollection<BroadcastMessage> BroadcastMessages { get; set; } = new List<BroadcastMessage>();
    }
}
