using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WhatsBroadcasterPro.Models;
using WhatsBroadcasterPro.Services;

namespace WhatsBroadcasterPro.ViewModels
{
    public partial class GroupsViewModel : ObservableObject
    {
        private readonly IDatabaseService _databaseService;

        [ObservableProperty]
        private ObservableCollection<Group> groups = new();

        [ObservableProperty]
        private bool isLoading;

        [ObservableProperty]
        private bool hasGroups;

        public GroupsViewModel(IDatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        public async Task InitializeAsync()
        {
            await LoadGroupsAsync();
        }

        [RelayCommand]
        private async Task LoadGroupsAsync()
        {
            IsLoading = true;
            try
            {
                var groupList = await _databaseService.GetGroupsAsync();
                Groups.Clear();
                
                foreach (var group in groupList)
                {
                    Groups.Add(group);
                }

                HasGroups = Groups.Any();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading groups: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task AddGroupAsync(Group group)
        {
            try
            {
                var addedGroup = await _databaseService.AddGroupAsync(group);
                Groups.Add(addedGroup);
                HasGroups = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding group: {ex.Message}");
                throw;
            }
        }

        [RelayCommand]
        private async Task UpdateGroupAsync(Group group)
        {
            try
            {
                await _databaseService.UpdateGroupAsync(group);
                var existingGroup = Groups.FirstOrDefault(g => g.Id == group.Id);
                if (existingGroup != null)
                {
                    var index = Groups.IndexOf(existingGroup);
                    Groups[index] = group;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating group: {ex.Message}");
                throw;
            }
        }

        [RelayCommand]
        private async Task DeleteGroupAsync(int groupId)
        {
            try
            {
                var success = await _databaseService.DeleteGroupAsync(groupId);
                if (success)
                {
                    var groupToRemove = Groups.FirstOrDefault(g => g.Id == groupId);
                    if (groupToRemove != null)
                    {
                        Groups.Remove(groupToRemove);
                        HasGroups = Groups.Any();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting group: {ex.Message}");
                throw;
            }
        }
    }
}
