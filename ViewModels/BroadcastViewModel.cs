using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WhatsBroadcasterPro.Models;
using WhatsBroadcasterPro.Services;

namespace WhatsBroadcasterPro.ViewModels
{
    public partial class BroadcastViewModel : ObservableObject
    {
        private readonly IDatabaseService _databaseService;
        private readonly IWhatsAppService _whatsAppService;

        [ObservableProperty]
        private ObservableCollection<Group> groups = new();

        [ObservableProperty]
        private Group? selectedGroup;

        [ObservableProperty]
        private string messageContent = string.Empty;

        [ObservableProperty]
        private string? attachedFilePath;

        [ObservableProperty]
        private bool isScheduled;

        [ObservableProperty]
        private DateTime scheduledDateTime = DateTime.Now.AddHours(1);

        [ObservableProperty]
        private int delayBetweenMessages = 5;

        [ObservableProperty]
        private bool isSending;

        [ObservableProperty]
        private double sendProgress;

        [ObservableProperty]
        private ObservableCollection<SendStatusItem> sendStatusItems = new();

        public BroadcastViewModel(IDatabaseService databaseService, IWhatsAppService whatsAppService)
        {
            _databaseService = databaseService;
            _whatsAppService = whatsAppService;
            
            // Subscribe to WhatsApp service events
            _whatsAppService.MessageSent += OnMessageSent;
        }

        public async Task InitializeAsync()
        {
            await LoadGroupsAsync();
        }

        [RelayCommand]
        private async Task LoadGroupsAsync()
        {
            try
            {
                var groupList = await _databaseService.GetGroupsAsync();
                Groups.Clear();
                
                foreach (var group in groupList.Where(g => g.ContactGroups.Any()))
                {
                    Groups.Add(group);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading groups: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task SendBroadcastMessageAsync()
        {
            if (SelectedGroup == null || string.IsNullOrWhiteSpace(MessageContent))
                return;

            if (!_whatsAppService.IsConnected)
            {
                throw new InvalidOperationException("غير متصل بواتساب. يرجى الاتصال أولاً.");
            }

            IsSending = true;
            SendProgress = 0;
            SendStatusItems.Clear();

            try
            {
                // Get contacts from the selected group
                var contacts = await _databaseService.GetContactsByGroupAsync(SelectedGroup.Id);
                
                if (!contacts.Any())
                {
                    throw new InvalidOperationException("لا توجد أرقام في المجموعة المحددة.");
                }

                // Create broadcast message record
                var broadcastMessage = new BroadcastMessage
                {
                    Content = MessageContent,
                    Type = string.IsNullOrEmpty(AttachedFilePath) ? MessageType.Text : MessageType.Image,
                    MediaPath = AttachedFilePath,
                    GroupId = SelectedGroup.Id,
                    Status = IsScheduled ? MessageStatus.Scheduled : MessageStatus.Sending,
                    ScheduledAt = IsScheduled ? ScheduledDateTime : null,
                    TotalRecipients = contacts.Count
                };

                await _databaseService.SaveBroadcastMessageAsync(broadcastMessage);

                // Initialize status items
                foreach (var contact in contacts)
                {
                    SendStatusItems.Add(new SendStatusItem
                    {
                        ContactName = contact.Name,
                        PhoneNumber = contact.PhoneNumber,
                        Status = "في الانتظار",
                        StatusColor = Colors.Gray
                    });
                }

                if (IsScheduled)
                {
                    // TODO: Implement scheduling logic
                    broadcastMessage.Status = MessageStatus.Scheduled;
                    await _databaseService.SaveBroadcastMessageAsync(broadcastMessage);
                    return;
                }

                // Send messages
                var results = await _whatsAppService.SendBroadcastMessageAsync(contacts, MessageContent, AttachedFilePath);
                
                // Update broadcast message with results
                broadcastMessage.SuccessfulSends = results.Count(r => r);
                broadcastMessage.FailedSends = results.Count(r => !r);
                broadcastMessage.Status = MessageStatus.Sent;
                broadcastMessage.SentAt = DateTime.Now;
                
                await _databaseService.SaveBroadcastMessageAsync(broadcastMessage);

                // Create message logs
                for (int i = 0; i < contacts.Count; i++)
                {
                    var messageLog = new MessageLog
                    {
                        BroadcastMessageId = broadcastMessage.Id,
                        ContactId = contacts[i].Id,
                        Status = results[i] ? DeliveryStatus.Sent : DeliveryStatus.Failed,
                        SentAt = DateTime.Now
                    };
                    
                    await _databaseService.UpdateMessageLogAsync(messageLog);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error sending broadcast: {ex.Message}");
                throw;
            }
            finally
            {
                IsSending = false;
                SendProgress = 1.0;
            }
        }

        private void OnMessageSent(object? sender, MessageSentEventArgs e)
        {
            var statusItem = SendStatusItems.FirstOrDefault(s => s.PhoneNumber == e.PhoneNumber);
            if (statusItem != null)
            {
                statusItem.Status = e.Success ? "تم الإرسال" : "فشل";
                statusItem.StatusColor = e.Success ? Colors.Green : Colors.Red;
                
                if (!e.Success && !string.IsNullOrEmpty(e.ErrorMessage))
                {
                    statusItem.Status += $" - {e.ErrorMessage}";
                }
            }

            // Update progress
            var completedCount = SendStatusItems.Count(s => s.Status != "في الانتظار" && s.Status != "جاري الإرسال");
            SendProgress = (double)completedCount / SendStatusItems.Count;
        }

        public void Dispose()
        {
            if (_whatsAppService != null)
            {
                _whatsAppService.MessageSent -= OnMessageSent;
            }
        }
    }

    public partial class SendStatusItem : ObservableObject
    {
        [ObservableProperty]
        private string contactName = string.Empty;

        [ObservableProperty]
        private string phoneNumber = string.Empty;

        [ObservableProperty]
        private string status = string.Empty;

        [ObservableProperty]
        private Color statusColor = Colors.Gray;
    }
}
