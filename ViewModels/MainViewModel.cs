using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WhatsBroadcasterPro.Models;
using WhatsBroadcasterPro.Services;

namespace WhatsBroadcasterPro.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly IDatabaseService _databaseService;
        private readonly IWhatsAppService _whatsAppService;

        [ObservableProperty]
        private int contactsCount;

        [ObservableProperty]
        private int groupsCount;

        [ObservableProperty]
        private string connectionStatus = "غير متصل";

        [ObservableProperty]
        private bool isConnected;

        [ObservableProperty]
        private ObservableCollection<BroadcastMessage> recentMessages = new();

        [ObservableProperty]
        private bool isLoading;

        public MainViewModel(IDatabaseService databaseService, IWhatsAppService whatsAppService)
        {
            _databaseService = databaseService;
            _whatsAppService = whatsAppService;
            
            // Subscribe to WhatsApp service events
            _whatsAppService.ConnectionStatusChanged += OnConnectionStatusChanged;
        }

        public async Task InitializeAsync()
        {
            await LoadDashboardDataAsync();
        }

        [RelayCommand]
        private async Task LoadDashboardDataAsync()
        {
            IsLoading = true;
            try
            {
                // Load contacts count
                var contacts = await _databaseService.GetContactsAsync();
                ContactsCount = contacts.Count;

                // Load groups count
                var groups = await _databaseService.GetGroupsAsync();
                GroupsCount = groups.Count;

                // Load recent messages
                var messages = await _databaseService.GetBroadcastMessagesAsync();
                RecentMessages.Clear();
                foreach (var message in messages.Take(5))
                {
                    RecentMessages.Add(message);
                }

                // Update connection status
                UpdateConnectionStatus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading dashboard data: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ConnectToWhatsAppAsync()
        {
            try
            {
                if (_whatsAppService.IsConnected)
                {
                    await _whatsAppService.DisconnectAsync();
                }
                else
                {
                    ConnectionStatus = "جاري الاتصال...";
                    await _whatsAppService.ConnectAsync();
                }
            }
            catch (Exception ex)
            {
                ConnectionStatus = $"خطأ: {ex.Message}";
            }
        }

        private void OnConnectionStatusChanged(object? sender, string status)
        {
            ConnectionStatus = status;
            IsConnected = _whatsAppService.IsConnected;
        }

        private void UpdateConnectionStatus()
        {
            IsConnected = _whatsAppService.IsConnected;
            ConnectionStatus = IsConnected ? "متصل" : "غير متصل";
        }

        public void Dispose()
        {
            if (_whatsAppService != null)
            {
                _whatsAppService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            }
        }
    }
}
