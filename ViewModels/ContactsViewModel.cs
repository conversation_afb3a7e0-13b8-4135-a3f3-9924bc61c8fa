using System.Collections.ObjectModel;
using System.ComponentModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using WhatsBroadcasterPro.Models;
using WhatsBroadcasterPro.Services;

namespace WhatsBroadcasterPro.ViewModels
{
    public partial class ContactsViewModel : ObservableObject
    {
        private readonly IDatabaseService _databaseService;

        [ObservableProperty]
        private ObservableCollection<ContactViewModel> contacts = new();

        [ObservableProperty]
        private ObservableCollection<Group> groups = new();

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private Group? selectedGroup;

        [ObservableProperty]
        private bool isLoading;

        [ObservableProperty]
        private bool hasContacts;

        public ContactsViewModel(IDatabaseService databaseService)
        {
            _databaseService = databaseService;
        }

        public async Task InitializeAsync()
        {
            await LoadContactsAsync();
            await LoadGroupsAsync();
        }

        [RelayCommand]
        private async Task LoadContactsAsync()
        {
            IsLoading = true;
            try
            {
                var contactList = await _databaseService.GetContactsAsync();
                var contactViewModels = contactList.Select(c => new ContactViewModel(c)).ToList();
                
                Contacts.Clear();
                foreach (var contact in contactViewModels)
                {
                    Contacts.Add(contact);
                }

                HasContacts = Contacts.Any();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                // Handle error
                System.Diagnostics.Debug.WriteLine($"Error loading contacts: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task LoadGroupsAsync()
        {
            try
            {
                var groupList = await _databaseService.GetGroupsAsync();
                Groups.Clear();
                Groups.Add(new Group { Id = 0, Name = "جميع المجموعات" }); // All groups option
                
                foreach (var group in groupList)
                {
                    Groups.Add(group);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading groups: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task AddContactAsync(Contact contact)
        {
            try
            {
                var addedContact = await _databaseService.AddContactAsync(contact);
                var contactViewModel = new ContactViewModel(addedContact);
                Contacts.Add(contactViewModel);
                HasContacts = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding contact: {ex.Message}");
                throw;
            }
        }

        [RelayCommand]
        private async Task UpdateContactAsync(Contact contact)
        {
            try
            {
                await _databaseService.UpdateContactAsync(contact);
                var existingContact = Contacts.FirstOrDefault(c => c.Id == contact.Id);
                if (existingContact != null)
                {
                    existingContact.UpdateFromModel(contact);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating contact: {ex.Message}");
                throw;
            }
        }

        [RelayCommand]
        private async Task DeleteContactAsync(int contactId)
        {
            try
            {
                var success = await _databaseService.DeleteContactAsync(contactId);
                if (success)
                {
                    var contactToRemove = Contacts.FirstOrDefault(c => c.Id == contactId);
                    if (contactToRemove != null)
                    {
                        Contacts.Remove(contactToRemove);
                        HasContacts = Contacts.Any();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting contact: {ex.Message}");
                throw;
            }
        }

        [RelayCommand]
        private void SearchContacts()
        {
            ApplyFilters();
        }

        [RelayCommand]
        private void FilterByGroup()
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            var filteredContacts = Contacts.AsEnumerable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                filteredContacts = filteredContacts.Where(c => 
                    c.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    c.PhoneNumber.Contains(SearchText) ||
                    (!string.IsNullOrEmpty(c.Email) && c.Email.Contains(SearchText, StringComparison.OrdinalIgnoreCase)));
            }

            // Apply group filter
            if (SelectedGroup != null && SelectedGroup.Id > 0)
            {
                filteredContacts = filteredContacts.Where(c => 
                    c.GroupNames.Contains(SelectedGroup.Name));
            }

            // Update visibility for each contact
            foreach (var contact in Contacts)
            {
                contact.IsVisible = filteredContacts.Contains(contact);
            }
        }

        public List<ContactViewModel> GetSelectedContacts()
        {
            return Contacts.Where(c => c.IsSelected).ToList();
        }

        public void ClearSelection()
        {
            foreach (var contact in Contacts)
            {
                contact.IsSelected = false;
            }
        }
    }

    public partial class ContactViewModel : ObservableObject
    {
        [ObservableProperty]
        private int id;

        [ObservableProperty]
        private string name = string.Empty;

        [ObservableProperty]
        private string phoneNumber = string.Empty;

        [ObservableProperty]
        private string? email;

        [ObservableProperty]
        private string? notes;

        [ObservableProperty]
        private string groupNames = string.Empty;

        [ObservableProperty]
        private bool isSelected;

        [ObservableProperty]
        private bool isVisible = true;

        public ContactViewModel(Contact contact)
        {
            UpdateFromModel(contact);
        }

        public void UpdateFromModel(Contact contact)
        {
            Id = contact.Id;
            Name = contact.Name;
            PhoneNumber = contact.PhoneNumber;
            Email = contact.Email;
            Notes = contact.Notes;
            GroupNames = string.Join(", ", contact.ContactGroups.Select(cg => cg.Group.Name));
        }

        public Contact ToModel()
        {
            return new Contact
            {
                Id = Id,
                Name = Name,
                PhoneNumber = PhoneNumber,
                Email = Email,
                Notes = Notes
            };
        }
    }
}
