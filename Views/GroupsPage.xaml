<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WhatsBroadcasterPro.Views.GroupsPage"
             Title="إدارة المجموعات">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" BackgroundColor="#28A745" Padding="15">
            <Grid ColumnDefinitions="*,Auto">
                <Label Grid.Column="0" 
                       Text="إدارة مجموعات الأرقام" 
                       FontSize="18" 
                       FontAttributes="Bold" 
                       TextColor="White" 
                       VerticalOptions="Center" />
                
                <Button Grid.Column="1" 
                        Text="➕ إضافة مجموعة" 
                        BackgroundColor="DarkGreen" 
                        TextColor="White" 
                        FontSize="12"
                        Padding="10,5"
                        Clicked="OnAddGroupClicked" />
            </Grid>
        </Border>

        <!-- Content -->
        <ScrollView Grid.Row="1" Padding="15">
            <StackLayout Spacing="15">
                
                <!-- Groups List -->
                <CollectionView x:Name="GroupsCollectionView">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Border BackgroundColor="White" 
                                    Stroke="LightGray" 
                                    StrokeThickness="1" 
                                    Padding="20" 
                                    Margin="0,5">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="10" />
                                </Border.StrokeShape>
                                
                                <Grid ColumnDefinitions="Auto,*,Auto">
                                    <!-- Color Indicator -->
                                    <Border Grid.Column="0" 
                                            BackgroundColor="{Binding Color}" 
                                            WidthRequest="20" 
                                            HeightRequest="20" 
                                            VerticalOptions="Center"
                                            Margin="0,0,15,0">
                                        <Border.StrokeShape>
                                            <RoundRectangle CornerRadius="10" />
                                        </Border.StrokeShape>
                                    </Border>
                                    
                                    <!-- Group Info -->
                                    <StackLayout Grid.Column="1" VerticalOptions="Center">
                                        <Label Text="{Binding Name}" 
                                               FontSize="16" 
                                               FontAttributes="Bold" />
                                        <Label Text="{Binding Description}" 
                                               FontSize="12" 
                                               TextColor="Gray" />
                                        <Label Text="{Binding ContactCount, StringFormat='{0} رقم'}" 
                                               FontSize="12" 
                                               TextColor="Blue" />
                                    </StackLayout>
                                    
                                    <!-- Actions -->
                                    <StackLayout Grid.Column="2" 
                                                 Orientation="Horizontal" 
                                                 Spacing="5"
                                                 VerticalOptions="Center">
                                        <Button Text="👥" 
                                                BackgroundColor="Blue" 
                                                TextColor="White" 
                                                FontSize="12"
                                                Padding="8,4"
                                                CommandParameter="{Binding}"
                                                Clicked="OnViewContactsClicked" />
                                        
                                        <Button Text="✏️" 
                                                BackgroundColor="Orange" 
                                                TextColor="White" 
                                                FontSize="12"
                                                Padding="8,4"
                                                CommandParameter="{Binding}"
                                                Clicked="OnEditGroupClicked" />
                                        
                                        <Button Text="🗑️" 
                                                BackgroundColor="Red" 
                                                TextColor="White" 
                                                FontSize="12"
                                                Padding="8,4"
                                                CommandParameter="{Binding}"
                                                Clicked="OnDeleteGroupClicked" />
                                    </StackLayout>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

                <!-- Empty State -->
                <StackLayout x:Name="EmptyStateLayout" 
                             IsVisible="False"
                             VerticalOptions="Center" 
                             HorizontalOptions="Center"
                             Margin="0,50,0,0">
                    <Label Text="📁" FontSize="64" HorizontalOptions="Center" />
                    <Label Text="لا توجد مجموعات" 
                           FontSize="18" 
                           FontAttributes="Bold" 
                           HorizontalOptions="Center" 
                           Margin="0,10,0,5" />
                    <Label Text="ابدأ بإنشاء مجموعة جديدة لتنظيم أرقامك" 
                           FontSize="14" 
                           TextColor="Gray" 
                           HorizontalOptions="Center" 
                           Margin="0,0,0,20" />
                    <Button Text="إنشاء أول مجموعة" 
                            BackgroundColor="#28A745" 
                            TextColor="White" 
                            Clicked="OnAddGroupClicked" />
                </StackLayout>

            </StackLayout>
        </ScrollView>
    </Grid>

</ContentPage>
