using WhatsBroadcasterPro.ViewModels;

namespace WhatsBroadcasterPro.Views;

public partial class GroupsPage : ContentPage
{
    private readonly GroupsViewModel _viewModel;

    public GroupsPage(GroupsViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
        Loaded += OnPageLoaded;
    }

    private async void OnPageLoaded(object sender, EventArgs e)
    {
        await _viewModel.InitializeAsync();
        UpdateEmptyState();
    }

    private void UpdateEmptyState()
    {
        EmptyStateLayout.IsVisible = !_viewModel.HasGroups;
    }

    private async void OnAddGroupClicked(object sender, EventArgs e)
    {
        // TODO: Navigate to add group page or show popup
        await DisplayAlert("إضافة مجموعة", "سيتم تطوير هذه الميزة قريباً", "موافق");
    }

    private async void OnViewContactsClicked(object sender, EventArgs e)
    {
        if (sender is Button button && button.CommandParameter is Models.Group group)
        {
            // TODO: Navigate to contacts page filtered by group
            await DisplayAlert("عرض الأرقام", $"عرض أرقام مجموعة {group.Name}", "موافق");
        }
    }

    private async void OnEditGroupClicked(object sender, EventArgs e)
    {
        if (sender is Button button && button.CommandParameter is Models.Group group)
        {
            // TODO: Navigate to edit group page
            await DisplayAlert("تعديل المجموعة", $"تعديل {group.Name}", "موافق");
        }
    }

    private async void OnDeleteGroupClicked(object sender, EventArgs e)
    {
        if (sender is Button button && button.CommandParameter is Models.Group group)
        {
            var result = await DisplayAlert("تأكيد الحذف", $"هل تريد حذف مجموعة {group.Name}؟", "نعم", "لا");
            if (result)
            {
                await _viewModel.DeleteGroupCommand.ExecuteAsync(group.Id);
                UpdateEmptyState();
            }
        }
    }
}
