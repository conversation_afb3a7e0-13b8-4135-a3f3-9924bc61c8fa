using WhatsBroadcasterPro.ViewModels;
using WhatsBroadcasterPro.Services;

namespace WhatsBroadcasterPro.Views;

public partial class ContactsPage : ContentPage
{
    private readonly ContactsViewModel _viewModel;

    public ContactsPage(ContactsViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
        Loaded += OnPageLoaded;
    }

    private async void OnPageLoaded(object sender, EventArgs e)
    {
        await _viewModel.InitializeAsync();
    }

    private async void OnAddContactClicked(object sender, EventArgs e)
    {
        // TODO: Navigate to add contact page or show popup
        await DisplayAlert("إضافة رقم", "سيتم تطوير هذه الميزة قريباً", "موافق");
    }

    private async void OnImportContactsClicked(object sender, EventArgs e)
    {
        // TODO: Implement import functionality
        await DisplayAlert("استيراد الأرقام", "سيتم تطوير هذه الميزة قريباً", "موافق");
    }

    private async void OnExportContactsClicked(object sender, EventArgs e)
    {
        // TODO: Implement export functionality
        await DisplayAlert("تصدير الأرقام", "سيتم تطوير هذه الميزة قريباً", "موافق");
    }

    private void OnSearchTextChanged(object sender, TextChangedEventArgs e)
    {
        _viewModel.SearchText = e.NewTextValue ?? string.Empty;
        _viewModel.SearchContactsCommand.Execute(null);
    }

    private void OnGroupFilterChanged(object sender, EventArgs e)
    {
        if (sender is Picker picker && picker.SelectedItem is Models.Group selectedGroup)
        {
            _viewModel.SelectedGroup = selectedGroup;
            _viewModel.FilterByGroupCommand.Execute(null);
        }
    }

    private void OnContactSelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        UpdateSelectionUI();
    }

    private void OnSelectAllChanged(object sender, CheckedChangedEventArgs e)
    {
        foreach (var contact in _viewModel.Contacts)
        {
            contact.IsSelected = e.Value;
        }
        UpdateSelectionUI();
    }

    private void OnContactCheckChanged(object sender, CheckedChangedEventArgs e)
    {
        UpdateSelectionUI();
    }

    private void UpdateSelectionUI()
    {
        var selectedCount = _viewModel.GetSelectedContacts().Count;
        SelectedActionsLayout.IsVisible = selectedCount > 0;
        SelectedCountLabel.Text = $"{selectedCount} عنصر محدد";
    }

    private async void OnEditContactClicked(object sender, EventArgs e)
    {
        if (sender is Button button && button.CommandParameter is ViewModels.ContactViewModel contact)
        {
            // TODO: Navigate to edit contact page
            await DisplayAlert("تعديل الرقم", $"تعديل {contact.Name}", "موافق");
        }
    }

    private async void OnDeleteContactClicked(object sender, EventArgs e)
    {
        if (sender is Button button && button.CommandParameter is ViewModels.ContactViewModel contact)
        {
            var result = await DisplayAlert("تأكيد الحذف", $"هل تريد حذف {contact.Name}؟", "نعم", "لا");
            if (result)
            {
                await _viewModel.DeleteContactCommand.ExecuteAsync(contact.Id);
            }
        }
    }

    private async void OnAddToGroupClicked(object sender, EventArgs e)
    {
        // TODO: Implement add to group functionality
        await DisplayAlert("إضافة للمجموعة", "سيتم تطوير هذه الميزة قريباً", "موافق");
    }

    private async void OnDeleteSelectedClicked(object sender, EventArgs e)
    {
        var selectedContacts = _viewModel.GetSelectedContacts();
        if (selectedContacts.Count == 0) return;

        var result = await DisplayAlert("تأكيد الحذف", $"هل تريد حذف {selectedContacts.Count} رقم؟", "نعم", "لا");
        if (result)
        {
            foreach (var contact in selectedContacts)
            {
                await _viewModel.DeleteContactCommand.ExecuteAsync(contact.Id);
            }
        }
    }

    private void OnClearSelectionClicked(object sender, EventArgs e)
    {
        _viewModel.ClearSelection();
        UpdateSelectionUI();
    }
}
