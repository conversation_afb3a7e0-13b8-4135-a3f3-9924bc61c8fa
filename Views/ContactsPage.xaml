<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WhatsBroadcasterPro.Views.ContactsPage"
             Title="إدارة الأرقام">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <Border Grid.Row="0" BackgroundColor="#007ACC" Padding="15">
            <Grid ColumnDefinitions="*,Auto,Auto,Auto">
                <Label Grid.Column="0" 
                       Text="إدارة أرقام الواتساب" 
                       FontSize="18" 
                       FontAttributes="Bold" 
                       TextColor="White" 
                       VerticalOptions="Center" />
                
                <Button Grid.Column="1" 
                        Text="➕ إضافة رقم" 
                        BackgroundColor="Green" 
                        TextColor="White" 
                        FontSize="12"
                        Padding="10,5"
                        Margin="5,0"
                        Clicked="OnAddContactClicked" />
                
                <Button Grid.Column="2" 
                        Text="📁 استيراد" 
                        BackgroundColor="Orange" 
                        TextColor="White" 
                        FontSize="12"
                        Padding="10,5"
                        Margin="5,0"
                        Clicked="OnImportContactsClicked" />
                
                <Button Grid.Column="3" 
                        Text="💾 تصدير" 
                        BackgroundColor="Purple" 
                        TextColor="White" 
                        FontSize="12"
                        Padding="10,5"
                        Margin="5,0"
                        Clicked="OnExportContactsClicked" />
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Padding="15">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- Search and Filter -->
            <StackLayout Grid.Row="0" Orientation="Horizontal" Spacing="10" Margin="0,0,0,15">
                <SearchBar x:Name="ContactSearchBar" 
                           Placeholder="البحث في الأرقام..." 
                           HorizontalOptions="FillAndExpand"
                           TextChanged="OnSearchTextChanged" />
                
                <Picker x:Name="GroupFilterPicker" 
                        Title="تصفية حسب المجموعة"
                        WidthRequest="200"
                        SelectedIndexChanged="OnGroupFilterChanged" />
            </StackLayout>

            <!-- Contacts List -->
            <CollectionView x:Name="ContactsCollectionView" 
                            Grid.Row="1"
                            SelectionMode="Multiple"
                            SelectionChanged="OnContactSelectionChanged">
                
                <CollectionView.Header>
                    <Grid BackgroundColor="LightGray" Padding="15,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        
                        <CheckBox Grid.Column="0" x:Name="SelectAllCheckBox" CheckedChanged="OnSelectAllChanged" />
                        <Label Grid.Column="1" Text="الاسم" FontAttributes="Bold" VerticalOptions="Center" />
                        <Label Grid.Column="2" Text="رقم الهاتف" FontAttributes="Bold" VerticalOptions="Center" />
                        <Label Grid.Column="3" Text="المجموعات" FontAttributes="Bold" VerticalOptions="Center" />
                        <Label Grid.Column="4" Text="الإجراءات" FontAttributes="Bold" VerticalOptions="Center" />
                    </Grid>
                </CollectionView.Header>

                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Border BackgroundColor="White" 
                                Stroke="LightGray" 
                                StrokeThickness="1" 
                                Padding="15,10" 
                                Margin="0,2">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="5" />
                            </Border.StrokeShape>
                            
                            <Grid ColumnDefinitions="Auto,*,*,*,Auto">
                                <CheckBox Grid.Column="0" 
                                          IsChecked="{Binding IsSelected}" 
                                          CheckedChanged="OnContactCheckChanged" />
                                
                                <StackLayout Grid.Column="1" VerticalOptions="Center">
                                    <Label Text="{Binding Name}" FontSize="14" FontAttributes="Bold" />
                                    <Label Text="{Binding Email}" FontSize="12" TextColor="Gray" />
                                </StackLayout>
                                
                                <Label Grid.Column="2" 
                                       Text="{Binding PhoneNumber}" 
                                       FontSize="14" 
                                       VerticalOptions="Center" />
                                
                                <StackLayout Grid.Column="3" VerticalOptions="Center">
                                    <Label Text="{Binding GroupNames}" FontSize="12" TextColor="Blue" />
                                </StackLayout>
                                
                                <StackLayout Grid.Column="4" Orientation="Horizontal" Spacing="5">
                                    <Button Text="✏️" 
                                            BackgroundColor="Blue" 
                                            TextColor="White" 
                                            FontSize="12"
                                            Padding="8,4"
                                            CommandParameter="{Binding}"
                                            Clicked="OnEditContactClicked" />
                                    
                                    <Button Text="🗑️" 
                                            BackgroundColor="Red" 
                                            TextColor="White" 
                                            FontSize="12"
                                            Padding="8,4"
                                            CommandParameter="{Binding}"
                                            Clicked="OnDeleteContactClicked" />
                                </StackLayout>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>

            <!-- Empty State -->
            <StackLayout x:Name="EmptyStateLayout" 
                         Grid.Row="1"
                         IsVisible="False"
                         VerticalOptions="Center" 
                         HorizontalOptions="Center">
                <Label Text="📱" FontSize="64" HorizontalOptions="Center" />
                <Label Text="لا توجد أرقام محفوظة" 
                       FontSize="18" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" 
                       Margin="0,10,0,5" />
                <Label Text="ابدأ بإضافة أرقام جديدة أو استيراد قائمة موجودة" 
                       FontSize="14" 
                       TextColor="Gray" 
                       HorizontalOptions="Center" 
                       Margin="0,0,0,20" />
                <Button Text="إضافة أول رقم" 
                        BackgroundColor="#007ACC" 
                        TextColor="White" 
                        Clicked="OnAddContactClicked" />
            </StackLayout>
        </Grid>

        <!-- Floating Action Menu -->
        <StackLayout x:Name="SelectedActionsLayout" 
                     Grid.Row="1"
                     IsVisible="False"
                     BackgroundColor="Black"
                     Opacity="0.9"
                     Padding="20"
                     VerticalOptions="End">
            <Label x:Name="SelectedCountLabel" 
                   Text="0 عنصر محدد" 
                   TextColor="White" 
                   FontSize="16" 
                   HorizontalOptions="Center" 
                   Margin="0,0,0,10" />
            
            <StackLayout Orientation="Horizontal" 
                         HorizontalOptions="Center" 
                         Spacing="15">
                <Button Text="إضافة للمجموعة" 
                        BackgroundColor="Green" 
                        TextColor="White" 
                        Clicked="OnAddToGroupClicked" />
                
                <Button Text="حذف المحدد" 
                        BackgroundColor="Red" 
                        TextColor="White" 
                        Clicked="OnDeleteSelectedClicked" />
                
                <Button Text="إلغاء التحديد" 
                        BackgroundColor="Gray" 
                        TextColor="White" 
                        Clicked="OnClearSelectionClicked" />
            </StackLayout>
        </StackLayout>
    </Grid>

</ContentPage>
