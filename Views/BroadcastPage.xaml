<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="WhatsBroadcasterPro.Views.BroadcastPage"
             Title="الإرسال الجماعي">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" BackgroundColor="#DC3545" Padding="15">
            <Label Text="إرسال رسائل جماعية" 
                   FontSize="18" 
                   FontAttributes="Bold" 
                   TextColor="White" 
                   HorizontalOptions="Center" />
        </Border>

        <!-- Content -->
        <ScrollView Grid.Row="1" Padding="20">
            <StackLayout Spacing="20">
                
                <!-- Group Selection -->
                <Border BackgroundColor="LightBlue" 
                        Stroke="DodgerBlue" 
                        StrokeThickness="2" 
                        Padding="15">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="10" />
                    </Border.StrokeShape>
                    
                    <StackLayout Spacing="10">
                        <Label Text="1. اختيار المجموعة" 
                               FontSize="16" 
                               FontAttributes="Bold" />
                        
                        <Picker x:Name="GroupPicker" 
                                Title="اختر المجموعة"
                                SelectedIndexChanged="OnGroupSelectionChanged" />
                        
                        <Label x:Name="SelectedGroupInfo" 
                               Text="لم يتم اختيار مجموعة" 
                               FontSize="12" 
                               TextColor="Gray" />
                    </StackLayout>
                </Border>

                <!-- Message Composition -->
                <Border BackgroundColor="LightGreen" 
                        Stroke="Green" 
                        StrokeThickness="2" 
                        Padding="15">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="10" />
                    </Border.StrokeShape>
                    
                    <StackLayout Spacing="10">
                        <Label Text="2. كتابة الرسالة" 
                               FontSize="16" 
                               FontAttributes="Bold" />
                        
                        <Editor x:Name="MessageEditor" 
                                Placeholder="اكتب رسالتك هنا..."
                                HeightRequest="120"
                                TextChanged="OnMessageTextChanged" />
                        
                        <Label x:Name="CharacterCountLabel" 
                               Text="0 حرف" 
                               FontSize="12" 
                               TextColor="Gray" 
                               HorizontalOptions="End" />
                        
                        <!-- Media Attachment -->
                        <StackLayout Orientation="Horizontal" Spacing="10">
                            <Button Text="📎 إرفاق ملف" 
                                    BackgroundColor="Orange" 
                                    TextColor="White" 
                                    FontSize="12"
                                    Clicked="OnAttachFileClicked" />
                            
                            <Label x:Name="AttachedFileLabel" 
                                   Text="لا يوجد ملف مرفق" 
                                   FontSize="12" 
                                   TextColor="Gray" 
                                   VerticalOptions="Center" />
                        </StackLayout>
                    </StackLayout>
                </Border>

                <!-- Scheduling Options -->
                <Border BackgroundColor="LightYellow" 
                        Stroke="Orange" 
                        StrokeThickness="2" 
                        Padding="15">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="10" />
                    </Border.StrokeShape>
                    
                    <StackLayout Spacing="10">
                        <Label Text="3. خيارات الإرسال" 
                               FontSize="16" 
                               FontAttributes="Bold" />
                        
                        <StackLayout Orientation="Horizontal" Spacing="10">
                            <RadioButton x:Name="SendNowRadio" 
                                         Content="إرسال فوري" 
                                         IsChecked="True"
                                         CheckedChanged="OnSendOptionChanged" />
                            
                            <RadioButton x:Name="ScheduleRadio" 
                                         Content="جدولة الإرسال"
                                         CheckedChanged="OnSendOptionChanged" />
                        </StackLayout>
                        
                        <StackLayout x:Name="ScheduleLayout" 
                                     IsVisible="False" 
                                     Spacing="10">
                            <DatePicker x:Name="ScheduleDatePicker" />
                            <TimePicker x:Name="ScheduleTimePicker" />
                        </StackLayout>
                        
                        <!-- Delay Settings -->
                        <StackLayout Spacing="5">
                            <Label Text="تأخير بين الرسائل (ثواني):" FontSize="14" />
                            <StackLayout Orientation="Horizontal" Spacing="10">
                                <Slider x:Name="DelaySlider" 
                                        Minimum="3" 
                                        Maximum="30" 
                                        Value="5"
                                        ValueChanged="OnDelayChanged" />
                                <Label x:Name="DelayLabel" 
                                       Text="5 ثواني" 
                                       FontSize="12" 
                                       VerticalOptions="Center" />
                            </StackLayout>
                        </StackLayout>
                    </StackLayout>
                </Border>

                <!-- Preview and Send -->
                <Border BackgroundColor="LightCoral" 
                        Stroke="Red" 
                        StrokeThickness="2" 
                        Padding="15">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="10" />
                    </Border.StrokeShape>
                    
                    <StackLayout Spacing="15">
                        <Label Text="4. معاينة وإرسال" 
                               FontSize="16" 
                               FontAttributes="Bold" />
                        
                        <!-- Summary -->
                        <StackLayout x:Name="SummaryLayout" Spacing="5">
                            <Label x:Name="SummaryLabel" 
                                   Text="اختر مجموعة واكتب رسالة للمتابعة" 
                                   FontSize="12" 
                                   TextColor="Gray" />
                        </StackLayout>
                        
                        <!-- Action Buttons -->
                        <StackLayout Orientation="Horizontal" 
                                     HorizontalOptions="Center" 
                                     Spacing="15">
                            <Button x:Name="PreviewButton" 
                                    Text="👁️ معاينة" 
                                    BackgroundColor="Blue" 
                                    TextColor="White" 
                                    IsEnabled="False"
                                    Clicked="OnPreviewClicked" />
                            
                            <Button x:Name="SendButton" 
                                    Text="📤 إرسال" 
                                    BackgroundColor="Red" 
                                    TextColor="White" 
                                    IsEnabled="False"
                                    Clicked="OnSendClicked" />
                        </StackLayout>
                    </StackLayout>
                </Border>

                <!-- Progress Section -->
                <Border x:Name="ProgressSection" 
                        BackgroundColor="LightGray" 
                        Stroke="Gray" 
                        StrokeThickness="2" 
                        Padding="15"
                        IsVisible="False">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="10" />
                    </Border.StrokeShape>
                    
                    <StackLayout Spacing="10">
                        <Label Text="حالة الإرسال" 
                               FontSize="16" 
                               FontAttributes="Bold" />
                        
                        <ProgressBar x:Name="SendProgressBar" 
                                     Progress="0" 
                                     ProgressColor="Green" />
                        
                        <Label x:Name="ProgressLabel" 
                               Text="جاري الإرسال..." 
                               FontSize="12" 
                               HorizontalOptions="Center" />
                        
                        <CollectionView x:Name="SendStatusCollectionView" 
                                        HeightRequest="200">
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <Grid ColumnDefinitions="*,Auto" Padding="5">
                                        <Label Grid.Column="0" 
                                               Text="{Binding ContactName}" 
                                               FontSize="12" />
                                        <Label Grid.Column="1" 
                                               Text="{Binding Status}" 
                                               FontSize="12" 
                                               TextColor="{Binding StatusColor}" />
                                    </Grid>
                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>
                    </StackLayout>
                </Border>

            </StackLayout>
        </ScrollView>
    </Grid>

</ContentPage>
