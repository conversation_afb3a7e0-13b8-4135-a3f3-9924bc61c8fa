using WhatsBroadcasterPro.ViewModels;

namespace WhatsBroadcasterPro.Views;

public partial class BroadcastPage : ContentPage
{
    private readonly BroadcastViewModel _viewModel;

    public BroadcastPage(BroadcastViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = _viewModel;
        Loaded += OnPageLoaded;
    }

    private async void OnPageLoaded(object sender, EventArgs e)
    {
        await _viewModel.InitializeAsync();
        UpdateUI();
    }

    private void OnGroupSelectionChanged(object sender, EventArgs e)
    {
        if (sender is Picker picker && picker.SelectedItem is Models.Group selectedGroup)
        {
            _viewModel.SelectedGroup = selectedGroup;
            UpdateSelectedGroupInfo();
            UpdateUI();
        }
    }

    private void UpdateSelectedGroupInfo()
    {
        if (_viewModel.SelectedGroup != null)
        {
            SelectedGroupInfo.Text = $"المجموعة: {_viewModel.SelectedGroup.Name} - {_viewModel.SelectedGroup.ContactGroups?.Count ?? 0} رقم";
        }
        else
        {
            SelectedGroupInfo.Text = "لم يتم اختيار مجموعة";
        }
    }

    private void OnMessageTextChanged(object sender, TextChangedEventArgs e)
    {
        _viewModel.MessageContent = e.NewTextValue ?? string.Empty;
        CharacterCountLabel.Text = $"{_viewModel.MessageContent.Length} حرف";
        UpdateUI();
    }

    private async void OnAttachFileClicked(object sender, EventArgs e)
    {
        // TODO: Implement file picker
        await DisplayAlert("إرفاق ملف", "سيتم تطوير هذه الميزة قريباً", "موافق");
    }

    private void OnSendOptionChanged(object sender, CheckedChangedEventArgs e)
    {
        if (sender is RadioButton radioButton && e.Value)
        {
            ScheduleLayout.IsVisible = radioButton == ScheduleRadio;
            _viewModel.IsScheduled = radioButton == ScheduleRadio;
        }
    }

    private void OnDelayChanged(object sender, ValueChangedEventArgs e)
    {
        _viewModel.DelayBetweenMessages = (int)e.NewValue;
        DelayLabel.Text = $"{(int)e.NewValue} ثواني";
    }

    private void UpdateUI()
    {
        var canSend = _viewModel.SelectedGroup != null && 
                      !string.IsNullOrWhiteSpace(_viewModel.MessageContent);

        PreviewButton.IsEnabled = canSend;
        SendButton.IsEnabled = canSend;

        UpdateSummary();
    }

    private void UpdateSummary()
    {
        if (_viewModel.SelectedGroup == null || string.IsNullOrWhiteSpace(_viewModel.MessageContent))
        {
            SummaryLabel.Text = "اختر مجموعة واكتب رسالة للمتابعة";
            return;
        }

        var contactCount = _viewModel.SelectedGroup.ContactGroups?.Count ?? 0;
        var messageLength = _viewModel.MessageContent.Length;
        var estimatedTime = contactCount * _viewModel.DelayBetweenMessages;

        SummaryLabel.Text = $"سيتم إرسال رسالة ({messageLength} حرف) إلى {contactCount} رقم\n" +
                           $"الوقت المتوقع: {estimatedTime / 60} دقيقة و {estimatedTime % 60} ثانية";
    }

    private async void OnPreviewClicked(object sender, EventArgs e)
    {
        if (_viewModel.SelectedGroup == null) return;

        var contactCount = _viewModel.SelectedGroup.ContactGroups?.Count ?? 0;
        var preview = $"المجموعة: {_viewModel.SelectedGroup.Name}\n" +
                     $"عدد الأرقام: {contactCount}\n" +
                     $"الرسالة: {_viewModel.MessageContent}\n" +
                     $"التأخير: {_viewModel.DelayBetweenMessages} ثانية";

        await DisplayAlert("معاينة الإرسال", preview, "موافق");
    }

    private async void OnSendClicked(object sender, EventArgs e)
    {
        if (_viewModel.SelectedGroup == null || string.IsNullOrWhiteSpace(_viewModel.MessageContent))
            return;

        var result = await DisplayAlert("تأكيد الإرسال", 
            "هل أنت متأكد من إرسال الرسالة؟", "نعم", "لا");

        if (result)
        {
            await StartSending();
        }
    }

    private async Task StartSending()
    {
        try
        {
            ProgressSection.IsVisible = true;
            SendButton.IsEnabled = false;
            SendButton.Text = "جاري الإرسال...";

            await _viewModel.SendBroadcastMessageCommand.ExecuteAsync(null);
        }
        catch (Exception ex)
        {
            await DisplayAlert("خطأ", $"حدث خطأ أثناء الإرسال: {ex.Message}", "موافق");
        }
        finally
        {
            SendButton.IsEnabled = true;
            SendButton.Text = "📤 إرسال";
        }
    }
}
