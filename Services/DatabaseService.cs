using Microsoft.EntityFrameworkCore;
using WhatsBroadcasterPro.Data;
using WhatsBroadcasterPro.Models;

namespace WhatsBroadcasterPro.Services
{
    public interface IDatabaseService
    {
        Task InitializeAsync();
        Task<List<Contact>> GetContactsAsync();
        Task<List<Group>> GetGroupsAsync();
        Task<Contact> AddContactAsync(Contact contact);
        Task<Group> AddGroupAsync(Group group);
        Task<bool> DeleteContactAsync(int contactId);
        Task<bool> DeleteGroupAsync(int groupId);
        Task<Contact> UpdateContactAsync(Contact contact);
        Task<Group> UpdateGroupAsync(Group group);
        Task<List<Contact>> GetContactsByGroupAsync(int groupId);
        Task<bool> AddContactToGroupAsync(int contactId, int groupId);
        Task<bool> RemoveContactFromGroupAsync(int contactId, int groupId);
        Task<BroadcastMessage> SaveBroadcastMessageAsync(BroadcastMessage message);
        Task<List<BroadcastMessage>> GetBroadcastMessagesAsync();
        Task<List<MessageLog>> GetMessageLogsAsync(int broadcastMessageId);
        Task UpdateMessageLogAsync(MessageLog messageLog);
    }

    public class DatabaseService : IDatabaseService
    {
        private readonly WhatsAppDbContext _context;

        public DatabaseService()
        {
            _context = new WhatsAppDbContext();
        }

        public async Task InitializeAsync()
        {
            await _context.Database.EnsureCreatedAsync();
        }

        public async Task<List<Contact>> GetContactsAsync()
        {
            return await _context.Contacts
                .Include(c => c.ContactGroups)
                .ThenInclude(cg => cg.Group)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<List<Group>> GetGroupsAsync()
        {
            return await _context.Groups
                .Include(g => g.ContactGroups)
                .ThenInclude(cg => cg.Contact)
                .OrderBy(g => g.Name)
                .ToListAsync();
        }

        public async Task<Contact> AddContactAsync(Contact contact)
        {
            _context.Contacts.Add(contact);
            await _context.SaveChangesAsync();
            return contact;
        }

        public async Task<Group> AddGroupAsync(Group group)
        {
            _context.Groups.Add(group);
            await _context.SaveChangesAsync();
            return group;
        }

        public async Task<bool> DeleteContactAsync(int contactId)
        {
            var contact = await _context.Contacts.FindAsync(contactId);
            if (contact == null) return false;

            _context.Contacts.Remove(contact);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteGroupAsync(int groupId)
        {
            var group = await _context.Groups.FindAsync(groupId);
            if (group == null) return false;

            _context.Groups.Remove(group);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<Contact> UpdateContactAsync(Contact contact)
        {
            contact.UpdatedAt = DateTime.Now;
            _context.Contacts.Update(contact);
            await _context.SaveChangesAsync();
            return contact;
        }

        public async Task<Group> UpdateGroupAsync(Group group)
        {
            group.UpdatedAt = DateTime.Now;
            _context.Groups.Update(group);
            await _context.SaveChangesAsync();
            return group;
        }

        public async Task<List<Contact>> GetContactsByGroupAsync(int groupId)
        {
            return await _context.ContactGroups
                .Where(cg => cg.GroupId == groupId)
                .Include(cg => cg.Contact)
                .Select(cg => cg.Contact)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<bool> AddContactToGroupAsync(int contactId, int groupId)
        {
            var existingRelation = await _context.ContactGroups
                .FirstOrDefaultAsync(cg => cg.ContactId == contactId && cg.GroupId == groupId);

            if (existingRelation != null) return false;

            var contactGroup = new ContactGroup
            {
                ContactId = contactId,
                GroupId = groupId
            };

            _context.ContactGroups.Add(contactGroup);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> RemoveContactFromGroupAsync(int contactId, int groupId)
        {
            var contactGroup = await _context.ContactGroups
                .FirstOrDefaultAsync(cg => cg.ContactId == contactId && cg.GroupId == groupId);

            if (contactGroup == null) return false;

            _context.ContactGroups.Remove(contactGroup);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<BroadcastMessage> SaveBroadcastMessageAsync(BroadcastMessage message)
        {
            _context.BroadcastMessages.Add(message);
            await _context.SaveChangesAsync();
            return message;
        }

        public async Task<List<BroadcastMessage>> GetBroadcastMessagesAsync()
        {
            return await _context.BroadcastMessages
                .Include(bm => bm.Group)
                .Include(bm => bm.MessageLogs)
                .OrderByDescending(bm => bm.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<MessageLog>> GetMessageLogsAsync(int broadcastMessageId)
        {
            return await _context.MessageLogs
                .Where(ml => ml.BroadcastMessageId == broadcastMessageId)
                .Include(ml => ml.Contact)
                .OrderBy(ml => ml.Contact.Name)
                .ToListAsync();
        }

        public async Task UpdateMessageLogAsync(MessageLog messageLog)
        {
            _context.MessageLogs.Update(messageLog);
            await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
