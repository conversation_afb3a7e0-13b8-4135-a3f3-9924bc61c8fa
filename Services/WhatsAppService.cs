using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;
using OpenQA.Selenium.Support.UI;
using WhatsBroadcasterPro.Models;

namespace WhatsBroadcasterPro.Services
{
    public interface IWhatsAppService
    {
        bool IsConnected { get; }
        Task ConnectAsync();
        Task DisconnectAsync();
        Task<bool> SendMessageAsync(string phoneNumber, string message);
        Task<bool> SendMessageWithMediaAsync(string phoneNumber, string message, string mediaPath);
        Task<List<bool>> SendBroadcastMessageAsync(List<Contact> contacts, string message, string? mediaPath = null);
        event EventHandler<string> ConnectionStatusChanged;
        event EventHandler<MessageSentEventArgs> MessageSent;
    }

    public class MessageSentEventArgs : EventArgs
    {
        public string PhoneNumber { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
    }

    public class WhatsAppService : IWhatsAppService
    {
        private IWebDriver? _driver;
        private WebDriverWait? _wait;
        private bool _isConnected = false;

        public bool IsConnected => _isConnected;

        public event EventHandler<string>? ConnectionStatusChanged;
        public event EventHandler<MessageSentEventArgs>? MessageSent;

        public async Task ConnectAsync()
        {
            try
            {
                // Setup Chrome options
                var options = new ChromeOptions();
                options.AddArgument("--user-data-dir=" + Path.Combine(FileSystem.AppDataDirectory, "chrome_profile"));
                options.AddArgument("--no-sandbox");
                options.AddArgument("--disable-dev-shm-usage");
                
                // Initialize Chrome driver
                _driver = new ChromeDriver(options);
                _wait = new WebDriverWait(_driver, TimeSpan.FromSeconds(30));

                // Navigate to WhatsApp Web
                _driver.Navigate().GoToUrl("https://web.whatsapp.com");

                // Wait for QR code or main interface
                await WaitForConnectionAsync();

                _isConnected = true;
                ConnectionStatusChanged?.Invoke(this, "متصل");
            }
            catch (Exception ex)
            {
                _isConnected = false;
                ConnectionStatusChanged?.Invoke(this, $"خطأ في الاتصال: {ex.Message}");
                throw;
            }
        }

        private async Task WaitForConnectionAsync()
        {
            var timeout = TimeSpan.FromMinutes(5); // 5 minutes timeout for QR scan
            var startTime = DateTime.Now;

            while (DateTime.Now - startTime < timeout)
            {
                try
                {
                    // Check if we're logged in by looking for the main chat interface
                    var chatElements = _driver?.FindElements(By.CssSelector("[data-testid='chat-list']"));
                    if (chatElements?.Count > 0)
                    {
                        return; // Successfully connected
                    }

                    // Check if QR code is still visible
                    var qrElements = _driver?.FindElements(By.CssSelector("[data-testid='qr-code']"));
                    if (qrElements?.Count > 0)
                    {
                        ConnectionStatusChanged?.Invoke(this, "في انتظار مسح رمز QR");
                    }

                    await Task.Delay(2000); // Wait 2 seconds before checking again
                }
                catch
                {
                    await Task.Delay(2000);
                }
            }

            throw new TimeoutException("انتهت مهلة انتظار الاتصال. يرجى المحاولة مرة أخرى.");
        }

        public async Task DisconnectAsync()
        {
            try
            {
                _driver?.Quit();
                _driver?.Dispose();
                _driver = null;
                _wait = null;
                _isConnected = false;
                ConnectionStatusChanged?.Invoke(this, "غير متصل");
            }
            catch (Exception ex)
            {
                ConnectionStatusChanged?.Invoke(this, $"خطأ في قطع الاتصال: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> SendMessageAsync(string phoneNumber, string message)
        {
            if (!_isConnected || _driver == null || _wait == null)
            {
                MessageSent?.Invoke(this, new MessageSentEventArgs 
                { 
                    PhoneNumber = phoneNumber, 
                    Success = false, 
                    ErrorMessage = "غير متصل بواتساب" 
                });
                return false;
            }

            try
            {
                // Format phone number (remove any non-digit characters except +)
                var formattedNumber = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");
                if (!formattedNumber.StartsWith("+"))
                {
                    formattedNumber = "+" + formattedNumber;
                }

                // Navigate to chat with the contact
                var chatUrl = $"https://web.whatsapp.com/send?phone={formattedNumber.Replace("+", "")}&text={Uri.EscapeDataString(message)}";
                _driver.Navigate().GoToUrl(chatUrl);

                // Wait for the page to load
                await Task.Delay(3000);

                // Wait for and click the send button
                var sendButton = _wait.Until(driver => 
                    driver.FindElement(By.CssSelector("[data-testid='send']")));
                
                sendButton.Click();

                // Wait a bit to ensure message is sent
                await Task.Delay(2000);

                MessageSent?.Invoke(this, new MessageSentEventArgs 
                { 
                    PhoneNumber = phoneNumber, 
                    Success = true 
                });

                return true;
            }
            catch (Exception ex)
            {
                MessageSent?.Invoke(this, new MessageSentEventArgs 
                { 
                    PhoneNumber = phoneNumber, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                });
                return false;
            }
        }

        public async Task<bool> SendMessageWithMediaAsync(string phoneNumber, string message, string mediaPath)
        {
            if (!_isConnected || _driver == null || _wait == null)
            {
                return false;
            }

            try
            {
                // First send the media, then the text message
                // This is a simplified implementation - in a real app, you'd need to handle file uploads
                return await SendMessageAsync(phoneNumber, message);
            }
            catch (Exception ex)
            {
                MessageSent?.Invoke(this, new MessageSentEventArgs 
                { 
                    PhoneNumber = phoneNumber, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                });
                return false;
            }
        }

        public async Task<List<bool>> SendBroadcastMessageAsync(List<Contact> contacts, string message, string? mediaPath = null)
        {
            var results = new List<bool>();

            foreach (var contact in contacts)
            {
                try
                {
                    bool success;
                    if (!string.IsNullOrEmpty(mediaPath))
                    {
                        success = await SendMessageWithMediaAsync(contact.PhoneNumber, message, mediaPath);
                    }
                    else
                    {
                        success = await SendMessageAsync(contact.PhoneNumber, message);
                    }

                    results.Add(success);

                    // Add delay between messages to avoid being blocked
                    await Task.Delay(Random.Shared.Next(3000, 8000)); // Random delay between 3-8 seconds
                }
                catch (Exception ex)
                {
                    results.Add(false);
                    MessageSent?.Invoke(this, new MessageSentEventArgs 
                    { 
                        PhoneNumber = contact.PhoneNumber, 
                        Success = false, 
                        ErrorMessage = ex.Message 
                    });
                }
            }

            return results;
        }

        public void Dispose()
        {
            try
            {
                _driver?.Quit();
                _driver?.Dispose();
            }
            catch
            {
                // Ignore disposal errors
            }
        }
    }
}
